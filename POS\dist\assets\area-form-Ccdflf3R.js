import{h as L,r as x,j as e,B as C}from"./index-D0Grd55b.js";import{i as B}from"./images-api-DS53jPy5.js";import{e as R,f as M,g as T}from"./use-areas-D9b36wbs.js";import"./date-range-picker-CruKYeHR.js";import{L as u}from"./form-Bk1C9kLO.js";import{I as p}from"./input-C-0UnKOB.js";import{S as E,a as G,b as V,c as H,d as J}from"./select-DBO-8fSu.js";import{X as z}from"./calendar-5lpy20z0.js";function $({areaId:N,storeUid:g}={}){var w;const v=L(),{createArea:F,isCreating:_}=R(),{updateArea:A,isUpdating:S}=M(),j=x.useRef(null),i=!!N&&!!g,{data:t,isLoading:f}=T(N||"",g||""),d=x.useMemo(()=>{try{const s=localStorage.getItem("pos_stores_data");if(s){const r=JSON.parse(s);return Array.isArray(r)?r.filter(n=>n.active===1):[]}return[]}catch{return[]}},[]),[a,c]=x.useState({areaName:"",storeUid:g||((w=d[0])==null?void 0:w.id)||"",description:"",sort:"",backgroundFile:null,backgroundPreview:""});x.useEffect(()=>{var s,r,n;if(t&&i&&d.length>0){const o=d.find(m=>m.id===t.store_uid),l={areaName:t.area_name||"",storeUid:o?o.id:((s=d[0])==null?void 0:s.id)||"",description:t.description||"",sort:((r=t.sort)==null?void 0:r.toString())||"",backgroundFile:null,backgroundPreview:((n=t.extra_data)==null?void 0:n.background)||""};c(l)}},[t,i,d]);const U=()=>{v({to:"/setting/area"})},D=async()=>{var s,r;if(b)if(i&&t){let n=((s=t.extra_data)==null?void 0:s.background)||"";n=a.backgroundPreview||((r=t.extra_data)==null?void 0:r.background)||"";const o={...t,area_name:a.areaName,description:a.description||void 0,sort:a.sort?parseInt(a.sort):t.sort,store_uid:a.storeUid,extra_data:{...t.extra_data,background:n}};A(o,{onSuccess:()=>{v({to:"/setting/area"})}})}else{const n={area_name:a.areaName,description:a.description||void 0,store_uid:a.storeUid,sort:a.sort?parseInt(a.sort):void 0,backgroundFile:a.backgroundFile};F(n,{onSuccess:()=>{v({to:"/setting/area"})}})}},b=a.areaName.trim()!==""&&a.storeUid!=="",k=_||S||f,I=async s=>{var n;const r=(n=s.target.files)==null?void 0:n[0];if(r){const o=new FileReader;o.onload=l=>{var h;const m=(h=l.target)==null?void 0:h.result;c(P=>({...P,backgroundFile:r,backgroundPreview:m}))},o.readAsDataURL(r);try{const m=(await B.uploadImage(r)).data.image_url;c(h=>({...h,backgroundFile:null,backgroundPreview:m}))}catch{}}},y=()=>{j.current&&j.current.click()};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(C,{variant:"ghost",size:"sm",onClick:U,className:"flex items-center",children:e.jsx(z,{className:"h-4 w-4"})}),e.jsx(C,{type:"button",disabled:k||!b,className:"min-w-[100px]",onClick:D,children:k?i?"Đang cập nhật...":"Đang tạo...":i?"Cập nhật":"Lưu"})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:i?"Chỉnh sửa khu vực":"Tạo khu vực"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:i&&f?e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-muted-foreground",children:"Đang tải dữ liệu khu vực..."})}):e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(u,{htmlFor:"area-name",className:"min-w-[200px] text-sm font-medium",children:["Tên khu vực ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(p,{id:"area-name",value:a.areaName,onChange:s=>c({...a,areaName:s.target.value}),placeholder:"Nhập tên khu vực",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(u,{className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(E,{value:a.storeUid,onValueChange:s=>c({...a,storeUid:s}),disabled:i,children:[e.jsx(G,{className:"flex-1",children:e.jsx(V,{placeholder:"Chọn cửa hàng"})}),e.jsx(H,{children:d.map(s=>e.jsx(J,{value:s.id,children:s.store_name},s.id))})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(u,{htmlFor:"area-description",className:"min-w-[200px] text-sm font-medium",children:"Mô tả"}),e.jsx(p,{id:"area-description",value:a.description,onChange:s=>c({...a,description:s.target.value}),placeholder:"Mô tả",className:"flex-1"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thứ tự hiển thị trong thiết bị bán hàng"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Khu vực có số nhỏ hơn sẽ được sắp xếp lên trên trong thiết bị bán hàng"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(u,{htmlFor:"area-sort",className:"min-w-[200px] text-sm font-medium",children:"Thứ tự hiển thị"}),e.jsx(p,{id:"area-sort",type:"number",value:a.sort,onChange:s=>c({...a,sort:s.target.value}),placeholder:"Nhập số thứ tự hiển thị",className:"flex-1"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Hình nền khu vực"}),e.jsx(p,{ref:j,type:"file",accept:"image/*",onChange:I,className:"hidden"}),e.jsx("div",{className:"flex-1",children:a.backgroundPreview?e.jsx("div",{children:e.jsx("img",{src:a.backgroundPreview,alt:"Background preview",className:"h-24 w-24 cursor-pointer rounded-md border object-cover transition-opacity hover:opacity-80",onClick:y,title:"Click để thay đổi ảnh"})}):e.jsx("div",{className:"rounded-md border-2 border-dashed border-gray-300 p-6 text-center transition-colors hover:border-gray-400",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"mx-auto h-12 w-12 text-gray-400",children:e.jsx("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),e.jsx("div",{children:e.jsx("button",{type:"button",onClick:y,className:"cursor-pointer text-sm font-medium text-blue-600 hover:text-blue-500",children:"Tải ảnh lên"})}),e.jsx("p",{className:"text-xs text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})})})]})]})})})]})}export{$ as A};
