import{r as a,ad as m,ae as f}from"./index-D0Grd55b.js";import{u as p}from"./user-CN3Oxfhq.js";const _=()=>{const i=a.useMemo(()=>{try{const e=localStorage.getItem(m);if(e){const s=JSON.parse(e);return Array.isArray(s)?s:[]}return[]}catch(e){return console.error("Error parsing pos_stores_data from localStorage:",e),[]}},[]),o=a.useMemo(()=>i.filter(e=>e.active===1),[i]),c=a.useMemo(()=>{const e=new Map;return o.forEach(s=>{const t=s.city_uid;e.has(t)||e.set(t,[]),e.get(t).push(s)}),e},[o]),n=e=>c.get(e)||[];return{stores:i,activeStores:o,storesByCity:c,getStoresByCity:n,getStoreById:e=>i.find(s=>s.id===e||s.store_id===e),getAllStoreIds:()=>o.map(e=>e.id||e.store_id),getSelectedStoreIds:(e,s)=>{const t=new Set;return e.forEach(r=>{n(r).forEach(l=>{t.add(l.id||l.store_id)})}),s.forEach(r=>{t.add(r)}),Array.from(t)},hasStores:o.length>0,isEmpty:o.length===0,totalStores:o.length}},I=()=>{const{currentBrandId:i}=p(),{stores:o}=_(),c=a.useMemo(()=>{try{const t=localStorage.getItem(f);if(t){const r=JSON.parse(t);return Array.isArray(r)?r:[]}return[]}catch(t){return console.error("Error parsing pos_cities_data from localStorage:",t),[]}},[]),n=a.useMemo(()=>i?c.filter(r=>{const y=r.id||r.city_id;return o.filter(S=>S.city_uid===y&&S.brand_uid===i).length>0}):c,[c,o,i]),u=a.useMemo(()=>n.filter(t=>t.active===1),[n]),d=a.useMemo(()=>[...u].sort((t,r)=>t.city_name.localeCompare(r.city_name,"vi")),[u]);return{cities:d,activeCities:u,filteredCities:n,currentBrandId:i,getCityById:t=>n.find(r=>r.id===t||r.city_id===t),getCityByName:t=>n.find(r=>r.city_name===t),getAllCityIds:()=>d.map(t=>t.id||t.city_id),hasCities:d.length>0,isEmpty:d.length===0,totalCities:d.length}};export{I as u};
