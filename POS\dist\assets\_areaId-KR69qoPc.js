import{aV as r,j as i}from"./index-DZ2N7iEN.js";import"./pos-api-PZMeNc3U.js";import"./vietqr-api-y35earyI.js";import"./user-B3TbfMPQ.js";import"./crm-api-DyqsDFNF.js";import"./header-DUOMiURq.js";import"./main-DEy6aM-s.js";import"./search-context-bUKMT4ET.js";import"./date-range-picker-extpnOqj.js";import"./form-CUvXDg29.js";import{C as m}from"./create-table-form-C59xK3KJ.js";import"./separator-Bnr4MN7f.js";import"./command-By9h7q-C.js";import"./calendar-CoHe9sRq.js";import"./createLucideIcon-2r9cCEY3.js";import"./index-CC24pdSB.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DDb7K8l1.js";import"./search-Cr9zvKK2.js";import"./createReactComponent-CQMxkbLi.js";import"./scroll-area-CoEnZUVR.js";import"./index-Csh0LmDQ.js";import"./select-B60YVMMU.js";import"./index-DIwSqRjm.js";import"./check-CP51yA4o.js";import"./IconChevronRight-Cvwf7k8w.js";import"./chevron-right-D-SI6PAy.js";import"./react-icons.esm-Ck8h5xF8.js";import"./popover-BK8t3srL.js";import"./use-areas-D-Twtt9k.js";import"./useQuery-CCx3k6lE.js";import"./utils-km2FGkQ4.js";import"./useMutation-zADCXE7W.js";import"./images-api-cr7Vek97.js";import"./query-keys-3lmd-xp6.js";import"./use-sales-channels-X2hXE3d5.js";import"./use-tables-9R_xosJa.js";import"./input-DpObCffE.js";import"./checkbox-Bqls14Dj.js";import"./collapsible-Dk62AUDY.js";import"./use-items-in-store-data-CqUd2DiP.js";import"./use-item-types-BdROFFdK.js";import"./use-item-classes-JUakwOO-.js";import"./use-units-CwHCNZjQ.js";import"./use-removed-items-DMGo-T5T.js";import"./items-in-store-api-DlayhMY_.js";import"./xlsx-DkH2s96g.js";import"./copy-CZwaU9IM.js";import"./plus-UCXJIK3L.js";import"./minus-DxdKRSWX.js";const ro=function(){const{areaId:o}=r.useParams(),{store_uid:t}=r.useSearch();return console.log("🔍 TableDetailPage rendered"),console.log("📍 Route params:",{areaId:o}),console.log("🔍 Route search:",{store_uid:t}),console.log("📊 Full URL:",window.location.href),i.jsx(m,{areaId:o,storeUid:t})};export{ro as component};
