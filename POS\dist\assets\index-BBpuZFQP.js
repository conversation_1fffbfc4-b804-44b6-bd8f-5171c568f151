import{aA as ve,e as Ne,j as e,B as v,a as be,r as N,l as ge,b as fe,c as ie,T as Te,o as le,p as oe,q as ce,h as Ce}from"./index-D0Grd55b.js";import{d as je,c as ye,e as De,f as Se,g as we}from"./use-membership-discounts-CcXyxBDW.js";import{D as Ee}from"./discount-toggle-button-C-9v5Jkz.js";import{v as H}from"./date-range-picker-CruKYeHR.js";import{L as _}from"./form-Bk1C9kLO.js";import{u as ke,a as _e,b as I,c as Pe,M as Me}from"./discount-form-context--wYYJ4hH.js";import{X as Fe,C as re}from"./calendar-5lpy20z0.js";import{I as $}from"./input-C-0UnKOB.js";import{S as J,a as Z,b as B,c as ee,d as se,C as P}from"./select-DBO-8fSu.js";import{R as Ae,a as de}from"./radio-group-CpEBFN0-.js";import{u as W}from"./useQuery-Ck3BpOfq.js";import{b as Q}from"./pos-api-B09qRspF.js";import{Q as X}from"./query-keys-3lmd-xp6.js";import{P as Re}from"./modal-hDollbt0.js";import{C as S}from"./checkbox-C_eqgJmW.js";import{C as M,a as F,b as A}from"./collapsible-MezBM5sx.js";import{C as R}from"./chevron-right-D2H_xC9V.js";import{P as me,a as ue,b as he}from"./popover-CMTiAV3j.js";import{C as pe}from"./calendar-Bxala8X3.js";import{f as U}from"./isSameMonth-C8JQo-AN.js";import{C as xe}from"./circle-help-CEmvpE3g.js";import{j as D}from"./date-utils-DBbLjCz0.js";function Ie(){const{handleBack:i,handleSave:u}=ke(),{isEditMode:o,isLoading:h,isFormValid:c}=_e(),{updateFormData:m}=I(),d=ve({strict:!1}),a=Ne({strict:!1}),n=d.id,x=a==null?void 0:a.store_uid,{data:s}=je(n||"",x||""),l=ye(),f=async()=>{if(!o||!s)return;const t=s.active===1?0:1,p={...s,active:t,is_update_same_discounts:!1};console.log("🔥 Membership Discount Toggle - API Data:",p);try{await l.mutateAsync(p),m({active:t})}catch(g){console.error("Failed to toggle discount status:",g)}};return e.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[e.jsx(v,{variant:"ghost",size:"sm",onClick:i,className:"flex items-center",children:e.jsx(Fe,{className:"h-4 w-4"})}),e.jsxs("h1",{className:"text-3xl font-bold",children:[o&&"Chỉnh sửa CTKM",!o&&"Tạo CTKM"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[o&&s&&e.jsx(Ee,{isActive:s.active===1,onToggle:f,disabled:h||l.isPending}),e.jsxs(v,{type:"button",disabled:h||!c,className:"min-w-[100px]",onClick:u,children:[h&&o&&"Đang cập nhật...",h&&!o&&"Đang tạo...",!h&&o&&"Cập nhật",!h&&!o&&"Lưu"]})]})]})}function Le({promotions:i,isLoading:u,disabled:o}){const{formData:h,updateFormData:c}=I(),m=d=>{if(d.startsWith("promotion-"))return;const a=i.find(n=>n.promotion_uid===d);console.log("🔥 Selected promotion:",a),c({promotionUid:d,promotionName:(a==null?void 0:a.promotion_name)||""})};return e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"min-w-[200px] text-sm font-medium",children:["CTKM ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(J,{value:h.promotionUid,onValueChange:m,disabled:o||!h.storeUid||u,children:[e.jsx(Z,{className:"flex-1",children:e.jsx(B,{placeholder:h.storeUid?u?"Đang tải...":i.length===0?"Không có CTKM":"Chọn CTKM":"Chọn cửa hàng trước"})}),e.jsx(ee,{children:i.length===0?e.jsx("div",{className:"px-2 py-1.5 text-sm text-gray-500",children:h.storeUid?"Không có CTKM":"Vui lòng chọn cửa hàng trước"}):i.filter(d=>d.promotion_uid).map(d=>e.jsx(se,{value:d.promotion_uid,children:d.promotion_name||"Chương trình không tên"},d.promotion_uid))})]})]})}function Ke(){const{formData:i,updateFormData:u}=I(),{isEditMode:o}=_e(),{promotions:h,isLoadingPromotions:c}=Pe(),{currentBrandStores:m}=be(),{data:d=[],isLoading:a}=De();return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin giảm giá"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(J,{value:i.storeUid,onValueChange:n=>{u({storeUid:n,promotionUid:"",promotionName:""})},disabled:o,children:[e.jsx(Z,{className:"flex-1",children:e.jsx(B,{placeholder:"Chọn cửa hàng"})}),e.jsx(ee,{children:m.map(n=>e.jsx(se,{value:n.id,children:n.store_name},n.id))})]})]}),e.jsx(Le,{promotions:h,isLoading:c,disabled:c}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"min-w-[200px] text-sm font-medium",children:["Hạng thành viên ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(J,{value:i.membershipTypeId,onValueChange:n=>u({membershipTypeId:n}),disabled:a,children:[e.jsx(Z,{className:"flex-1",children:e.jsx(B,{placeholder:"Chọn hạng thành viên"})}),e.jsx(ee,{children:Array.isArray(d)&&d.filter(n=>n.active===1).map(n=>e.jsx(se,{value:n.type_id,children:n.type_name},n.id))})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"min-w-[200px] text-sm font-medium",children:["Chọn loại giảm giá ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(Ae,{value:i.discountType,onValueChange:n=>u({discountType:n}),className:"flex gap-6",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(de,{value:"PERCENT",id:"percent"}),e.jsx(_,{htmlFor:"percent",children:"Phần trăm"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(de,{value:"AMOUNT",id:"amount"}),e.jsx(_,{htmlFor:"amount",children:"Số tiền"})]})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"max-w-[240px] min-w-[240px] text-sm font-medium",children:[i.discountType==="PERCENT"?"Phần trăm GG ngày thường":"Số tiền GG ngày thường"," ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx($,{type:"number",min:"0",max:i.discountType==="PERCENT"?"100":void 0,value:i.taDiscount||"",onChange:n=>{const x=Number(n.target.value),s=i.discountType==="PERCENT"&&x>100?100:x;u({taDiscount:s})},placeholder:"0",className:"max-w-[200px] min-w-[200px]"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"max-w-[240px] min-w-[240px] text-sm font-medium",children:[i.discountType==="PERCENT"?"Phần trăm GG ngày sinh nhật":"Số tiền GG ngày sinh nhật"," ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx($,{type:"number",min:"0",max:i.discountType==="PERCENT"?"100":void 0,value:i.birthTaDiscount||"",onChange:n=>{const x=Number(n.target.value),s=i.discountType==="PERCENT"&&x>100?100:x;u({birthTaDiscount:s})},placeholder:"0",className:"max-w-[200px] min-w-[200px]"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"max-w-[240px] min-w-[240px] text-sm font-medium",children:["Áp dụng trước, sau sinh nhật ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{type:"number",min:"0",value:i.birthTimeRangeApply||"",onChange:n=>{const x=Number(n.target.value);u({birthTimeRangeApply:x})},placeholder:"0",className:"max-w-[100px] min-w-[100px]"}),e.jsx("span",{className:"text-sm text-gray-600",children:"ngày và đúng ngày sinh nhật"})]})]})]})}function Oe({itemTypes:i,selectedItems:u,searchTerm:o,onItemToggle:h}){const[c,m]=N.useState(!1),[d,a]=N.useState(!1),n=(Array.isArray(i)?i:[]).filter(l=>{const f=l.item_type_name||l.name||"";return f.toLowerCase()!=="uncategory"&&f.toLowerCase().includes(o.toLowerCase())}),x=n.filter(l=>u.includes(l.item_type_id||l.id)),s=n.filter(l=>!u.includes(l.item_type_id||l.id));return e.jsxs("div",{className:"space-y-4",children:[e.jsxs(M,{open:!c,onOpenChange:l=>m(!l),children:[e.jsx(F,{asChild:!0,children:e.jsxs(v,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",x.length,")"]}),c&&e.jsx(R,{className:"h-4 w-4"}),!c&&e.jsx(P,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[x.map(l=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${l.active===0?"opacity-50":""}`,children:[e.jsx(S,{checked:!0,onCheckedChange:()=>h(l.item_type_id||l.id),disabled:l.active===0}),e.jsx("span",{className:"text-sm",children:l.item_type_name||l.name||"Không có tên"})]},l.id)),x.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn nhóm nào"})]})]}),e.jsxs(M,{open:!d,onOpenChange:l=>a(!l),children:[e.jsx(F,{asChild:!0,children:e.jsxs(v,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",s.length,")"]}),d&&e.jsx(R,{className:"h-4 w-4"}),!d&&e.jsx(P,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[s.map(l=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${l.active===0?"opacity-50":""}`,children:[e.jsx(S,{checked:!1,onCheckedChange:()=>h(l.item_type_id||l.id),disabled:l.active===0}),e.jsx("span",{className:"text-sm",children:l.item_type_name||l.name||"Không có tên"})]},l.id)),s.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có nhóm nào"})]})]})]})}function He({items:i,selectedItems:u,searchTerm:o,isLoading:h,onItemToggle:c}){const[m,d]=N.useState(!1),[a,n]=N.useState(!1),s=(Array.isArray(i)?i:[]).filter(t=>{const p=(t==null?void 0:t.item_name)||(t==null?void 0:t.name)||"";return t.active!==0&&p.toLowerCase().includes(o.toLowerCase())}).map(t=>({...t,name:t.item_name||t.name||"Không có tên"})),l=s.filter(t=>u.includes(t.item_id||t.id)),f=s.filter(t=>!u.includes(t.item_id||t.id));return h?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(M,{open:!m,onOpenChange:t=>d(!t),children:[e.jsx(F,{asChild:!0,children:e.jsxs(v,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",l.length,")"]}),m&&e.jsx(R,{className:"h-4 w-4"}),!m&&e.jsx(P,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[l.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(S,{checked:!0,onCheckedChange:()=>c(t.item_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),l.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn món ăn nào"})]})]}),e.jsxs(M,{open:!a,onOpenChange:t=>n(!t),children:[e.jsx(F,{asChild:!0,children:e.jsxs(v,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",f.length,")"]}),a&&e.jsx(R,{className:"h-4 w-4"}),!a&&e.jsx(P,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[f.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(S,{checked:!1,onCheckedChange:()=>c(t.item_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),f.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có món ăn nào"})]})]})]})}function Ue({packages:i,selectedItems:u,searchTerm:o,isLoading:h,onItemToggle:c}){const[m,d]=N.useState(!1),[a,n]=N.useState(!1),s=(Array.isArray(i)?i:[]).filter(t=>{const g=((t==null?void 0:t.package_name)||(t==null?void 0:t.name)||"").toLowerCase().includes(o.toLowerCase());return t.active!==0&&g}).map(t=>({...t,name:t.package_name||t.name||"Không có tên"})),l=s.filter(t=>u.includes(t.package_id||t.id)),f=s.filter(t=>!u.includes(t.package_id||t.id));return h?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(M,{open:!m,onOpenChange:t=>d(!t),children:[e.jsx(F,{asChild:!0,children:e.jsxs(v,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",l.length,")"]}),m&&e.jsx(R,{className:"h-4 w-4"}),!m&&e.jsx(P,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[l.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(S,{checked:!0,onCheckedChange:()=>c(t.package_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),l.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn combo nào"})]})]}),e.jsxs(M,{open:!a,onOpenChange:t=>n(!t),children:[e.jsx(F,{asChild:!0,children:e.jsxs(v,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",f.length,")"]}),a&&e.jsx(R,{className:"h-4 w-4"}),!a&&e.jsx(P,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[f.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(S,{checked:!1,onCheckedChange:()=>c(t.package_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),f.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có combo nào"})]})]})]})}function $e({open:i,onOpenChange:u,storeUid:o,onSave:h,initialApplyToAll:c=!1,initialActiveFilter:m=null,initialSelectedItems:d=[]}){const{selectedBrand:a}=ge(),{company:n}=fe(),[x,s]=N.useState(""),[l,f]=N.useState(!1),[t,p]=N.useState(null),[g,y]=N.useState([]),{data:b=[],isLoading:w}=W({queryKey:[X.ITEM_TYPES,n==null?void 0:n.id,a==null?void 0:a.id,o],queryFn:async()=>!(n!=null&&n.id)||!(a!=null&&a.id)||!o?[]:(await Q.get(`/mdata/v1/item-types?skip_limit=true&company_uid=${n.id}&brand_uid=${a.id}&store_uid=${o}`)).data.data||[],enabled:i&&!!(n!=null&&n.id)&&!!(a!=null&&a.id)&&!!o}),{data:G=[],isLoading:V}=W({queryKey:[X.ITEMS,n==null?void 0:n.id,a==null?void 0:a.id,o],queryFn:async()=>!(n!=null&&n.id)||!(a!=null&&a.id)||!o?[]:(await Q.get(`/mdata/v1/items?skip_limit=true&company_uid=${n.id}&brand_uid=${a.id}&list_store_uid=${o}`)).data.data||[],enabled:i&&!!(n!=null&&n.id)&&!!(a!=null&&a.id)&&!!o}),{data:q=[],isLoading:z}=W({queryKey:[X.PACKAGES,n==null?void 0:n.id,a==null?void 0:a.id,o],queryFn:async()=>!(n!=null&&n.id)||!(a!=null&&a.id)||!o?[]:(await Q.get(`/mdata/v1/packages?skip_limit=true&company_uid=${n.id}&brand_uid=${a.id}&store_uid=${o}`)).data.data||[],enabled:i&&!!(n!=null&&n.id)&&!!(a!=null&&a.id)&&!!o}),L=w||V||z;N.useEffect(()=>{i&&(s(""),f(c),p(m),y(d))},[i,c,m,d]);const Y=j=>{f(j),j&&p(null)},K=j=>{f(!1),p(T=>{const k=T===j?null:j;return k!==T&&y([]),k})},E=j=>{y(T=>T.includes(j)?T.filter(k=>k!==j):[...T,j])},r=()=>{u(!1)},C=()=>{l?h(["all"],!0,null):h(g,!1,t),u(!1)};return e.jsx(Re,{title:"Áp dụng cho",open:i,onOpenChange:u,onCancel:r,onConfirm:C,cancelText:"Hủy",confirmText:"Lưu",maxWidth:"sm:max-w-2xl",isLoading:L,confirmDisabled:!l&&g.length===0,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{id:"apply-all",checked:l,onCheckedChange:Y}),e.jsx(_,{htmlFor:"apply-all",className:"text-sm font-medium",children:"Áp dụng cho tất cả món và nhóm"})]}),e.jsx($,{placeholder:"Tìm kiếm",value:x,onChange:j=>s(j.target.value),className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{className:"text-sm font-medium",children:"Áp dụng cho"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{type:"button",variant:t==="groups"?"default":"outline",size:"sm",onClick:()=>K("groups"),children:"Nhóm"}),e.jsx(v,{type:"button",variant:t==="items"?"default":"outline",size:"sm",onClick:()=>K("items"),children:"Món ăn"}),e.jsx(v,{type:"button",variant:t==="packages"?"default":"outline",size:"sm",onClick:()=>K("packages"),children:"Combo"})]})]}),!l&&e.jsxs("div",{className:"space-y-2",children:[t==="groups"&&e.jsx(Oe,{itemTypes:b,selectedItems:g,searchTerm:x,onItemToggle:E}),t==="items"&&e.jsx(He,{items:G,selectedItems:g,searchTerm:x,isLoading:L,onItemToggle:E}),t==="packages"&&e.jsx(Ue,{packages:q,selectedItems:g,searchTerm:x,isLoading:L,onItemToggle:E})]})]})})}function Ge(){var t;const{formData:i,updateFormData:u}=I(),[o,h]=N.useState(!1),c=()=>{h(!0)},m=p=>({...i.filterState,is_all:p?1:0,is_type:0,is_item:0,is_combo:0,type_id:"",item_id:"",combo_id:""}),d=(p,g,y)=>{const b=y.join(",");switch(g){case"groups":p.is_type=1,p.type_id=b;break;case"items":p.is_item=1,p.item_id=b;break;case"packages":p.is_combo=1,p.combo_id=b;break}},a=(p,g,y)=>{const b=m(g);!g&&p.length>0&&y&&d(b,y,p),u({filterState:b})},n=((t=i.filterState)==null?void 0:t.is_all)===1,x={groups:{flag:"is_type",field:"type_id",label:"nhóm"},items:{flag:"is_item",field:"item_id",label:"món"},packages:{flag:"is_combo",field:"combo_id",label:"combo"}},s=()=>{const p=i.filterState;if(!p)return null;for(const[g,y]of Object.entries(x))if(p[y.flag]===1)return g;return null},l=()=>{const p=i.filterState,g=s();if(!p||!g)return[];const y=x[g],b=p[y.field];return b?b.split(",").filter(w=>w.trim()):[]},f=()=>{if(n)return"Áp dụng cho tất cả";const p=l();if(p.length>0){const g=s(),y=g?x[g].label:"";return`${p.length} ${y}`}return"Thêm"};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Tuỳ chỉnh"}),e.jsx("div",{className:"mb-4 text-sm text-gray-600",children:"Áp dụng giảm giá tự động cho các món hoặc nhóm món, combo cụ thể"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"min-w-[200px] text-sm font-medium",children:["Áp dụng cho ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(v,{type:"button",variant:"outline",onClick:c,disabled:!i.storeUid,className:"flex-1 justify-start",children:f()})]}),e.jsx($e,{open:o,onOpenChange:h,storeUid:i.storeUid,onSave:a,initialApplyToAll:n,initialActiveFilter:s(),initialSelectedItems:l()})]})}function Ve(){const{formData:i,updateFormData:u}=I(),o=new Date;o.setHours(0,0,0,0);const h=i.startDate?new Date(i.startDate):void 0,c=i.endDate?new Date(i.endDate):void 0,m=a=>{if(a){const n=U(a,"yyyy-MM-dd");u({startDate:n})}},d=a=>{if(a){const n=U(a,"yyyy-MM-dd");u({endDate:n})}};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Ngày áp dụng"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(_,{className:"min-w-[120px] text-sm font-medium",children:"Ngày bắt đầu"}),e.jsxs(me,{children:[e.jsx(ue,{asChild:!0,children:e.jsxs(v,{variant:"outline",className:ie("flex-1 justify-start text-left font-normal",!h&&"text-muted-foreground"),children:[e.jsx(pe,{className:"mr-2 h-4 w-4"}),h?U(h,"dd/MM/yyyy",{locale:H}):"Chọn ngày bắt đầu"]})}),e.jsx(he,{className:"w-auto p-0",align:"start",children:e.jsx(re,{mode:"single",selected:h,onSelect:m,disabled:a=>a>o,initialFocus:!0,locale:H})})]})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(_,{className:"min-w-[120px] text-sm font-medium",children:"Ngày kết thúc"}),e.jsxs(me,{children:[e.jsx(ue,{asChild:!0,children:e.jsxs(v,{variant:"outline",className:ie("flex-1 justify-start text-left font-normal",!c&&"text-muted-foreground"),children:[e.jsx(pe,{className:"mr-2 h-4 w-4"}),c?U(c,"dd/MM/yyyy",{locale:H}):"Chọn ngày kết thúc"]})}),e.jsx(he,{className:"w-auto p-0",align:"start",children:e.jsx(re,{mode:"single",selected:c,onSelect:d,disabled:a=>a<o,initialFocus:!0,locale:H})})]})]})]})]})}const qe=[{label:"T2",value:"0"},{label:"T3",value:"1"},{label:"T4",value:"2"},{label:"T5",value:"3"},{label:"T6",value:"4"},{label:"T7",value:"5"},{label:"CN",value:"6"}],ze=[{value:"0"},{value:"1"},{value:"2"},{value:"3"},{value:"4"},{value:"5"},{value:"6"},{value:"7"},{value:"8"},{value:"9"},{value:"10"},{value:"11"},{value:"12"},{value:"13"},{value:"14"},{value:"15"},{value:"16"},{value:"17"},{value:"18"},{value:"19"},{value:"20"},{value:"21"},{value:"22"},{value:"23"}];function Ye(){const{formData:i,updateFormData:u}=I(),o=c=>{const m=i.marketingDays||[],d=m.includes(c)?m.filter(a=>a!==c):[...m,c];u({marketingDays:d})},h=c=>{const m=i.marketingHours||[],d=m.includes(c)?m.filter(a=>a!==c):[...m,c];u({marketingHours:d})};return e.jsx(Te,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Khung thời gian áp dụng"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(_,{className:"text-sm font-medium text-gray-500",children:"Chọn ngày"}),e.jsxs(le,{children:[e.jsx(oe,{asChild:!0,children:e.jsx(xe,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(ce,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần"})})]})]}),e.jsx("div",{className:"flex gap-2",children:qe.map(c=>{var m;return e.jsx(v,{type:"button",variant:(m=i.marketingDays)!=null&&m.includes(c.value)?"default":"outline",size:"sm",onClick:()=>o(c.value),className:"flex-1",children:c.label},c.value)})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(_,{className:"text-sm font-medium text-gray-500",children:"Chọn giờ"}),e.jsxs(le,{children:[e.jsx(oe,{asChild:!0,children:e.jsx(xe,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(ce,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày"})})]})]}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:ze.map(c=>{var m;return e.jsxs(v,{type:"button",variant:(m=i.marketingHours)!=null&&m.includes(c.value)?"default":"outline",size:"sm",onClick:()=>h(c.value),className:"text-xs",children:[c.value,":00"]},c.value)})})]})]})})}function We({discountId:i,initialStoreUid:u,enablePromotionsFetch:o=!1}={}){const h=Ce(),{company:c}=fe(),{selectedBrand:m}=ge(),d=!!i;console.log("🔥 Membership Discount Edit Mode - API Call Params:",{discountId:i,initialStoreUid:u,isEditMode:d});const{data:a,isLoading:n}=je(i||"",u||""),x=(a==null?void 0:a.store_uid)||u||"",[s,l]=N.useState({storeUid:u||"",membershipTypeId:"",discountType:"PERCENT",taDiscount:0,otsDiscount:0,birthTaDiscount:0,birthOtsDiscount:0,birthTimeRangeApply:0,startDate:new Date().toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],marketingDays:[],marketingHours:[],promotionUid:"",promotionName:"",active:1,filterState:{is_all:0,is_item:0,is_type:0,type_id:"",item_id:"",is_combo:0,combo_id:""}}),f=d?x:s.storeUid;console.log("🔥 Membership Discount Promotions Fetch:",{isEditMode:d,storeUidForPromotions:x,formDataStoreUid:s.storeUid,promotionsStoreUid:f,enablePromotionsFetch:o});const{data:t=[],isLoading:p}=Se(f,{enabled:o&&!!f}),g=r=>{const C=new Date(r);return new Date(C.getTime()+7*60*60*1e3).toISOString().split("T")[0]},y=r=>new Date(r+"T00:00:00+07:00"),b=r=>{var te,ae,ne;const C=g(r.from_date),j=g(r.to_date),T=D.convertNumbersToDayStrings(D.convertBitFlagsToDays(r.time_sale_date_week)),k=D.convertNumbersToHourStrings(D.convertBitFlagsToHours(r.time_sale_hour_day)),O=r.discount_type==="PERCENT";return{storeUid:r.store_uid,membershipTypeId:r.membership_type_id||"",discountType:r.discount_type,taDiscount:O?(r.ta_discount||0)*100:r.ta_discount||0,otsDiscount:O?(r.ots_discount||0)*100:r.ots_discount||0,birthTaDiscount:O?(r.birth_ta_discount||0)*100:r.birth_ta_discount||0,birthOtsDiscount:O?(r.birth_ots_discount||0)*100:r.birth_ots_discount||0,birthTimeRangeApply:r.birth_time_range_apply||0,startDate:C,endDate:j,marketingDays:T,marketingHours:k,promotionUid:r.promotion_uid,promotionName:((te=r.promotion)==null?void 0:te.promotion_name)||"",active:r.active,filterState:{is_all:r.is_all,is_item:r.is_item,is_type:r.is_type,type_id:r.type_id,item_id:r.item_id,is_combo:(ae=r.extra_data)==null?void 0:ae.is_combo,combo_id:((ne=r.extra_data)==null?void 0:ne.combo_id)||""}}};N.useEffect(()=>{if(a&&d){console.log("🔥 Membership Discount Edit Mode - Raw API Data:",a);const r=b(a);console.log("🔥 Membership Discount Edit Mode - Transformed Form Data:",r),l(r)}},[a,d]);const{mutate:w,isPending:G}=ye({onSuccess:()=>{h({to:"/sale/discount/membership"})}}),{mutate:V,isPending:q}=we({onSuccess:()=>{h({to:"/sale/discount/membership"})}}),z=()=>{h({to:"/sale/discount/membership"})},L=()=>{const r=s.marketingDays.length>0?D.convertDayStringsToNumbers(s.marketingDays):[0,1,2,3,4,5,6],C=s.marketingHours.length>0?D.convertHourStringsToNumbers(s.marketingHours):D.getAllHours(),j=D.convertDaysToBitFlags(r),T=D.convertHoursToBitFlags(C);return d?a?{...a,membership_type_id:s.membershipTypeId,membership_type_name:s.membershipTypeId,birth_ta_discount:s.discountType==="PERCENT"?s.birthTaDiscount/100:s.birthTaDiscount,birth_ots_discount:s.discountType==="PERCENT"?s.birthOtsDiscount/100:s.birthOtsDiscount,birth_time_range_apply:s.birthTimeRangeApply,ta_discount:s.discountType==="PERCENT"?s.taDiscount/100:s.taDiscount,ots_discount:s.discountType==="PERCENT"?s.otsDiscount/100:s.otsDiscount,is_all:s.filterState.is_all,is_type:s.filterState.is_type,is_item:s.filterState.is_item,type_id:s.filterState.type_id,item_id:s.filterState.item_id,discount_type:s.discountType,from_date:y(s.startDate).getTime(),to_date:y(s.endDate).getTime(),time_sale_hour_day:T,time_sale_date_week:j,description:null,extra_data:{combo_id:s.filterState.combo_id,is_combo:s.filterState.is_combo},active:s.active,promotion_uid:s.promotionUid,store_uid:s.storeUid,is_update_same_discounts:!1}:{}:{discount_type:s.discountType,promotion_uid:s.promotionUid||"1576be99-992c-4085-a68c-105f7fbf0fff",type_id:s.filterState.type_id,item_id:s.filterState.item_id,birth_time_range_apply:s.birthTimeRangeApply,from_date:y(s.startDate).getTime(),to_date:y(s.endDate).getTime(),store_uid:s.storeUid,membership_type_id:s.membershipTypeId,time_sale_date_week:j,time_sale_hour_day:T,company_uid:(c==null?void 0:c.id)||"",brand_uid:(m==null?void 0:m.id)||"",membership_type_name:s.membershipTypeId,ta_discount:s.discountType==="PERCENT"?s.taDiscount/100:s.taDiscount,ots_discount:s.discountType==="PERCENT"?s.otsDiscount/100:s.otsDiscount,birth_ta_discount:s.discountType==="PERCENT"?s.birthTaDiscount/100:s.birthTaDiscount,birth_ots_discount:s.discountType==="PERCENT"?s.birthOtsDiscount/100:s.birthOtsDiscount,is_all:s.filterState.is_all,is_type:s.filterState.is_type,is_item:s.filterState.is_item,extra_data:{is_combo:s.filterState.is_combo,combo_id:s.filterState.combo_id}}},Y=async()=>{if(!E)return;const r=L();console.log("🔥 Membership Discount Save - API Data:",r),d?w(r):V(r)},K=r=>{l(C=>({...C,...r}))},E=s.storeUid!==""&&s.membershipTypeId!==""&&s.promotionUid!==""&&(s.discountType==="PERCENT",s.taDiscount>0||s.birthTaDiscount>0);return{formData:s,updateFormData:K,handleBack:z,handleSave:Y,isFormValid:E,isLoading:q||G||n,isEditMode:d,promotions:o?t:[],isLoadingPromotions:o?p:!1}}function fs({discountId:i,storeUid:u}={}){const o=We({discountId:i,initialStoreUid:u,enablePromotionsFetch:!0});return e.jsx(Me,{value:{...o,promotions:o.promotions||[],isLoadingPromotions:o.isLoadingPromotions||!1},children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Ie,{}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(Ke,{}),e.jsx(Ge,{}),e.jsx(Ve,{}),e.jsx(Ye,{})]})})})]})})}export{fs as M};
