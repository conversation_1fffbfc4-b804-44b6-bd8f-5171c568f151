import{j as e,B as T,h as k,r as x,a4 as h}from"./index-D0Grd55b.js";import{g as C}from"./error-utils-rXhAXMyo.js";import"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import{a as K,b as L,u as $}from"./use-item-classes-s7fFgXey.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import{H as A}from"./header-yApU5MZq.js";import{M as H}from"./main-Czv3HpP4.js";import{C as R}from"./index-AO0RdSTE.js";import"./date-range-picker-CruKYeHR.js";import"./form-Bk1C9kLO.js";import{P as B}from"./profile-dropdown-dGP8eyih.js";import{S as O,T as z}from"./search-BEMocVbv.js";import{I as Q}from"./input-C-0UnKOB.js";import{B as V}from"./badge-DoEZpO1Y.js";import{I as F}from"./IconTrash-avbXk0dh.js";import{u as U,e as X,f as I}from"./index-CwC79dMR.js";import{T as q,a as G,b as f,c as J,d as W,e as b}from"./table-CLNYB6yq.js";import{I as Y}from"./IconPlus-MF1uzRH5.js";import"./useQuery-Ck3BpOfq.js";import"./utils-km2FGkQ4.js";import"./useMutation-ATsU-ht7.js";import"./query-keys-3lmd-xp6.js";import"./separator-CiUiq7rT.js";import"./dialog-C8IVKkOo.js";import"./calendar-5lpy20z0.js";import"./createLucideIcon-DNzDbUBG.js";import"./index-CW7Xpojs.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-D2H_xC9V.js";import"./react-icons.esm-BOnXQgjl.js";import"./popover-CMTiAV3j.js";import"./select-DBO-8fSu.js";import"./index-CI2TkimM.js";import"./index-C-UyCxtf.js";import"./check-TFQPNqMS.js";import"./avatar-C7dnn6zI.js";import"./dropdown-menu-BvqrmFsX.js";import"./index-DxQNaO1C.js";import"./search-context-CjM0jrYw.js";import"./command-Bcq7GTcy.js";import"./search-C4dfJjaw.js";import"./createReactComponent-eJgt86Cn.js";import"./scroll-area-r2ikcXUQ.js";import"./IconChevronRight-DnVUSvDn.js";import"./IconSearch-ClUZlEwa.js";const Z=({title:s,searchValue:r,searchPlaceholder:l,onSearchChange:a,onSearchKeyDown:i,actionButton:t})=>e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h2",{className:"text-2xl font-semibold",children:s}),e.jsx(Q,{placeholder:l,className:"w-64",value:r,onChange:n=>a(n.target.value),onKeyDown:i})]}),t&&e.jsxs(T,{size:"sm",onClick:t.onClick,children:[t.icon&&e.jsx("span",{className:"mr-2",children:t.icon}),t.label]})]}),ee=[{accessorKey:"id",header:"#",cell:({row:s})=>{const r=s.index+1;return e.jsx("div",{className:"w-[50px] font-medium",children:r})},enableSorting:!1},{accessorKey:"item_class_id",header:"Mã loại món",cell:({row:s})=>{const r=s.original;return e.jsx("span",{className:"font-medium",children:r.item_class_id})}},{accessorKey:"item_class_name",header:"Tên loại món",cell:({row:s})=>{const r=s.original;return e.jsx("span",{className:"font-medium",children:r.item_class_name})}},{id:"actions",header:"Thao tác",cell:({row:s,table:r})=>{const l=s.original,a=l.active===1,i=r.options.meta;return e.jsx(V,{variant:a?"default":"destructive",className:`cursor-pointer ${a?"bg-green-500 text-white hover:bg-green-600":"bg-red-500 text-white hover:bg-red-600"}`,onClick:t=>{var n;t.stopPropagation(),(n=i==null?void 0:i.onToggleItemClassStatus)==null||n.call(i,l)},children:a?"Active":"Deactive"})}},{id:"delete",header:"",cell:({row:s,table:r})=>{const l=s.original,a=r.options.meta;return e.jsxs(T,{variant:"ghost",size:"sm",onClick:i=>{var t;i.stopPropagation(),(t=a==null?void 0:a.onDeleteItemClass)==null||t.call(a,l)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(F,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa loại món ",l.item_class_name]})]})}}];function se({columns:s,data:r,onEditItemClass:l,onDeleteItemClass:a,onToggleItemClassStatus:i,onRowClick:t}){var d;const n=U({data:r,columns:s,getCoreRowModel:X(),meta:{onEditItemClass:l,onDeleteItemClass:a,onToggleItemClassStatus:i}});return e.jsx("div",{className:"rounded-md border",children:e.jsxs(q,{children:[e.jsx(G,{children:n.getHeaderGroups().map(m=>e.jsx(f,{children:m.headers.map(c=>e.jsx(J,{children:c.isPlaceholder?null:I(c.column.columnDef.header,c.getContext())},c.id))},m.id))}),e.jsx(W,{children:(d=n.getRowModel().rows)!=null&&d.length?n.getRowModel().rows.map(m=>e.jsx(f,{"data-state":m.getIsSelected()&&"selected",className:t?"hover:bg-muted/50 cursor-pointer":"",onClick:()=>t==null?void 0:t(m.original),children:m.getVisibleCells().map(c=>e.jsx(b,{children:I(c.column.columnDef.cell,c.getContext())},c.id))},m.id)):e.jsx(f,{children:e.jsx(b,{colSpan:s.length,className:"h-24 text-center",children:"Không có dữ liệu loại món."})})})]})})}function te(){const s=k(),[r,l]=x.useState(""),[a,i]=x.useState(""),[t,n]=x.useState(!1),[d,m]=x.useState(null),c=K(),j=L(),{data:N,isLoading:y,error:S}=$({searchTerm:r||void 0}),v=y,u=S,_=o=>{h.info(`Chỉnh sửa loại món: ${o.item_class_name}`)},w=async o=>{try{const p={...o,active:o.active===1?0:1};await c.mutateAsync(p);const g=p.active===1?"kích hoạt":"vô hiệu hóa";h.success(`Đã ${g} loại món "${o.item_class_name}"`)}catch(p){const g=C(p);h.error(g)}},D=o=>{m(o),n(!0)},M=async()=>{if(d)try{await j.mutateAsync(d.id),h.success(`Loại món "${d.item_class_name}" đã được xóa thành công!`),n(!1),m(null)}catch(o){const p=C(o);h.error(p)}},P=()=>{s({to:"/menu/item-class/detail"})},E=o=>{s({to:"/menu/item-class/detail/$id",params:{id:o.id}})};return e.jsxs(e.Fragment,{children:[e.jsx(A,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(O,{}),e.jsx(z,{}),e.jsx(B,{})]})}),e.jsx(H,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Z,{title:"Danh sách loại món",searchValue:a,searchPlaceholder:"Tìm kiếm loại món...",onSearchChange:i,onSearchKeyDown:o=>{o.key==="Enter"&&(o.preventDefault(),l(a))},actionButton:{label:"Tạo loại món",icon:e.jsx(Y,{className:"h-4 w-4"}),onClick:P}}),u&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:C(u)})}),!u&&v&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:"Đang tải dữ liệu loại món..."})}),!u&&!v&&e.jsx(se,{columns:ee,data:N||[],onEditItemClass:_,onDeleteItemClass:D,onToggleItemClassStatus:w,onRowClick:E}),e.jsx(R,{open:t,onOpenChange:n,content:"Bạn có muốn xoá ?",onConfirm:M,isLoading:j.isPending})]})})]})}const Je=te;export{Je as component};
