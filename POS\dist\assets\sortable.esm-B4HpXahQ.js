import{r as v,R as K}from"./index-D0Grd55b.js";import{f as R,g as de,h as fe,i as ge,s as pe,j as be,k as ve,l as U,a as he,u as me,m as xe,C as Y,n as Ie,o as Re}from"./core.esm-ZfuNvihZ.js";function q(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function ye(e,t){return e.reduce((n,r,s)=>{const a=t.get(r);return a&&(n[s]=a),n},Array(e.length))}function O(e){return e!==null&&e>=0}function Ce(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function we(e){return typeof e=="boolean"?{draggable:e,droppable:e}:e}const B=e=>{let{rects:t,activeIndex:n,overIndex:r,index:s}=e;const a=q(t,r,n),o=t[s],c=a[s];return!c||!o?null:{x:c.left-o.left,y:c.top-o.top,scaleX:c.width/o.width,scaleY:c.height/o.height}},L={scaleX:1,scaleY:1},Xe=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:s,rects:a,overIndex:o}=e;const c=(t=a[n])!=null?t:r;if(!c)return null;if(s===n){const u=a[o];return u?{x:0,y:n<o?u.top+u.height-(c.top+c.height):u.top-c.top,...L}:null}const f=De(a,s,n);return s>n&&s<=o?{x:0,y:-c.height-f,...L}:s<n&&s>=o?{x:0,y:c.height+f,...L}:{x:0,y:0,...L}};function De(e,t,n){const r=e[t],s=e[t-1],a=e[t+1];return r?n<t?s?r.top-(s.top+s.height):a?a.top-(r.top+r.height):0:a?a.top-(r.top+r.height):s?r.top-(s.top+s.height):0:0}const H="Sortable",V=K.createContext({activeIndex:-1,containerId:H,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:B,disabled:{draggable:!1,droppable:!1}});function _e(e){let{children:t,id:n,items:r,strategy:s=B,disabled:a=!1}=e;const{active:o,dragOverlay:c,droppableRects:f,over:u,measureDroppableContainers:i}=be(),l=ve(H,n),d=c.rect!==null,g=v.useMemo(()=>r.map(I=>typeof I=="object"&&"id"in I?I.id:I),[r]),y=o!=null,C=o?g.indexOf(o.id):-1,h=u?g.indexOf(u.id):-1,w=v.useRef(g),x=!Ce(g,w.current),m=h!==-1&&C===-1||x,b=we(a);U(()=>{x&&y&&i(g)},[x,g,y,i]),v.useEffect(()=>{w.current=g},[g]);const D=v.useMemo(()=>({activeIndex:C,containerId:l,disabled:b,disableTransforms:m,items:g,overIndex:h,useDragOverlay:d,sortedRects:ye(g,f),strategy:s}),[C,l,b.draggable,b.droppable,m,g,h,f,d,s]);return K.createElement(V.Provider,{value:D},t)}const Se=e=>{let{id:t,items:n,activeIndex:r,overIndex:s}=e;return q(n,r,s).indexOf(t)},Ae=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:s,items:a,newIndex:o,previousItems:c,previousContainerId:f,transition:u}=e;return!u||!r||c!==a&&s===o?!1:n?!0:o!==s&&t===f},Te={duration:200,easing:"ease"},J="transform",Ee=Y.Transition.toString({property:J,duration:0,easing:"linear"}),Ne={roleDescription:"sortable"};function Oe(e){let{disabled:t,index:n,node:r,rect:s}=e;const[a,o]=v.useState(null),c=v.useRef(n);return U(()=>{if(!t&&n!==c.current&&r.current){const f=s.current;if(f){const u=Re(r.current,{ignoreTransform:!0}),i={x:f.left-u.left,y:f.top-u.top,scaleX:f.width/u.width,scaleY:f.height/u.height};(i.x||i.y)&&o(i)}}n!==c.current&&(c.current=n)},[t,n,r,s]),v.useEffect(()=>{a&&o(null)},[a]),a}function je(e){let{animateLayoutChanges:t=Ae,attributes:n,disabled:r,data:s,getNewIndex:a=Se,id:o,strategy:c,resizeObserverConfig:f,transition:u=Te}=e;const{items:i,containerId:l,activeIndex:d,disabled:g,disableTransforms:y,sortedRects:C,overIndex:h,useDragOverlay:w,strategy:x}=v.useContext(V),m=Le(r,g),b=i.indexOf(o),D=v.useMemo(()=>({sortable:{containerId:l,index:b,items:i},...s}),[l,s,b,i]),I=v.useMemo(()=>i.slice(i.indexOf(o)),[i,o]),{rect:N,node:z,isOver:W,setNodeRef:P}=he({id:o,data:D,disabled:m.droppable,resizeObserverConfig:{updateMeasurementsFor:I,...f}}),{active:T,activatorEvent:Z,activeNodeRect:ee,attributes:te,setNodeRef:X,listeners:re,isDragging:M,over:ne,setActivatorNodeRef:se,transform:oe}=me({id:o,data:D,attributes:{...Ne,...n},disabled:m.draggable}),ae=xe(P,X),S=!!T,_=S&&!y&&O(d)&&O(h),j=!w&&M,F=j&&_?oe:null,ie=_?F??(c??x)({rects:C,activeNodeRect:ee,activeIndex:d,overIndex:h,index:b}):null,E=O(d)&&O(h)?a({id:o,items:i,activeIndex:d,overIndex:h}):b,A=T==null?void 0:T.id,p=v.useRef({activeId:A,items:i,newIndex:E,containerId:l}),ce=i!==p.current.items,G=t({active:T,containerId:l,isDragging:M,isSorting:S,id:o,index:b,items:i,newIndex:p.current.newIndex,previousItems:p.current.items,previousContainerId:p.current.containerId,transition:u,wasDragging:p.current.activeId!=null}),$=Oe({disabled:!G,index:b,node:z,rect:N});return v.useEffect(()=>{S&&p.current.newIndex!==E&&(p.current.newIndex=E),l!==p.current.containerId&&(p.current.containerId=l),i!==p.current.items&&(p.current.items=i)},[S,E,l,i]),v.useEffect(()=>{if(A===p.current.activeId)return;if(A!=null&&p.current.activeId==null){p.current.activeId=A;return}const ue=setTimeout(()=>{p.current.activeId=A},50);return()=>clearTimeout(ue)},[A]),{active:T,activeIndex:d,attributes:te,data:D,rect:N,index:b,newIndex:E,items:i,isOver:W,isSorting:S,isDragging:M,listeners:re,node:z,overIndex:h,over:ne,setNodeRef:ae,setActivatorNodeRef:se,setDroppableNodeRef:P,setDraggableNodeRef:X,transform:$??ie,transition:le()};function le(){if($||ce&&p.current.newIndex===b)return Ee;if(!(j&&!Ie(Z)||!u)&&(S||G))return Y.Transition.toString({...u,property:J})}}function Le(e,t){var n,r;return typeof e=="boolean"?{draggable:e,droppable:!1}:{draggable:(n=e==null?void 0:e.draggable)!=null?n:t.draggable,droppable:(r=e==null?void 0:e.droppable)!=null?r:t.droppable}}function k(e){if(!e)return!1;const t=e.data.current;return!!(t&&"sortable"in t&&typeof t.sortable=="object"&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable)}const ke=[R.Down,R.Right,R.Up,R.Left],Fe=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:s,droppableContainers:a,over:o,scrollableAncestors:c}}=t;if(ke.includes(e.code)){if(e.preventDefault(),!n||!r)return;const f=[];a.getEnabled().forEach(l=>{if(!l||l!=null&&l.disabled)return;const d=s.get(l.id);if(d)switch(e.code){case R.Down:r.top<d.top&&f.push(l);break;case R.Up:r.top>d.top&&f.push(l);break;case R.Left:r.left>d.left&&f.push(l);break;case R.Right:r.left<d.left&&f.push(l);break}});const u=de({collisionRect:r,droppableRects:s,droppableContainers:f});let i=fe(u,"id");if(i===(o==null?void 0:o.id)&&u.length>1&&(i=u[1].id),i!=null){const l=a.get(n.id),d=a.get(i),g=d?s.get(d.id):null,y=d==null?void 0:d.node.current;if(y&&g&&l&&d){const h=ge(y).some((I,N)=>c[N]!==I),w=Q(l,d),x=Me(l,d),m=h||!w?{x:0,y:0}:{x:x?r.width-g.width:0,y:x?r.height-g.height:0},b={x:g.left,y:g.top};return m.x&&m.y?b:pe(b,m)}}}};function Q(e,t){return!k(e)||!k(t)?!1:e.data.current.sortable.containerId===t.data.current.sortable.containerId}function Me(e,t){return!k(e)||!k(t)||!Q(e,t)?!1:e.data.current.sortable.index<t.data.current.sortable.index}export{_e as S,q as a,B as r,Fe as s,je as u,Xe as v};
