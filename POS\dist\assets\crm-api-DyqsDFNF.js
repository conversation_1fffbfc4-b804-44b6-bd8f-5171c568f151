import{y as t}from"./index-DZ2N7iEN.js";const n={Accept:"application/json, text/plain, */*","Accept-Language":"en-US,en;q=0.9,vi;q=0.8",Connection:"keep-alive","Content-Type":"application/json",Server:"LIVE","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},r=t.create({baseURL:"https://crm-ipos-proxy.lethimcook.workers.dev",timeout:3e4,headers:n});r.interceptors.request.use(e=>{const o=localStorage.getItem("crm_token");return o?e.headers.Authorization=o:console.warn("No CRM token found in localStorage"),e});export{r as c};
