import{u as X,r as w,z as I,j as e,B as E,h as le,aA as de,a4 as H}from"./index-D0Grd55b.js";import{g as q}from"./error-utils-rXhAXMyo.js";import"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import{c as he,d as me,e as ue,a as xe,u as fe,f as pe}from"./use-users-ByAke5a8.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import"./date-range-picker-CruKYeHR.js";import{u as ge,L as je,j as Se,k as te,a as U,b as $,c as V,d as O,e as W,F as ve}from"./form-Bk1C9kLO.js";import{I as z}from"./input-C-0UnKOB.js";import{S as be,a as ye,b as Ne,c as we,d as re}from"./select-DBO-8fSu.js";import{s as Ce}from"./zod-G2vIgQkk.js";import{u as _e}from"./use-roles-FABx1m88.js";import{D as Y,a as Z,b as D,c as ee,e as se}from"./dialog-C8IVKkOo.js";import{C as K}from"./checkbox-C_eqgJmW.js";import{c as ke}from"./createReactComponent-eJgt86Cn.js";import{I as G}from"./IconChevronDown-C1OURZxU.js";import{R as Be,a as Te}from"./radio-group-CpEBFN0-.js";import{u as oe}from"./use-tables-DSnIc-n-.js";import{P as ne}from"./password-input-DVF7dySI.js";import{X as J}from"./calendar-5lpy20z0.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Q=ke("outline","chevron-up","IconChevronUp",[["path",{d:"M6 15l6 -6l6 6",key:"svg-0"}]]);function Ee(){const{brands:s,cities:a,stores:m}=X(d=>d.auth),h=w.useMemo(()=>!s||!a||!m?[]:s.map(d=>{const t=a.filter(r=>r.active===1).map(r=>({...r,stores:m.filter(f=>f.active===1&&f.brand_uid===d.id&&f.city_uid===r.id)})).filter(r=>r.stores.length>0);return{...d,cities:t}}).filter(d=>d.cities.length>0),[s,a,m]),u=d=>h.find(t=>t.id===d),j=d=>h.flatMap(t=>t.cities).find(t=>t.id===d);return{hierarchicalData:h,brands:s,cities:a,stores:m,findBrandById:u,findCityById:j,findStoreById:d=>h.flatMap(t=>t.cities).flatMap(t=>t.stores).find(t=>t.id===d),findBrandByCity:d=>h.find(t=>t.cities.some(r=>r.id===d)),findCityByStore:d=>h.flatMap(t=>t.cities).find(t=>t.stores.some(r=>r.id===d)),findBrandByStore:d=>h.find(t=>t.cities.some(r=>r.stores.some(f=>f.id===d))),getStoresByBrand:d=>{const t=u(d);return t?t.cities.flatMap(r=>r.stores):[]},getStoresByCity:d=>{const t=j(d);return t?t.stores:[]},filterHierarchicalData:d=>d?h.map(t=>({...t,cities:t.cities.map(r=>({...r,stores:r.stores.filter(f=>f.store_name.toLowerCase().includes(d.toLowerCase()))})).filter(r=>r.city_name.toLowerCase().includes(d.toLowerCase())||r.stores.length>0)})).filter(t=>t.brand_name.toLowerCase().includes(d.toLowerCase())||t.cities.length>0):h,isLoading:!s||!a||!m,isEmpty:h.length===0}}function Ae(s){const[a,m]=w.useState(""),[h,u]=w.useState(new Set),[j,o]=w.useState(new Set),{hierarchicalData:n,filterHierarchicalData:l}=Ee(),[c,x]=w.useState(()=>{const i=new Set;return s.forEach(g=>{g.startsWith("store:")&&i.add(g.replace("store:",""))}),i});w.useEffect(()=>{const i=new Set;s.forEach(g=>{g.startsWith("store:")&&i.add(g.replace("store:",""))}),x(i)},[s,n]);const S=w.useMemo(()=>l(a),[l,a]),C=i=>{const g=new Set(h);g.has(i)?g.delete(i):g.add(i),u(g)},d=i=>{const g=new Set(j);g.has(i)?g.delete(i):g.add(i),o(g)},t=(i,g)=>{const b=new Set(c);if(g==="brand"){const A=n.find(_=>_.id===i);if(!A)return;f(i)?A.cities.forEach(_=>{_.stores.forEach(N=>{b.delete(N.id)})}):A.cities.forEach(_=>{_.stores.forEach(N=>{b.add(N.id)})})}else if(g==="city"){const A=n.flatMap(_=>_.cities).find(_=>_.id===i);if(!A)return;r(i)?A.stores.forEach(_=>{b.delete(_.id)}):A.stores.forEach(_=>{b.add(_.id)})}else if(g==="store")if(b.has(i)){b.delete(i);const L=n.flatMap(N=>N.cities).find(N=>N.stores.some(B=>B.id===i)),_=n.find(N=>N.cities.some(B=>B.stores.some(P=>P.id===i)));L&&_&&L.stores.filter(B=>B.id!==i&&b.has(B.id)).length===0&&_.cities.filter(P=>P.id!==L.id&&P.stores.some(k=>b.has(k.id))).length}else{b.add(i);const L=n.flatMap(N=>N.cities).find(N=>N.stores.some(B=>B.id===i)),_=n.find(N=>N.cities.some(B=>B.stores.some(P=>P.id===i)));L&&_&&_.cities.some(N=>N.stores.some(B=>B.id!==i&&b.has(B.id)))}x(b)},r=i=>{const g=n.flatMap(b=>b.cities).find(b=>b.id===i);return!g||g.stores.length===0?!1:g.stores.some(b=>c.has(b.id))},f=i=>{const g=n.find(b=>b.id===i);return!g||g.cities.length===0?!1:g.cities.some(b=>b.stores.some(A=>c.has(A.id)))},v=(i,g)=>g==="store"?c.has(i):g==="city"?r(i):g==="brand"?f(i):!1,p=(i,g)=>!1,y=()=>c.size,T=new Set;return c.forEach(i=>{T.add(`store:${i}`)}),{searchTerm:a,setSearchTerm:m,expandedBrands:h,expandedCities:j,localSelectedItems:T,setLocalSelectedItems:i=>{const g=new Set;i.forEach(b=>{b.startsWith("store:")&&g.add(b.replace("store:",""))}),x(g)},hierarchicalData:n,filteredData:S,toggleBrandExpansion:C,toggleCityExpansion:d,handleItemToggle:t,isItemSelected:v,isItemIndeterminate:p,getSelectedCount:y}}const Le=(s=!1)=>I.object({email:I.string().email("Email không hợp lệ"),full_name:I.string().min(1,"Tên nhân viên là bắt buộc"),phone:I.string().optional(),role_uid:I.string().min(1,"Chức vụ là bắt buộc"),password:s?I.string().optional():I.string().min(6,"Mật khẩu phải có ít nhất 6 ký tự"),confirmPassword:s?I.string().optional():I.string().min(6,"Xác nhận mật khẩu phải có ít nhất 6 ký tự"),brand_access:I.array(I.string()).optional()}).refine(a=>s?a.password&&a.password.length>0?a.password===a.confirmPassword:!a.confirmPassword||a.confirmPassword.length===0:a.password===a.confirmPassword,{message:"Mật khẩu xác nhận không khớp",path:["confirmPassword"]});function Ie(s){return s?s.role_id==="OWNER":!1}function ae(s){return s?["CASHIER","ORDER"].includes(s.role_id):!1}function ce(s){return s?s.role_id==="ORDER":!1}function Pe(s,a){if(!a)return"";const m=s.replace("store:",""),h=a.find(u=>u.id===m);return(h==null?void 0:h.brand_uid)||""}function Me({initialData:s,isEditMode:a=!1,onSubmit:m}){const[h,u]=w.useState(!1),[j,o]=w.useState(!1),[n,l]=w.useState(!1),[c,x]=w.useState([]),[S,C]=w.useState(""),[d,t]=w.useState([]),{data:r=[],isLoading:f}=_e(),{stores:v}=X(k=>k.auth),p=Le(a),y=()=>({email:(s==null?void 0:s.email)||"",full_name:(s==null?void 0:s.full_name)||"",phone:(s==null?void 0:s.phone)||"",role_uid:(s==null?void 0:s.role_uid)||"",password:"",confirmPassword:"",brand_access:(s==null?void 0:s.brand_access)||[]}),T=ge({resolver:Ce(p),defaultValues:y()});w.useEffect(()=>{var k;if(s&&a)if(s.brand_access&&s.brand_access.length>0){x(s.brand_access);const M=r.find(F=>F.id===s.role_uid);if(ae(M)&&s.brand_access.length===1&&(C(s.brand_access[0]),ce(M)&&((k=s.user_permissions)!=null&&k.tables))){const F=s.brand_access[0].replace("store:",""),R=s.user_permissions.tables[F];R&&Array.isArray(R)&&t(R)}}else console.log("🔍 No brand_access found in initialData")},[s,a,r]),w.useEffect(()=>{if(s&&a){const k={email:s.email||"",full_name:s.full_name||"",phone:s.phone||"",role_uid:s.role_uid||"",password:"",confirmPassword:"",brand_access:s.brand_access||[]};T.reset(k),Object.entries(k).forEach(([M,F])=>{T.setValue(M,F,{shouldValidate:!1,shouldDirty:!1})})}},[s,a,T]);const i=T.watch("role_uid"),g=r.find(k=>k.id===i),b=Ie(g),A=ae(g),L=ce(g);return{form:T,handleSubmit:async k=>{try{const{confirmPassword:M,...F}=k;let R=[];b||(A?R=S?[S]:[]:R=c);const ie={...F,brand_access:R};await m(ie)}catch(M){console.error("Error creating employee:",M)}},roles:r,rolesLoading:f,stores:v,selectedRole:i,selectedRoleObj:g,isOwner:b,needsStore:A,needsTables:L,selectedBrandAccess:c,selectedStore:S,selectedTables:d,showBrandSelector:h,setShowBrandSelector:u,showStoreSelector:j,setShowStoreSelector:o,showTableSelector:n,setShowTableSelector:l,handleBrandAccessSave:k=>{x(k),u(!1)},handleStoreSave:k=>{C(k),o(!1),t([])},handleTablesSave:k=>{t(k),l(!1)},getBrandIdFromStore:k=>Pe(k,v)}}function Fe({selectedStore:s}){const[a,m]=w.useState(""),[h,u]=w.useState(new Set),[j,o]=w.useState(s),{brands:n,cities:l,stores:c}=X(t=>t.auth),x=w.useMemo(()=>!n||!l||!c?[]:n.map(t=>{const r=l.filter(f=>f.active===1).map(f=>({...f,stores:c.filter(v=>v.active===1&&v.brand_uid===t.id&&v.city_uid===f.id)})).filter(f=>f.stores.length>0);return{...t,cities:r}}).filter(t=>t.cities.length>0),[n,l,c]),S=w.useMemo(()=>a?x.map(t=>({...t,cities:t.cities.map(r=>({...r,stores:r.stores.filter(f=>f.store_name.toLowerCase().includes(a.toLowerCase()))})).filter(r=>r.city_name.toLowerCase().includes(a.toLowerCase())||r.stores.length>0)})).filter(t=>t.brand_name.toLowerCase().includes(a.toLowerCase())||t.cities.length>0):x,[x,a]);return{searchTerm:a,setSearchTerm:m,hierarchicalData:x,filteredData:S,expandedBrands:h,toggleBrandExpansion:t=>{const r=new Set(h);r.has(t)?r.delete(t):r.add(t),u(r)},localSelectedStore:j,setLocalSelectedStore:o,resetSelection:()=>{o(s)}}}function Re({selectedTables:s,storeId:a,brandId:m,enabled:h}){const[u,j]=w.useState(new Set),[o,n]=w.useState(new Set(s)),{data:l=[],isLoading:c}=oe({storeUid:a}),x=w.useMemo(()=>{const v=new Map;return l.forEach(p=>{var i;const y=p.area_uid||"no-area",T=((i=p.area)==null?void 0:i.area_name)||"Không có khu vực";v.has(y)||v.set(y,{area_uid:y,area_name:T,tables:[]}),v.get(y).tables.push(p)}),Array.from(v.values()).sort((p,y)=>p.area_name.localeCompare(y.area_name))},[l]);return{tables:l,areaData:x,isLoading:c,expandedAreas:u,toggleAreaExpansion:v=>{const p=new Set(u);p.has(v)?p.delete(v):p.add(v),j(p)},localSelectedTables:o,setLocalSelectedTables:n,resetSelection:()=>{n(new Set(s))},handleTableToggle:v=>{const p=new Set(o);p.has(v)?p.delete(v):p.add(v),n(p)},handleAreaToggle:v=>{const p=new Set(o),y=v.tables.map(i=>i.id);y.every(i=>p.has(i))?y.forEach(i=>p.delete(i)):y.forEach(i=>p.add(i)),n(p)},isAreaSelected:v=>v.tables.map(y=>y.id).every(y=>o.has(y)),isAreaIndeterminate:v=>{const p=v.tables.map(T=>T.id),y=p.filter(T=>o.has(T)).length;return y>0&&y<p.length}}}function He({filteredData:s,expandedBrands:a,expandedCities:m,toggleBrandExpansion:h,toggleCityExpansion:u,handleItemToggle:j,isItemSelected:o,isItemIndeterminate:n}){return e.jsx("div",{className:"max-h-96 overflow-y-auto rounded-md border",children:s.map(l=>e.jsxs("div",{className:"border-b last:border-b-0",children:[e.jsxs("div",{className:"flex items-center gap-2 p-3 hover:bg-gray-50",children:[e.jsx(K,{checked:o(l.id,"brand"),onCheckedChange:()=>j(l.id,"brand"),ref:c=>{if(c){const x=c.querySelector("input");x&&(x.indeterminate=n(l.id,"brand"))}}}),e.jsxs("button",{onClick:()=>h(l.id),className:"flex flex-1 items-center gap-1 text-left font-medium",children:[a.has(l.id)?e.jsx(Q,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"}),l.brand_name]})]}),a.has(l.id)&&e.jsx("div",{className:"pl-6",children:l.cities.map(c=>e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2 p-2 hover:bg-gray-50",children:[e.jsx(K,{checked:o(c.id,"city"),onCheckedChange:()=>j(c.id,"city"),ref:x=>{if(x){const S=x.querySelector("input");S&&(S.indeterminate=n(c.id,"city"))}}}),e.jsxs("button",{onClick:()=>u(c.id),className:"flex flex-1 items-center gap-1 text-left",children:[m.has(c.id)?e.jsx(Q,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"}),c.city_name]})]}),m.has(c.id)&&e.jsx("div",{className:"pl-6",children:c.stores.map(x=>e.jsxs("div",{className:"flex items-center gap-2 p-2 hover:bg-gray-50",children:[e.jsx(K,{checked:o(x.id,"store"),onCheckedChange:()=>j(x.id,"store")}),e.jsx("span",{className:"flex-1",children:x.store_name})]},x.id))})]},c.id))})]},l.id))})}function Ue({open:s,onOpenChange:a,selectedItems:m,onSave:h}){const{searchTerm:u,setSearchTerm:j,expandedBrands:o,expandedCities:n,localSelectedItems:l,filteredData:c,toggleBrandExpansion:x,toggleCityExpansion:S,handleItemToggle:C,isItemSelected:d,isItemIndeterminate:t,getSelectedCount:r}=Ae(m),f=()=>{h(Array.from(l)),a(!1)},v=()=>{a(!1)};return e.jsx(Y,{open:s,onOpenChange:a,children:e.jsxs(Z,{className:"max-w-4xl",children:[e.jsx(D,{children:e.jsx(ee,{className:"text-center",children:"Chọn thương hiệu và thành phố, nhà hàng"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(z,{placeholder:"Tìm kiếm thương hiệu, thành phố, cửa hàng...",value:u,onChange:p=>j(p.target.value)})}),e.jsx("div",{className:"max-h-96 overflow-y-auto",children:e.jsx(He,{filteredData:c,expandedBrands:o,expandedCities:n,toggleBrandExpansion:x,toggleCityExpansion:S,handleItemToggle:C,isItemSelected:d,isItemIndeterminate:t})})]}),e.jsxs(se,{children:[e.jsx(E,{variant:"outline",onClick:v,children:"Hủy"}),e.jsxs(E,{onClick:f,children:["Lưu (",r()," mục)"]})]})]})})}function $e({open:s,onOpenChange:a,selectedStore:m,onSave:h}){const{searchTerm:u,setSearchTerm:j,filteredData:o,expandedBrands:n,toggleBrandExpansion:l,localSelectedStore:c,setLocalSelectedStore:x,resetSelection:S}=Fe({selectedStore:m}),C=()=>{h(c),a(!1)},d=()=>{S(),a(!1)};return e.jsx(Y,{open:s,onOpenChange:a,children:e.jsxs(Z,{className:"max-w-2xl",children:[e.jsx(D,{children:e.jsx(ee,{children:"Chọn cửa hàng"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(z,{placeholder:"Tìm kiếm cửa hàng...",value:u,onChange:t=>j(t.target.value)})}),e.jsx("div",{className:"max-h-96 overflow-y-auto",children:e.jsx(Be,{value:c,onValueChange:x,children:o.map(t=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex cursor-pointer items-center gap-2 rounded-md bg-gray-100 p-2 hover:bg-gray-200",onClick:()=>l(t.id),children:[n.has(t.id)?e.jsx(G,{className:"h-4 w-4"}):e.jsx(Q,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:t.brand_name})]}),n.has(t.id)&&e.jsx("div",{className:"ml-6 space-y-2",children:t.cities.map(r=>e.jsx("div",{className:"space-y-1",children:e.jsx("div",{className:"ml-4 space-y-1",children:r.stores.map(f=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Te,{value:`store:${f.id}`,id:`store-${f.id}`}),e.jsx(je,{htmlFor:`store-${f.id}`,className:"text-sm",children:f.store_name})]},f.id))})},r.id))})]},t.id))})})]}),e.jsxs(se,{children:[e.jsx(E,{variant:"outline",onClick:d,children:"Hủy"}),e.jsx(E,{onClick:C,disabled:!c,children:"Lưu"})]})]})})}function Ve({open:s,onOpenChange:a,selectedTables:m,onSave:h,storeId:u,brandId:j}){const{areaData:o,isLoading:n,expandedAreas:l,toggleAreaExpansion:c,localSelectedTables:x,resetSelection:S,handleTableToggle:C,handleAreaToggle:d,isAreaSelected:t,isAreaIndeterminate:r}=Re({selectedTables:m,storeId:u,brandId:j,enabled:s}),f=()=>{h(Array.from(x)),a(!1)},v=()=>{S(),a(!1)};return e.jsx(Y,{open:s,onOpenChange:a,children:e.jsxs(Z,{className:"max-w-2xl",children:[e.jsx(D,{children:e.jsx(ee,{children:"Chọn bàn"})}),e.jsx("div",{className:"space-y-4",children:n?e.jsx("div",{className:"text-center",children:"Đang tải danh sách bàn..."}):o.length===0?e.jsx("div",{className:"text-center text-gray-500",children:"Không có bàn nào trong cửa hàng này"}):e.jsx("div",{className:"max-h-96 space-y-2 overflow-y-auto",children:o.map(p=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2 rounded-md bg-gray-100 p-2",children:[e.jsx("button",{onClick:()=>c(p.area_uid),className:"flex items-center gap-1 rounded p-1 hover:bg-gray-200",children:l.has(p.area_uid)?e.jsx(G,{className:"h-4 w-4"}):e.jsx(Q,{className:"h-4 w-4"})}),e.jsx(K,{checked:t(p)||r(p)&&"indeterminate",onCheckedChange:()=>d(p)}),e.jsx("span",{className:"font-medium",children:p.area_name}),e.jsxs("span",{className:"text-sm text-gray-500",children:["(",p.tables.length," bàn)"]})]}),l.has(p.area_uid)&&e.jsx("div",{className:"ml-6 space-y-1",children:p.tables.map(y=>e.jsxs("div",{className:"flex items-center gap-2 rounded p-2 hover:bg-gray-50",children:[e.jsx(K,{checked:x.has(y.id),onCheckedChange:()=>C(y.id)}),e.jsx("span",{className:"text-sm",children:y.table_name})]},y.id))})]},p.area_uid))})}),e.jsxs(se,{children:[e.jsx(E,{variant:"outline",onClick:v,children:"Hủy"}),e.jsxs(E,{onClick:f,children:["Lưu (",x.size," bàn)"]})]})]})})}function Oe({selectedStore:s,brands:a,cities:m,stores:h}){const{brands:u,cities:j,stores:o}=X(C=>C.auth),n=a||u,l=m||j,c=h||o,S=(()=>{if(!s||!n||!l||!c)return null;const C=s.replace("store:",""),d=c.find(f=>f.id===C);if(!d)return null;const t=l.find(f=>f.id===d.city_uid),r=n.find(f=>f.id===d.brand_uid);return{storeName:d.store_name,cityName:(t==null?void 0:t.city_name)||"",brandName:(r==null?void 0:r.brand_name)||""}})();return S?e.jsx("div",{className:"space-y-3",children:e.jsx("div",{className:"rounded-md border bg-gray-50",children:e.jsxs("div",{className:"grid grid-cols-2 border-b bg-white last:border-b-0 hover:bg-gray-50",children:[e.jsx("div",{className:"p-3 text-sm font-medium text-gray-700",children:S.brandName}),e.jsxs("div",{className:"p-3 text-sm text-gray-600",children:[S.cityName," - ",S.storeName]})]})})}):null}function We({selectedItems:s}){const{brands:a,cities:m,stores:h}=X(n=>n.auth),u=()=>!a||!m||!h?[]:a.map(n=>{const l=m.filter(c=>c.active===1).map(c=>({...c,stores:h.filter(x=>x.active===1&&x.brand_uid===n.id&&x.city_uid===c.id)})).filter(c=>c.stores.length>0);return{...n,cities:l}}).filter(n=>n.cities.length>0),o=(()=>{const n=u(),l=new Set(s),c=[];return n.forEach(x=>{if(l.has(`brand:${x.id}`))c.push({brandName:x.brand_name,accessLevel:"Truy cập toàn thương hiệu",type:"brand"});else{const S=[],C=[];x.cities.forEach(t=>{l.has(`city:${t.id}`)?S.push(t.city_name):t.stores.forEach(r=>{l.has(`store:${r.id}`)&&C.push({cityName:t.city_name,storeName:r.store_name})})}),S.forEach(t=>{c.push({brandName:x.brand_name,accessLevel:`${t} - Truy cập toàn thành phố`,type:"city"})});const d=C.reduce((t,r)=>(t[r.cityName]||(t[r.cityName]=[]),t[r.cityName].push(r.storeName),t),{});Object.entries(d).forEach(([t,r])=>{c.push({brandName:x.brand_name,accessLevel:`${t} - ${r.join(", ")}`,type:"store"})})}}),c})();return o.length===0?e.jsx("div",{className:"rounded-md border border-dashed border-gray-300 bg-gray-50 p-6",children:e.jsx("div",{className:"text-center text-sm text-gray-500"})}):e.jsx("div",{className:"space-y-3",children:e.jsx("div",{className:"rounded-md border bg-gray-50",children:o.map((n,l)=>e.jsxs("div",{className:"grid grid-cols-2 border-b bg-white last:border-b-0 hover:bg-gray-50",children:[e.jsx("div",{className:"p-3 text-sm font-medium text-gray-700",children:n.brandName}),e.jsx("div",{className:"p-3 text-sm text-gray-600",children:n.accessLevel})]},l))})})}function ze({needsStoreSelection:s,selectedStore:a,selectedBrandAccess:m,onShowStoreSelector:h,onShowBrandSelector:u,brands:j,cities:o,stores:n}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-base font-medium",children:"Cho phép truy cập vào thương hiệu"}),e.jsxs("div",{className:"space-y-1",children:[s?e.jsx(Oe,{selectedStore:a,brands:j,cities:o,stores:n}):e.jsx(We,{selectedItems:m}),e.jsx(E,{type:"button",variant:"outline",onClick:s?h:u,className:"w-full justify-center text-blue-600",children:s?"Chọn cửa hàng":"Chọn thương hiệu và cửa hàng"})]})]})}function Ke({control:s,isEditMode:a,userId:m}){const[h,u]=w.useState(""),j=he(),{setValue:o}=Se(),n=te({control:s,name:"password"}),l=te({control:s,name:"confirmPassword"}),c=n&&l&&n===l&&n.length>=6,x=async()=>{if(!(!m||!c))try{u(""),await j.mutateAsync({user_uid:m,new_password:n,confirm_password:l}),u("Đổi mật khẩu thành công"),o("password",""),o("confirmPassword","")}catch(S){console.error("Change password error:",S)}};return e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-base font-medium",children:a?"Đổi mật khẩu":"Tạo mật khẩu"}),e.jsx("p",{className:"text-muted-foreground py-2 text-sm",children:"Vui lòng tạo một mật khẩu dài ít nhất 6 ký tự bao gồm chữ và số. Đổi mật khẩu sẽ làm tài khoản đăng xuất khỏi tất cả thiết bị"}),e.jsx(U,{control:s,name:"password",render:({field:S})=>e.jsxs($,{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(V,{className:"text-right text-sm",children:"Mật khẩu *"}),e.jsxs("div",{className:"col-span-3",children:[e.jsx(O,{children:e.jsx(ne,{placeholder:a?"Để trống nếu không đổi":"Nhập mật khẩu",...S})}),e.jsx(W,{})]})]})}),e.jsx(U,{control:s,name:"confirmPassword",render:({field:S})=>e.jsxs($,{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(V,{className:"text-right text-sm",children:"Xác nhận mật khẩu *"}),e.jsxs("div",{className:"col-span-3",children:[e.jsx(O,{children:e.jsx(ne,{placeholder:a?"Để trống nếu không đổi":"Nhập lại mật khẩu",...S})}),e.jsx(W,{})]})]})}),e.jsxs("div",{className:"grid grid-cols-4 gap-2",children:[e.jsx("div",{}),e.jsxs("div",{className:"col-span-4",children:[n&&l&&n!==l&&e.jsx("p",{className:"text-sm text-red-600",children:"Mật khẩu không khớp"}),n&&n.length>0&&n.length<6&&e.jsx("p",{className:"text-sm text-red-600",children:"Mật khẩu phải chứa ít nhất 6 ký tự bao gồm chữ và số"}),h&&e.jsx("p",{className:"text-sm text-green-600",children:h})]})]}),a&&m&&e.jsxs("div",{className:"grid grid-cols-4 gap-2",children:[e.jsx("div",{}),e.jsx("div",{className:"col-span-4",children:e.jsx(E,{type:"button",onClick:x,disabled:!c,className:`${c?"bg-blue-600 text-white hover:bg-blue-700":"cursor-not-allowed bg-gray-300 text-gray-500 hover:bg-gray-300"}`,children:j.isPending?"Đang lưu...":"Lưu mật khẩu mới"})})]})]})}function Xe({selectedTables:s,storeId:a,brandId:m}){const{data:h=[]}=oe({storeUid:a}),u=w.useMemo(()=>!s.length||!h.length?[]:s.map(o=>{const n=h.find(l=>l.id===o);return n?{id:n.id,name:n.table_name,areaName:n.area.area_name}:null}).filter(Boolean).sort((o,n)=>o.areaName.localeCompare(n.areaName)),[s,h]);if(u.length===0)return null;const j=u.reduce((o,n)=>(n&&(o[n.areaName]||(o[n.areaName]=[]),o[n.areaName].push(n.name)),o),{});return e.jsx("div",{className:"space-y-3",children:e.jsx("div",{className:"rounded-md border bg-gray-50",children:Object.entries(j).map(([o,n])=>e.jsxs("div",{className:"grid grid-cols-2 border-b bg-white last:border-b-0 hover:bg-gray-50",children:[e.jsx("div",{className:"p-3 text-sm font-medium text-gray-700",children:o}),e.jsx("div",{className:"p-3 text-sm text-gray-600",children:n.join(", ")})]},o))})})}function qe({selectedTables:s,selectedStore:a,brandId:m,onShowTableSelector:h}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-base font-medium",children:"Chọn khu vực, bàn"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(Xe,{selectedTables:s,storeId:a.replace("store:",""),brandId:m}),e.jsx(E,{type:"button",variant:"outline",onClick:h,className:"w-full justify-center text-blue-600",children:"Chọn khu vực, bàn"})]})]})}function Ge({onSubmit:s,onCancel:a,isLoading:m=!1,showSaveButton:h=!0,initialData:u,isEditMode:j=!1}){const{form:o,handleSubmit:n,roles:l,rolesLoading:c,selectedRole:x,isOwner:S,needsStore:C,needsTables:d,selectedBrandAccess:t,selectedStore:r,selectedTables:f,showBrandSelector:v,setShowBrandSelector:p,showStoreSelector:y,setShowStoreSelector:T,showTableSelector:i,setShowTableSelector:g,handleBrandAccessSave:b,handleStoreSave:A,handleTablesSave:L,getBrandIdFromStore:_}=Me({initialData:u,isEditMode:j,onSubmit:s});return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mx-auto max-w-2xl",children:[h&&e.jsx("div",{className:"mb-4 flex justify-end",children:e.jsx(E,{type:"submit",form:"employee-form",disabled:m,children:m?"Đang lưu...":"Lưu"})}),e.jsx(ve,{...o,children:e.jsxs("form",{id:"employee-form",onSubmit:o.handleSubmit(n),className:"space-y-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-1 text-base font-medium",children:"Tài khoản"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Hãy sử dụng email khả dụng đối với tài khoản cho Owner, Quản lý chuỗi, IT Support, Marketing, Kế toán"})]}),e.jsx(U,{control:o.control,name:"email",render:({field:N})=>e.jsxs($,{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(V,{className:"text-right text-sm",children:"Email *"}),e.jsxs("div",{className:"col-span-3",children:[e.jsx(O,{children:e.jsx(z,{placeholder:"Nhập email nhân viên",disabled:j,...N})}),e.jsx(W,{})]})]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-base font-medium",children:"Chi tiết"}),e.jsx(U,{control:o.control,name:"full_name",render:({field:N})=>e.jsxs($,{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(V,{className:"text-right text-sm",children:"Tên nhân viên *"}),e.jsxs("div",{className:"col-span-3",children:[e.jsx(O,{children:e.jsx(z,{placeholder:"Nhập tên nhân viên",...N})}),e.jsx(W,{})]})]})}),e.jsx(U,{control:o.control,name:"phone",render:({field:N})=>e.jsxs($,{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(V,{className:"text-right text-sm",children:"Số điện thoại"}),e.jsxs("div",{className:"col-span-3",children:[e.jsx(O,{children:e.jsx(z,{placeholder:"Nhập số điện thoại",...N})}),e.jsx(W,{})]})]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-base font-medium",children:"Vị trí - Chức vụ"}),e.jsx(U,{control:o.control,name:"role_uid",render:({field:N})=>e.jsxs($,{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(V,{className:"text-right text-sm",children:"Chức vụ *"}),e.jsxs("div",{className:"col-span-3",children:[e.jsxs(be,{onValueChange:N.onChange,value:N.value,children:[e.jsx(O,{children:e.jsx(ye,{children:e.jsx(Ne,{placeholder:"Chọn chức vụ"})})}),e.jsx(we,{children:c?e.jsx(re,{disabled:!0,value:"loading",children:"Đang tải..."}):l.map(B=>e.jsx(re,{value:B.id,children:B.role_name},B.id))})]}),e.jsx(W,{})]})]})})]}),!S&&x&&e.jsx(ze,{needsStoreSelection:C,selectedStore:r,selectedBrandAccess:t,onShowStoreSelector:()=>T(!0),onShowBrandSelector:()=>p(!0),brands:u==null?void 0:u.brands,cities:u==null?void 0:u.cities,stores:u==null?void 0:u.storeDetails}),d&&r&&e.jsx(qe,{selectedTables:f,selectedStore:r,brandId:_(r),onShowTableSelector:()=>g(!0)}),e.jsx(Ke,{control:o.control,isEditMode:j,userId:u==null?void 0:u.id})]})})]}),e.jsx(Ue,{open:v,onOpenChange:p,selectedItems:t,onSave:b}),e.jsx($e,{open:y,onOpenChange:T,selectedStore:r,onSave:A}),d&&r&&e.jsx(Ve,{open:i,onOpenChange:g,selectedTables:f,onSave:L,storeId:r.replace("store:",""),brandId:_(r)})]})}function ps(){const s=le();let a;try{const r=de({from:"/_authenticated/employee/detail/$userId"});a=r==null?void 0:r.userId}catch{a=void 0}const m=!!a,h=me(),u=ue(),j=xe(),o=fe(),{data:n,isLoading:l,error:c}=pe(a||"",m),x=async r=>{try{if(m&&a){const f={...r};r.password&&(f.confirm_password=r.password),await u.mutateAsync({userId:a,params:f,userData:n}),H.success("Cập nhật nhân viên thành công!")}else{if(!r.password){H.error("Mật khẩu là bắt buộc khi tạo nhân viên mới");return}await h.mutateAsync({...r,password:r.password,confirm_password:r.password}),H.success("Tạo nhân viên thành công!")}s({to:"/employee/list"})}catch(f){H.error(q(f))}},S=async()=>{if(a)try{await j.mutateAsync(a),s({to:"/employee/list"})}catch(r){H.error(q(r))}},C=async()=>{if(a)try{await o.mutateAsync(a),s({to:"/employee/list"})}catch(r){H.error(q(r))}},d=()=>{s({to:"/employee/list"})};if(m&&l)return e.jsxs("div",{className:"min-h-screen bg-white",children:[e.jsx("div",{className:"bg-white px-4 py-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(E,{variant:"ghost",size:"icon",onClick:d,className:"h-8 w-8",children:e.jsx(J,{className:"h-4 w-4"})}),e.jsx("h1",{className:"mb-2 text-3xl font-medium",children:"Chi tiết nhân viên"}),e.jsx("div",{})]})}),e.jsx("div",{className:"px-6 py-6",children:e.jsx("div",{className:"text-center",children:"Đang tải thông tin nhân viên..."})})]});if(m&&c)return e.jsxs("div",{className:"min-h-screen bg-white",children:[e.jsx("div",{className:"bg-white px-4 py-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(E,{variant:"ghost",size:"icon",onClick:d,className:"h-8 w-8",children:e.jsx(J,{className:"h-4 w-4"})}),e.jsx("h1",{className:"mb-2 text-3xl font-medium",children:"Chi tiết nhân viên"}),e.jsx("div",{})]})}),e.jsx("div",{className:"px-6 py-6",children:e.jsx("div",{className:"text-center text-red-600",children:q(c)})})]});const t=m?u.isPending||j.isPending||o.isPending:h.isPending;return e.jsxs("div",{className:"min-h-screen bg-white",children:[e.jsx("div",{className:"bg-white px-4 py-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(E,{variant:"ghost",size:"icon",onClick:d,className:"h-8 w-8",children:e.jsx(J,{className:"h-4 w-4"})}),e.jsx("h1",{className:"mb-2 text-3xl font-medium",children:m?"Chi tiết nhân viên":"Tạo nhân viên mới"}),e.jsxs("div",{className:"flex items-center gap-3",children:[m&&n&&e.jsx(e.Fragment,{children:n.active===1?e.jsx(E,{type:"button",variant:"outline",className:"border-red-500 text-red-500 hover:bg-red-50",onClick:S,disabled:t,children:j.isPending?"Active":"Deactive"}):e.jsx(E,{type:"button",variant:"outline",className:"border-red-500 text-red-500 hover:bg-red-50",onClick:C,disabled:t,children:(o.isPending,"Active")})}),e.jsx(E,{type:"button",onClick:()=>{},children:"Lưu và đồng bộ"}),e.jsx(E,{type:"submit",form:"employee-form",disabled:t,children:t?"Đang lưu...":"Lưu"})]})]})}),e.jsx("div",{className:"px-6 py-6",children:e.jsx(Ge,{onSubmit:x,onCancel:d,isLoading:t,showSaveButton:!1,initialData:m?n:void 0,isEditMode:m},m?(n==null?void 0:n.id)||"edit":"create")})]})}export{ps as E};
