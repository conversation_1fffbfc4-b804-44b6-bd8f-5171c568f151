import{E as x}from"./exceljs.min-CLCFqwcY.js";import"./index-DZ2N7iEN.js";async function L(F,w){const{filename:E="top-promotions.xlsx"}=w,_=new x.Workbook,a=_.addWorksheet("Báo cáo doanh thu khuyến mãi"),m=new Set;F.forEach(t=>{t.list_data&&Array.isArray(t.list_data)&&t.list_data.forEach(o=>{o.date&&m.add(o.date)})});const d=Array.from(m).sort(),p=["","Tổng"],y=["CTKM","Tổng hoá đơn","Giảm giá","Doanh thu (net)"];d.forEach((t,o)=>{const e=new Date(t).toLocaleDateString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric"});p.push(e,e),y.push("Tổng hoá đơn","Giảm giá","<PERSON><PERSON><PERSON> thu (net)","Tổng hoá đơn","Giảm giá","Doanh thu (net)")});const f=4+d.length*6,C=[];for(let t=0;t<f;t++)C.push({header:"",key:`col_${t}`,width:t===0?25:15});a.columns=C;const i=a.getRow(1);i.height=30,i.font={bold:!0,color:{argb:"FFFFFF"}},i.fill={type:"pattern",pattern:"solid",fgColor:{argb:"4472C4"}},i.alignment={horizontal:"center",vertical:"middle"},i.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},p.forEach((t,o)=>{const e=i.getCell(o+1);e.value=t,o===0?(e.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF"}},e.font={bold:!0,color:{argb:"000000"}}):o===1?(e.fill={type:"pattern",pattern:"solid",fgColor:{argb:"4472C4"}},e.font={bold:!0,color:{argb:"FFFFFF"}}):(o-2)%2===0?(e.fill={type:"pattern",pattern:"solid",fgColor:{argb:"4472C4"}},e.font={bold:!0,color:{argb:"FFFFFF"}}):(e.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF8C00"}},e.font={bold:!0,color:{argb:"FFFFFF"}})}),f>=4&&a.mergeCells(1,2,1,4);let b=5;d.forEach(()=>{const t=b,o=b+5;o<=f&&a.mergeCells(1,t,1,o),b=o+1});const s=a.getRow(2);s.height=30,s.font={bold:!0,color:{argb:"000000"}},s.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF"}},s.alignment={horizontal:"center",vertical:"middle"},s.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},y.forEach((t,o)=>{const e=s.getCell(o+1);e.value=t,o===0&&(e.font={bold:!0,color:{argb:"000000"}})}),F.forEach((t,o)=>{const e={col_0:t.promotion_name};let c;t.total_bill!==void 0&&t.discount_amount!==void 0&&t.revenue_net!==void 0?c={total_bill:t.total_bill||0,discount_amount:t.discount_amount||0,revenue_net:t.revenue_net||0}:c=(t.list_data||[]).reduce((n,u)=>({total_bill:(n.total_bill||0)+(u.total_bill||0),discount_amount:(n.discount_amount||0)+(u.discount_amount||0),revenue_net:(n.revenue_net||0)+(u.revenue_net||0)}),{total_bill:0,discount_amount:0,revenue_net:0}),e.col_1=c.total_bill||0,e.col_2=c.discount_amount||0,e.col_3=c.revenue_net||0;let l=4;d.forEach((n,u)=>{const r=(t.list_data||[]).find($=>$.date===n);l+5<f&&(e[`col_${l}`]=(r==null?void 0:r.total_bill)||0,l++,e[`col_${l}`]=(r==null?void 0:r.discount_amount)||0,l++,e[`col_${l}`]=(r==null?void 0:r.revenue_net)||0,l++,e[`col_${l}`]=0,l++,e[`col_${l}`]=0,l++,e[`col_${l}`]=0,l++)});const h=a.addRow(e);h.alignment={horizontal:"center",vertical:"middle"},o%2===0?h.fill={type:"pattern",pattern:"solid",fgColor:{argb:"F8F9FA"}}:h.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF"}},h.eachCell((n,u)=>{n.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},typeof n.value=="number"&&(n.value>0?(n.numFmt="#,##0",n.font={bold:!0,color:{argb:"000000"}}):(n.numFmt="#,##0",n.font={color:{argb:"666666"}}))})});const k=await _.xlsx.writeBuffer(),R=new Blob([k],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),v=window.URL.createObjectURL(R),g=document.createElement("a");g.href=v,g.download=E,g.click(),window.URL.revokeObjectURL(v)}export{L as exportPromotionRevenueToExcel};
