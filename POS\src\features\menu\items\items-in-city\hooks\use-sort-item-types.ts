import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'
import { useCurrentBrand } from '@/stores/posStore'

import { api } from '@/lib/api/pos/pos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface SortItemTypesPayload {
  city_uid: string
  data: Array<{ id: string; sort: number }>
}

export function useSortItemTypes() {
  const queryClient = useQueryClient()
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  return useMutation({
    mutationFn: async (payload: SortItemTypesPayload) => {
      if (!company?.id || !selectedBrand?.id) {
        toast.error('Thiếu thông tin công ty hoặc thương hiệu')
        return
      }

      const body = {
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        city_uid: payload.city_uid,
        data: payload.data
      }

      await api.put('/mdata/v1/item-types/sort', body)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEM_TYPES] })
      toast.success('Sắp xếp nhóm món thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi sắp xếp nhóm món: ${error?.message}`)
    }
  })
}
