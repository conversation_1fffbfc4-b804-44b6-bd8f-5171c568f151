import{j as r,O as t}from"./index-D0Grd55b.js";import{C as i}from"./index-BNJAlodD.js";import"./date-range-picker-CruKYeHR.js";import"./search-context-CjM0jrYw.js";import"./pos-api-B09qRspF.js";import"./form-Bk1C9kLO.js";import"./main-Czv3HpP4.js";import"./index-DBt5Jghn.js";import"./calendar-5lpy20z0.js";import"./createLucideIcon-DNzDbUBG.js";import"./index-CW7Xpojs.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-D2H_xC9V.js";import"./react-icons.esm-BOnXQgjl.js";import"./popover-CMTiAV3j.js";import"./select-DBO-8fSu.js";import"./index-CI2TkimM.js";import"./index-C-UyCxtf.js";import"./check-TFQPNqMS.js";import"./command-Bcq7GTcy.js";import"./dialog-C8IVKkOo.js";import"./search-C4dfJjaw.js";import"./createReactComponent-eJgt86Cn.js";import"./scroll-area-r2ikcXUQ.js";import"./IconChevronRight-DnVUSvDn.js";const o="...",K=function(){return r.jsx(i,{publishableKey:o,afterSignOutUrl:"/clerk/sign-in",signInUrl:"/clerk/sign-in",signUpUrl:"/clerk/sign-up",signInFallbackRedirectUrl:"/clerk/user-management",signUpFallbackRedirectUrl:"/clerk/user-management",children:r.jsx(t,{})})};export{K as component};
