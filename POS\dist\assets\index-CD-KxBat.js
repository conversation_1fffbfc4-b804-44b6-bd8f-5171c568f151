import{a3 as ze,u as ae,l as Ke,a4 as B,r as j,h as Le,j as e,B as P,c as Pe,T as lt,o as ct,p as ot,q as dt,R as Oe}from"./index-D0Grd55b.js";import{b as He}from"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import{u as mt}from"./use-customizations-DtrTtBD6.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import{H as ht}from"./header-yApU5MZq.js";import{M as ut}from"./main-Czv3HpP4.js";import{P as xt}from"./profile-dropdown-dGP8eyih.js";import{S as pt,T as ft}from"./search-BEMocVbv.js";import{u as ge,a as _t,b as gt,c as yt,g as Be,d as Ue,e as jt,f as bt,h as wt,i as Nt,I as vt,j as Ct,k as St,l as kt,C as It,B as Tt}from"./customization-dialog-87EDwGFq.js";import{E as Dt}from"./exceljs.min-BlLyHhym.js";import{h as Et,x as Se,G as ke,H as Ft,J as Mt,K as Pt,i as Ot,A as Bt,a as Rt,C as Vt,B as At}from"./react-icons.esm-BOnXQgjl.js";import{D as zt,a as Kt,b as Lt,c as ee}from"./dropdown-menu-BvqrmFsX.js";import{D as q}from"./data-table-column-header-CWPVxhJt.js";import{B as Re}from"./badge-DoEZpO1Y.js";import{S as Ht}from"./status-badge-D9DmlS5I.js";import"./date-range-picker-CruKYeHR.js";import"./form-Bk1C9kLO.js";import{C as Ve}from"./checkbox-C_eqgJmW.js";import{S as pe}from"./settings-FfwbJ8zD.js";import{I as Ut}from"./IconCopy-9PsX5jxY.js";import{I as qt}from"./IconTrash-avbXk0dh.js";import{u as qe,g as Gt,a as $t,b as Wt,d as Qt,e as Ge,f as fe}from"./index-CwC79dMR.js";import{S as ie,a as $}from"./scroll-area-r2ikcXUQ.js";import{T as ye,a as je,b as G,c as te,d as be,e as W}from"./table-CLNYB6yq.js";import{C as $e}from"./confirm-dialog-BctmuKmX.js";import{a as Xt,C as Yt}from"./chevron-right-D2H_xC9V.js";import{i as Jt,u as ne}from"./use-item-types-CuyGAtwR.js";import{u as re}from"./use-removed-items-7Ty-LinJ.js";import{I as Zt}from"./input-C-0UnKOB.js";import{S as oe,a as de,b as me,c as he,d as Q}from"./select-DBO-8fSu.js";import{M as es}from"./multi-select-OhaHZZzq.js";import{T as Ie}from"./trash-2-D2PaDI_I.js";import{I as ts}from"./IconFilter-ClC5QT7u.js";import{X as ss}from"./calendar-5lpy20z0.js";import{S as I}from"./skeleton-zUnEohn-.js";import{b as We,c as _e,K as Qe,P as Xe,D as Ye,d as Je,C as Ze}from"./core.esm-ZfuNvihZ.js";import{s as et,S as tt,v as as,a as st,u as at,r as is}from"./sortable.esm-B4HpXahQ.js";import{read as Te,utils as De}from"./xlsx-DkH2s96g.js";import{u as we}from"./use-item-classes-s7fFgXey.js";import{u as Ne}from"./use-units-ByB1meVo.js";import{D as X,a as Y,b as J,c as Z}from"./dialog-C8IVKkOo.js";import{C as Ce}from"./combobox-NkstXop_.js";import{u as Ee}from"./useQuery-Ck3BpOfq.js";import{u as it}from"./useMutation-ATsU-ht7.js";import{i as ns}from"./item-api-1Lw2G9JI.js";import{s as rs}from"./sources-api-DecL-FPl.js";import{Q as se}from"./query-keys-3lmd-xp6.js";import{D as ls}from"./download-25JBGwPO.js";import{U as cs}from"./upload-9hlzyje0.js";import{u as os}from"./use-pos-cities-data-BBSMUcaZ.js";import{I as ds,a as ms}from"./IconPinned-DTOrhMHj.js";import"./separator-CiUiq7rT.js";import"./avatar-C7dnn6zI.js";import"./search-context-CjM0jrYw.js";import"./command-Bcq7GTcy.js";import"./search-C4dfJjaw.js";import"./createLucideIcon-DNzDbUBG.js";import"./createReactComponent-eJgt86Cn.js";import"./IconChevronRight-DnVUSvDn.js";import"./IconSearch-ClUZlEwa.js";import"./use-dialog-state-Bi2BGkq6.js";import"./modal-hDollbt0.js";import"./zod-G2vIgQkk.js";import"./index-CI2TkimM.js";import"./index-CW7Xpojs.js";import"./index-DxQNaO1C.js";import"./check-TFQPNqMS.js";import"./popover-CMTiAV3j.js";import"./isSameMonth-C8JQo-AN.js";import"./index-C-UyCxtf.js";import"./alert-dialog-B23_tn7-.js";import"./circle-x-Izs54kGD.js";import"./chevrons-up-down-BCCoC0zx.js";import"./utils-km2FGkQ4.js";import"./sources-CfiQ7039.js";const Fe=[{sourceId:"10000045",name:"ZALO"},{sourceId:"10000049",name:"FACEBOOK"},{sourceId:"10000134",name:"SO"},{sourceId:"10000162",name:"CRM"},{sourceId:"10000165",name:"VNPAY"},{sourceId:"10000168",name:"GOJEK (GOVIET)"},{sourceId:"10000169",name:"ShopeeFood"},{sourceId:"10000171",name:"MANG VỀ"},{sourceId:"10000172",name:"TẠI CHỖ"},{sourceId:"10000176",name:"CALL CENTER"},{sourceId:"10000216",name:"O2O"},{sourceId:"10000253",name:"BEFOOD"}],hs=()=>{const n=new Dt.Workbook;return n.creator="POS System",n.lastModifiedBy="POS System",n.created=new Date,n.modified=new Date,n},us=()=>{const n=["item_uid","item_id","item_name","Nhóm món","Giá gốc","Vat (%)"],t=Fe.map(s=>`${s.name} [${s.sourceId}]`);return[...n,...t]},xs=(n,t)=>{const i=n.map(d=>{const x=t.find(y=>y.id===d.item_type_uid),h=(x==null?void 0:x.item_type_name)||"Uncategory";return{item:d,itemTypeName:h}}).reduce((d,{item:x,itemTypeName:h})=>(d[h]||(d[h]=[]),d[h].push({item:x,itemTypeName:h}),d),{}),a=Object.keys(i).sort(),c=[];return a.forEach(d=>{const x=i[d];x.sort((h,y)=>h.item.item_name.localeCompare(y.item.item_name)),x.forEach(({item:h,itemTypeName:y})=>{const b=[h.id,h.item_id,h.item_name,y,h.ots_price||0,(h.ots_tax||0)*100];Fe.forEach(S=>{var g,v;const w=(v=(g=h.extra_data)==null?void 0:g.price_by_source)==null?void 0:v.find(p=>p.source_id===S.sourceId);b.push((w==null?void 0:w.price)||"")}),c.push(b)})}),c},ps=(n,t,s)=>{const i=n.addWorksheet("Sheet"),a=us(),c=xs(t,s);return i.addRow(a).eachCell(h=>{h.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},h.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},h.alignment={horizontal:"center",vertical:"middle"},h.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),c.forEach(h=>{i.addRow(h).eachCell(b=>{b.font={size:10},b.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),[40,15,25,15,12,10,...Fe.map(()=>15)].forEach((h,y)=>{i.getColumn(y+1).width=h}),i},fs=async(n,t,s)=>{try{const i=hs();ps(i,n,t);const a=await i.xlsx.writeBuffer(),c=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),d=new Date().toISOString().slice(0,19).replace(/:/g,"-"),x=`import-price-by-source_${s}_${d}.xlsx`,h=window.URL.createObjectURL(c),y=document.createElement("a");return y.href=h,y.download=x,document.body.appendChild(y),y.click(),document.body.removeChild(y),window.URL.revokeObjectURL(h),Promise.resolve()}catch(i){return console.error("Error creating price by source Excel file:",i),Promise.reject(i)}},_s=["item_uid","item_id","item_name","Nhóm món","Giá gốc","Vat (%)","ZALO [10000045]","FACEBOOK [10000049]","SO [10000134]","CRM [10000162]","VNPAY [10000165]","GOJEK (GOVIET) [10000168]","ShopeeFood [10000169]","MANG VỀ [10000171]","TẠI CHỖ [10000172]","CALL CENTER [10000176]","O2O [10000216]","BEFOOD [10000253]"],gs=async n=>new Promise((t,s)=>{const i=new FileReader;i.onload=a=>{var c;try{const d=(c=a.target)==null?void 0:c.result;if(!d){s(new Error("Không thể đọc file"));return}const x=Te(d,{type:"array"}),h=x.SheetNames[0];if(!h){s(new Error("File Excel không có sheet nào"));return}const y=x.Sheets[h],b=De.sheet_to_json(y,{header:1});if(b.length<2){s(new Error("File Excel không có dữ liệu"));return}const S=b[0],w=_s.filter(v=>!S.includes(v));w.length>0&&console.warn("Missing headers:",w);const g=[];for(let v=1;v<b.length;v++){const p=b[v];if(!p||p.length===0)continue;const N={item_uid:"",item_id:"",item_name:"",item_type_name:"",ots_price:0,ots_tax:0};S.forEach((k,E)=>{const f=p[E];switch(k){case"item_uid":N.item_uid=String(f||"");break;case"item_id":N.item_id=String(f||"");break;case"item_name":N.item_name=String(f||"");break;case"Nhóm món":N.item_type_name=String(f||"");break;case"Giá gốc":N.ots_price=parseFloat(String(f||"0"))||0;break;case"Vat (%)":N.ots_tax=parseFloat(String(f||"0"))||0;break;case"ZALO [10000045]":case"FACEBOOK [10000049]":case"SO [10000134]":case"CRM [10000162]":case"VNPAY [10000165]":case"GOJEK (GOVIET) [10000168]":case"ShopeeFood [10000169]":case"MANG VỀ [10000171]":case"TẠI CHỖ [10000172]":case"CALL CENTER [10000176]":case"O2O [10000216]":case"BEFOOD [10000253]":if(f!=null&&f!==""){const l=parseFloat(String(f));isNaN(l)||(N[k]=l)}break;default:f!=null&&(N[k]=f);break}}),N.item_uid&&N.item_id&&N.item_name&&g.push(N)}console.log("📊 Parsed Excel data:",{totalRows:b.length-1,validItems:g.length,headers:S,sampleItem:g[0]}),t(g)}catch(d){console.error("Error parsing Excel file:",d),s(new Error("Có lỗi xảy ra khi đọc file Excel"))}},i.onerror=()=>{s(new Error("Có lỗi xảy ra khi đọc file"))},i.readAsArrayBuffer(n)}),ys=n=>{if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","application/octet-stream"].includes(n.type)&&!n.name.match(/\.(xlsx|xls)$/i))return{isValid:!1,error:"File phải có định dạng Excel (.xlsx hoặc .xls)"};const s=10*1024*1024;return n.size>s?{isValid:!1,error:"File không được vượt quá 10MB"}:{isValid:!0}};function js(){const n=ze(),{company:t}=ae(i=>i.auth),{selectedBrand:s}=Ke();return it({mutationFn:async i=>{if(!(t!=null&&t.id)||!(s!=null&&s.id)){B.error("Thiếu thông tin công ty hoặc thương hiệu");return}const a={company_uid:t.id,brand_uid:s.id,city_uid:i.city_uid,data:i.data};await He.put("/mdata/v1/item-types/sort",a)},onSuccess:()=>{n.invalidateQueries({queryKey:[se.ITEM_TYPES]}),B.success("Sắp xếp nhóm món thành công!")},onError:i=>{B.error(`Lỗi khi sắp xếp nhóm món: ${i==null?void 0:i.message}`)}})}function bs(){const[n,t]=j.useState(!1),[s,i]=j.useState(!1),[a,c]=j.useState(null),[d,x]=j.useState(!1),[h,y]=j.useState([]),[b,S]=j.useState("all"),[w,g]=j.useState("all"),[v,p]=j.useState([]),[N,k]=j.useState("all");return{isCustomizationDialogOpen:n,isBuffetItem:s,selectedMenuItem:a,isBuffetConfigModalOpen:d,selectedBuffetMenuItem:h,selectedItemTypeUid:b,selectedCityUid:w,selectedDaysOfWeek:v,selectedStatus:N,setIsCustomizationDialogOpen:t,setIsBuffetItem:i,setSelectedMenuItem:c,setIsBuffetConfigModalOpen:x,setSelectedBuffetMenuItem:y,setSelectedItemTypeUid:S,setSelectedCityUid:g,setSelectedDaysOfWeek:p,setSelectedStatus:k}}const ws=(n,t=!0)=>{const{company:s,brands:i}=ae(c=>c.auth),a=i==null?void 0:i[0];return Ee({queryKey:[se.ITEMS_LIST,"price-by-source",n],queryFn:async()=>{const c={company_uid:(s==null?void 0:s.id)||"",brand_uid:(a==null?void 0:a.id)||"",city_uid:n,skip_limit:!0,active:1};return(await ns.getItems(c)).data||[]},enabled:t&&!!(s!=null&&s.id&&(a!=null&&a.id)&&n),staleTime:5*60*1e3,gcTime:10*60*1e3})},Ns=(n,t=!0)=>{const{company:s,brands:i}=ae(c=>c.auth),a=i==null?void 0:i[0];return Ee({queryKey:[se.SOURCES,"price-by-source",n],queryFn:async()=>{const c={company_uid:(s==null?void 0:s.id)||"",brand_uid:(a==null?void 0:a.id)||"",city_uid:n,skip_limit:!0};return await rs.getSources(c)||[]},enabled:t&&!!(s!=null&&s.id&&(a!=null&&a.id)&&n),staleTime:5*60*1e3,gcTime:10*60*1e3})},vs=(n,t=!0)=>{const{company:s,brands:i}=ae(c=>c.auth),a=i==null?void 0:i[0];return Ee({queryKey:[se.ITEM_TYPES,"price-by-source",n],queryFn:async()=>{const c={company_uid:(s==null?void 0:s.id)||"",brand_uid:(a==null?void 0:a.id)||"",city_uid:n,skip_limit:!0,active:1};return(await Jt.getItemTypes(c)).data||[]},enabled:t&&!!(s!=null&&s.id&&(a!=null&&a.id)&&n),staleTime:5*60*1e3,gcTime:10*60*1e3})},Cs=(n,t=!0)=>{const s=ws(n,t),i=Ns(n,t),a=vs(n,t);return{items:s.data||[],sources:i.data||[],itemTypes:a.data||[],isLoading:s.isLoading||i.isLoading||a.isLoading,isError:s.isError||i.isError||a.isError,error:s.error||i.error||a.error,refetch:()=>{s.refetch(),i.refetch(),a.refetch()}}},Ss=(n,t,s)=>n.map(i=>{const a=t.find(x=>x.id===i.item_uid);if(!a)throw new Error(`Original item not found for ${i.item_uid}`);const c=[],d={};return s.forEach(x=>{const h=`${x.sourceName} [${x.sourceId}]`;d[h]=x.sourceId}),Object.entries(d).forEach(([x,h])=>{const y=i[x];if(y!=null&&y!==""){const b=typeof y=="string"?parseFloat(y):y;!isNaN(b)&&b>0&&c.push({source_id:h,price:b})}}),{...a,ots_price:i.ots_price,ots_tax:i.ots_tax/100,ta_price:i.ots_price,ta_tax:i.ots_tax/100,extra_data:{...a.extra_data,price_by_source:c}}}),ks=()=>{const n=ze(),{mutate:t,isPending:s}=it({mutationFn:async i=>{const a=Ss(i.previewItems,i.originalItems,i.sources),c=await He.put("/mdata/v1/items",a);return c.data.data||c.data},onSuccess:()=>{n.invalidateQueries({queryKey:[se.ITEMS_LIST],refetchType:"none"}),setTimeout(()=>{n.refetchQueries({queryKey:[se.ITEMS_LIST]})},100),B.success("Đã cập nhật cấu hình giá theo nguồn thành công!")},onError:i=>{B.error(`Có lỗi xảy ra khi lưu cấu hình: ${i.message}`)}});return{bulkUpdatePriceBySource:t,isUpdating:s}};function Is(){const{setOpen:n}=ge(),t=Le(),s=()=>{t({to:"/menu/items/items-in-city/detail"})},i=()=>{n("export-dialog")},a=()=>{n("import")},c=()=>{n("price-by-source-config")},d=()=>{n("sort-menu")},x=()=>{},h=()=>{};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(zt,{children:[e.jsx(Kt,{asChild:!0,children:e.jsxs(P,{variant:"outline",size:"sm",children:["Tiện ích",e.jsx(Et,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(Lt,{align:"end",className:"w-56",children:[e.jsxs(ee,{onClick:i,children:[e.jsx(Se,{className:"mr-2 h-4 w-4"}),"Xuất, sửa thực đơn"]}),e.jsxs(ee,{onClick:a,children:[e.jsx(ke,{className:"mr-2 h-4 w-4"}),"Thêm món từ file"]}),e.jsxs(ee,{onClick:c,children:[e.jsx(Ft,{className:"mr-2 h-4 w-4"}),"Cấu hình giá theo nguồn"]}),e.jsxs(ee,{onClick:d,children:[e.jsx(Mt,{className:"mr-2 h-4 w-4"}),"Sắp xếp thực đơn"]}),e.jsxs(ee,{onClick:x,children:[e.jsx(Pt,{className:"mr-2 h-4 w-4"}),"Sao chép thực đơn"]}),e.jsxs(ee,{onClick:h,children:[e.jsx(Ot,{className:"mr-2 h-4 w-4"}),"Cấu hình khung thời gian"]})]})]}),e.jsx(P,{variant:"default",size:"sm",onClick:s,children:"Tạo món"})]})})}function Ae({column:n,title:t,className:s,defaultSort:i="desc"}){if(!n.getCanSort())return e.jsx("div",{className:Pe(s),children:t});const a=()=>{const c=n.getIsSorted();c?c==="desc"?n.toggleSorting(!1):n.toggleSorting(!0):n.toggleSorting(i==="desc")};return e.jsx("div",{className:Pe("flex items-center space-x-2",s),children:e.jsxs(P,{variant:"ghost",size:"sm",className:"-ml-3 h-8 hover:bg-accent",onClick:a,children:[e.jsx("span",{children:t}),n.getIsSorted()==="desc"?e.jsx(Bt,{className:"ml-2 h-4 w-4"}):n.getIsSorted()==="asc"?e.jsx(Rt,{className:"ml-2 h-4 w-4"}):e.jsx(Vt,{className:"ml-2 h-4 w-4"})]})})}const Ts=({onBuffetConfigClick:n})=>[{id:"select",header:({table:t})=>e.jsx(Ve,{checked:t.getIsAllPageRowsSelected(),onCheckedChange:s=>t.toggleAllPageRowsSelected(!!s),"aria-label":"Select all"}),cell:({row:t})=>e.jsx(Ve,{checked:t.getIsSelected(),onCheckedChange:s=>t.toggleSelected(!!s),"aria-label":"Select row",onClick:s=>s.stopPropagation()}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:t})=>e.jsx("div",{className:"w-[50px]",children:t.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"code",header:({column:t})=>e.jsx(q,{column:t,title:"Mã món"}),cell:({row:t})=>e.jsx("div",{className:"text-sm font-medium",children:t.getValue("code")}),enableSorting:!1,enableHiding:!0},{accessorKey:"name",header:({column:t})=>e.jsx(q,{column:t,title:"Tên món"}),cell:({row:t})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm font-medium",children:t.getValue("name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"price",header:({column:t})=>e.jsx(q,{column:t,title:"Giá"}),cell:({row:t})=>{const s=t.getValue("price");return e.jsx("div",{className:"text-sm font-medium",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(s)})},enableSorting:!1,enableHiding:!0},{accessorKey:"vatPercent",header:({column:t})=>e.jsx(q,{column:t,title:"VAT (%)"}),cell:({row:t})=>{const s=t.getValue("vatPercent");return e.jsx("div",{className:"text-right text-sm",children:s*100})},enableSorting:!1,enableHiding:!0},{accessorKey:"itemType",header:({column:t})=>e.jsx(q,{column:t,title:"Nhóm món"}),cell:({row:t})=>e.jsx(Re,{variant:"outline",className:"text-xs",children:t.getValue("itemType")}),enableSorting:!1,enableHiding:!0},{accessorKey:"itemClass",header:({column:t})=>e.jsx(q,{column:t,title:"Loại món"}),cell:({row:t})=>t.getValue("itemClass")&&e.jsx(Re,{variant:"outline",className:"text-center text-xs",children:t.getValue("itemClass")}),enableSorting:!1,enableHiding:!0},{accessorKey:"unit",header:({column:t})=>e.jsx(q,{column:t,title:"Đơn vị tính"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("unit")}),enableSorting:!1,enableHiding:!0},{accessorKey:"sideItems",header:({column:t})=>e.jsx(Ae,{column:t,title:"Món ăn kèm",defaultSort:"desc"}),cell:({row:t})=>{const s=t.getValue("sideItems");if(!s)return e.jsx("div",{children:"Món chính"});const i=s==="Món ăn kèm"?"Món ăn kèm":s;return e.jsx(lt,{children:e.jsxs(ct,{children:[e.jsx(ot,{asChild:!0,children:e.jsx("div",{className:"max-w-[120px] cursor-help truncate text-sm",children:i})}),e.jsx(dt,{children:e.jsx("p",{className:"max-w-[300px]",children:i})})]})})},enableSorting:!0,enableHiding:!0},{accessorKey:"city",header:({column:t})=>e.jsx(q,{column:t,title:"Thành phố"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("city")}),enableSorting:!1,enableHiding:!0},{accessorKey:"buffetConfig",header:({column:t})=>e.jsx(q,{column:t,title:"Cấu hình buffet"}),cell:({row:t})=>{var a;const s=t.original;return((a=s.extra_data)==null?void 0:a.is_buffet_item)===1?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Đã cấu hình"}),e.jsx(P,{variant:"outline",size:"sm",onClick:()=>n(s),className:"h-6 px-2 text-xs",children:e.jsx(pe,{className:"h-3 w-3"})})]}):e.jsxs(P,{variant:"outline",size:"sm",onClick:()=>n(s),className:"h-7 px-2 text-xs",children:[e.jsx(pe,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{accessorKey:"customization",header:({column:t})=>e.jsx(q,{column:t,title:"Customization"}),cell:({row:t,table:s})=>{var x;const i=t.original,a=s.options.meta,c=i.customization_uid,d=(x=a==null?void 0:a.customizations)==null?void 0:x.find(h=>h.id===c);return d?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:d.name}),e.jsx(P,{variant:"outline",size:"sm",onClick:()=>{var h;return(h=a==null?void 0:a.onCustomizationClick)==null?void 0:h.call(a,i)},className:"h-6 px-2 text-xs",children:e.jsx(pe,{className:"h-3 w-3"})})]}):e.jsxs(P,{variant:"outline",size:"sm",onClick:()=>{var h;return(h=a==null?void 0:a.onCustomizationClick)==null?void 0:h.call(a,i)},className:"h-7 px-2 text-xs",children:[e.jsx(pe,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{id:"copy",header:"Sao chép tạo món mới",cell:({row:t,table:s})=>{const i=t.original,a=s.options.meta;return e.jsxs(P,{variant:"ghost",size:"sm",className:"ml-14 h-8 w-8",onClick:c=>{var d;c.stopPropagation(),(d=a==null?void 0:a.onCopyClick)==null||d.call(a,i)},children:[e.jsx(Ut,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép thiết bị ",i.item_name]})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"isActive",header:({column:t})=>e.jsx(Ae,{column:t,title:"Thao tác",defaultSort:"desc"}),enableSorting:!0,cell:({row:t,table:s})=>{const i=t.original,a=t.getValue("isActive"),c=s.options.meta;return e.jsx("div",{onClick:d=>{var x;d.stopPropagation(),(x=c==null?void 0:c.onToggleStatus)==null||x.call(c,i)},className:"cursor-pointer",children:e.jsx(Ht,{isActive:a,activeText:"Active",inactiveText:"Deactive"})})},enableHiding:!0},{id:"actions",cell:({row:t,table:s})=>{const i=t.original,a=s.options.meta;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs(P,{variant:"ghost",size:"sm",onClick:c=>{var d;c.stopPropagation(),(d=a==null?void 0:a.onDeleteClick)==null||d.call(a,i)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(qt,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa món ",i.item_name]})]})})},enableSorting:!1,enableHiding:!1,size:80}],Ds=Ts;function Es({currentPage:n,onPageChange:t,hasNextPage:s}){const i=()=>{n>1&&t(n-1)},a=()=>{s&&t(n+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(P,{variant:"outline",size:"sm",onClick:i,disabled:n===1,className:"flex items-center gap-2",children:[e.jsx(Xt,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:n}),e.jsxs(P,{variant:"outline",size:"sm",onClick:a,disabled:!s,className:"flex items-center gap-2",children:["Sau",e.jsx(Yt,{className:"h-4 w-4"})]})]})}const Fs=[{label:"Thứ 2",value:"2"},{label:"Thứ 3",value:"3"},{label:"Thứ 4",value:"4"},{label:"Thứ 5",value:"5"},{label:"Thứ 6",value:"6"},{label:"Thứ 7",value:"7"},{label:"Chủ Nhật",value:"1"}],Ms=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}];function Ps({table:n,selectedItemTypeUid:t="all",onItemTypeChange:s,selectedCityUid:i="all",onCityChange:a,selectedDaysOfWeek:c=[],onDaysOfWeekChange:d,selectedStatus:x="all",onStatusChange:h,onDeleteSelected:y}){var f;const[b,S]=j.useState(!1),{data:w=[]}=ne(),{data:g=[]}=re(),v=g.filter(l=>l.active===1),p=v.map(l=>({label:l.city_name,value:l.id})),N=v.map(l=>l.id).join(",");j.useEffect(()=>{i==="all"&&N&&a&&a(N)},[i,N,a]);const k=n.getState().columnFilters.length>0,E=n.getFilteredSelectedRowModel().rows.length;return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[E>0&&e.jsxs(P,{variant:"destructive",size:"sm",onClick:y,className:"h-9",children:[e.jsx(Ie,{}),"Xóa món (",E,")"]}),e.jsx(Zt,{placeholder:"Tìm kiếm món ăn...",value:((f=n.getColumn("name"))==null?void 0:f.getFilterValue())??"",onChange:l=>{var C;return(C=n.getColumn("name"))==null?void 0:C.setFilterValue(l.target.value)},className:"h-9 w-[150px] lg:w-[250px]"}),e.jsxs(oe,{value:i,onValueChange:l=>{a&&a(l)},children:[e.jsx(de,{className:"h-10 w-[180px]",children:e.jsx(me,{placeholder:"Chọn thành phố"})}),e.jsxs(he,{children:[e.jsx(Q,{value:N,children:"Tất cả thành phố"}),p.map(l=>e.jsx(Q,{value:l.value,children:l.label},l.value))]})]}),e.jsxs(oe,{value:x,onValueChange:h,children:[e.jsx(de,{className:"h-10 w-[180px]",children:e.jsx(me,{placeholder:"Chọn Trạng thái"})}),e.jsx(he,{children:Ms.map(l=>e.jsx(Q,{value:l.value,children:l.label},l.value))})]}),e.jsxs(P,{variant:"outline",size:"sm",onClick:()=>S(!b),className:"h-9",children:[e.jsx(ts,{className:"h-4 w-4"}),"Nâng cao"]}),k&&e.jsxs(P,{variant:"ghost",onClick:()=>n.resetColumnFilters(),className:"h-10 px-2 lg:px-3",children:["Reset",e.jsx(ss,{className:"ml-2 h-4 w-4"})]})]})}),b&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(oe,{value:t,onValueChange:s,children:[e.jsx(de,{className:"h-10 w-[180px]",children:e.jsx(me,{placeholder:"Chọn loại món"})}),e.jsxs(he,{children:[e.jsx(Q,{value:"all",children:"Tất cả nhóm món"}),w.filter(l=>l.active===1).map(l=>({label:l.item_type_name,value:l.id})).map(l=>e.jsx(Q,{value:l.value,children:l.label},l.value))]})]}),e.jsx(es,{options:Fs,value:c,onValueChange:d||(()=>{}),placeholder:"Chọn ngày trong tuần",className:"min-h-9 w-[300px]",maxCount:1})]})]})}function Os({columns:n,data:t,onCustomizationClick:s,onCopyClick:i,onToggleStatus:a,onRowClick:c,onDeleteClick:d,customizations:x,selectedItemTypeUid:h,onItemTypeChange:y,selectedCityUid:b,onCityChange:S,selectedDaysOfWeek:w,onDaysOfWeekChange:g,selectedStatus:v,onStatusChange:p,hasNextPageOverride:N,currentPage:k,onPageChange:E}){var H;const[f,l]=j.useState({}),[C,_]=j.useState({}),[O,r]=j.useState([]),[T,m]=j.useState([]),[u,o]=j.useState(!1),{deleteMultipleItemsAsync:D}=_t(),V=()=>{o(!0)},M=async()=>{try{const K=A.getFilteredSelectedRowModel().rows.map(U=>U.original.id);await D(K),o(!1),A.resetRowSelection()}catch{}},R=(F,K)=>{const U=K.target;U.closest('input[type="checkbox"]')||U.closest("button")||U.closest('[role="button"]')||U.closest(".badge")||U.tagName==="BUTTON"||c==null||c(F)},A=qe({data:t,columns:n,state:{sorting:T,columnVisibility:C,rowSelection:f,columnFilters:O},enableRowSelection:!0,onRowSelectionChange:l,onSortingChange:m,onColumnFiltersChange:r,onColumnVisibilityChange:_,getCoreRowModel:Ge(),getFilteredRowModel:Qt(),getSortedRowModel:Wt(),getFacetedRowModel:$t(),getFacetedUniqueValues:Gt(),meta:{onCustomizationClick:s,onCopyClick:i,onToggleStatus:a,onDeleteClick:d,customizations:x}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(Ps,{table:A,selectedItemTypeUid:h,onItemTypeChange:y,selectedCityUid:b,onCityChange:S,selectedDaysOfWeek:w,onDaysOfWeekChange:g,selectedStatus:v,onStatusChange:p,onDeleteSelected:V}),e.jsxs(ie,{className:"rounded-md border",children:[e.jsxs(ye,{className:"relative",children:[e.jsx(je,{children:A.getHeaderGroups().map(F=>e.jsx(G,{children:F.headers.map(K=>e.jsx(te,{colSpan:K.colSpan,children:K.isPlaceholder?null:fe(K.column.columnDef.header,K.getContext())},K.id))},F.id))}),e.jsx(be,{children:(H=A.getRowModel().rows)!=null&&H.length?A.getRowModel().rows.map(F=>e.jsx(G,{"data-state":F.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:K=>R(F.original,K),children:F.getVisibleCells().map(K=>e.jsx(W,{children:fe(K.column.columnDef.cell,K.getContext())},K.id))},F.id)):e.jsx(G,{children:e.jsx(W,{colSpan:n.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx($,{orientation:"horizontal"})]}),e.jsx(Es,{currentPage:k??1,onPageChange:F=>E&&E(F),hasNextPage:!!N}),e.jsx($e,{open:u,onOpenChange:o,title:`Bạn có chắc muốn xóa ${A.getFilteredSelectedRowModel().rows.length} món đã chọn`,desc:"Hành động này không thể hoàn tác.",confirmText:"Xóa",cancelBtnText:"Hủy",className:"top-[30%] translate-y-[-50%]",handleConfirm:M,destructive:!0})]})}function Bs(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(I,{className:"h-8 w-[250px]"}),e.jsx(I,{className:"h-8 w-[100px]"}),e.jsx(I,{className:"h-8 w-[100px]"}),e.jsx(I,{className:"h-8 w-[100px]"})]}),e.jsx(I,{className:"h-8 w-[100px]"})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsx("div",{className:"border-b p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(I,{className:"h-4 w-8"}),e.jsx(I,{className:"h-4 w-20"}),e.jsx(I,{className:"h-4 w-32"}),e.jsx(I,{className:"h-4 w-20"}),e.jsx(I,{className:"h-4 w-16"}),e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-4 w-20"}),e.jsx(I,{className:"h-4 w-16"}),e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-4 w-20"}),e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-4 w-16"})]})}),Array.from({length:10}).map((n,t)=>e.jsx("div",{className:"border-b p-4 last:border-b-0",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(I,{className:"h-4 w-8"}),e.jsx(I,{className:"h-4 w-20"}),e.jsx(I,{className:"h-4 w-32"}),e.jsx(I,{className:"h-4 w-20"}),e.jsx(I,{className:"h-4 w-16"}),e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-4 w-20"}),e.jsx(I,{className:"h-4 w-16"}),e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-4 w-20"}),e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-4 w-16"})]})},t))]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(I,{className:"h-8 w-[200px]"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(I,{className:"h-8 w-[100px]"}),e.jsx(I,{className:"h-8 w-8"}),e.jsx(I,{className:"h-8 w-8"})]})]})]})}function Rs({itemTypes:n,selectedCityUid:t,selectedItemTypeUid:s,onSelect:i,className:a,onOrderChange:c,onOrderChangeDetailed:d}){const x=We(_e(Xe,{activationConstraint:{distance:6}}),_e(Qe,{coordinateGetter:et})),[h,y]=j.useState([]);j.useEffect(()=>{n&&n.length>0?y(n.map(w=>({id:w.id,item_type_name:w.item_type_name,city_uid:w.city_uid??null}))):y([])},[n]);const b=j.useMemo(()=>h.map(w=>w.id),[h]),S=w=>{const{active:g,over:v}=w;!v||g.id===v.id||y(p=>{const N=p.findIndex(m=>m.id===String(g.id)),k=p.findIndex(m=>m.id===String(v.id));if(N===-1||k===-1)return p;const E=m=>{var u;return((u=p[m])==null?void 0:u.city_uid)===t};if(!E(N))return p;const f=p.map((m,u)=>u).filter(m=>E(m));if(f.length<=1)return p;const l=f.indexOf(N);if(l===-1)return p;let C=f.findIndex(m=>m>=k);if(C===-1&&(C=f.length-1),!E(k)){let m=-1;for(let o=f.length-1;o>=0;o--)if(f[o]<k){m=o;break}const u=f.findIndex(o=>o>k);if(m===-1&&u===-1)return p;if(m===-1)C=u;else if(u===-1)C=m;else{const o=Math.abs(k-f[m]),D=Math.abs(f[u]-k);C=o<=D?m:u}}if(l===C)return p;const _=f.map(m=>p[m]),O=st(_,l,C),r=p.slice();let T=0;for(let m=0;m<r.length;m++)E(m)&&(r[m]=O[T++]);return c&&c(r.map(m=>m.id)),d&&d(r),r})};return e.jsx("div",{className:a,children:e.jsxs(ie,{className:"h-[50vh] w-full",children:[e.jsx(Ye,{sensors:x,collisionDetection:Je,onDragEnd:S,children:e.jsx(tt,{items:b,strategy:as,children:e.jsx("div",{className:"space-y-1 p-2",children:h.map(w=>e.jsx(Vs,{itemType:w,selectedCityUid:t,isSelected:s===w.id,onSelect:()=>i(w.id)},w.id))})})}),e.jsx($,{orientation:"vertical"})]})})}function Vs({itemType:n,selectedCityUid:t,isSelected:s,onSelect:i}){const a=n.city_uid===t,{attributes:c,listeners:d,setNodeRef:x,transform:h,transition:y,isDragging:b}=at({id:n.id}),S={transform:Ze.Transform.toString(h),transition:y,opacity:b?.6:1,zIndex:b?1e3:1,cursor:a?"move":"default"};return e.jsxs("div",{ref:x,style:S,...a?c:{},...a?d:{},className:`
        flex items-center justify-between rounded-md border px-3 py-2 text-sm transition-colors
        ${s?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 bg-white hover:bg-gray-50"}
        ${a?"cursor-move":"cursor-pointer"}
      `,onClick:i,children:[e.jsx("span",{className:"flex-1 truncate",children:n.item_type_name}),e.jsxs("div",{className:"flex items-center gap-1",children:[n.city_uid!==t&&e.jsx(ds,{className:"h-3 w-3 text-gray-400",title:"Nhóm món tại thành phố khác"}),a&&e.jsx(ms,{className:"h-3 w-3 text-gray-400"})]})]})}function As({item:n}){const{attributes:t,listeners:s,setNodeRef:i,transform:a,transition:c,isDragging:d}=at({id:n.id}),x={transform:Ze.Transform.toString(a),transition:c,opacity:d?.6:1,zIndex:d?1e3:1};return e.jsxs("div",{ref:i,style:x,...t,...s,className:"flex cursor-move flex-col items-center rounded-lg border bg-white p-3 shadow-sm transition-shadow hover:shadow-md",children:[n.image_path_thumb?e.jsx("img",{src:n.image_path_thumb,alt:n.item_name,className:"mb-2 h-16 w-16 rounded-md object-cover",onError:h=>{const y=h.target;y.style.display="none"}}):e.jsx("div",{className:"mb-2 flex h-16 w-16 items-center justify-center rounded-md bg-gray-100",children:e.jsx("span",{className:"text-xs text-gray-400",children:"No Image"})}),e.jsx("h4",{className:"text-center text-xs font-medium text-gray-900 line-clamp-2",children:n.item_name}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(n.ots_price||0)})]})}function zs({selectedItemTypeUid:n,itemTypes:t,sortedItems:s,sensors:i,onDragEnd:a}){var d;const c=n&&((d=t.find(x=>x.id===n))==null?void 0:d.item_type_name)||"Uncategory";return e.jsxs("div",{className:"flex h-full flex-col rounded-lg border",children:[e.jsx("div",{className:"flex-shrink-0 border-b bg-gray-50 p-3",children:e.jsx("h3",{className:"text-sm font-medium",children:c})}),e.jsx("div",{className:"min-h-0 flex-1",children:e.jsxs(ie,{className:"h-[50vh] w-full",children:[e.jsx("div",{className:"p-4",children:n?s.length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:e.jsx("p",{children:"Không có món ăn nào trong nhóm này"})}):e.jsx(Ye,{sensors:i,collisionDetection:Je,onDragEnd:a,children:e.jsx(tt,{items:s.map(x=>x.id),strategy:is,children:e.jsx("div",{className:"grid grid-cols-4 gap-4",children:s.map(x=>e.jsx(As,{item:x},x.id))})})}):e.jsx("div",{className:"py-8 text-center text-gray-500",children:e.jsx("p",{children:"Vui lòng chọn nhóm món từ danh sách bên trái"})})}),e.jsx($,{orientation:"vertical"})]})})]})}function Ks({open:n,onOpenChange:t,data:s,onSave:i}){var m;const[a,c]=j.useState(s),[d,x]=j.useState([]),[h,y]=j.useState(!1),{company:b,brands:S}=ae(u=>u.auth),w=S==null?void 0:S[0],{bulkCreateItemsInCity:g,isBulkCreating:v}=gt(),{data:p=[]}=ne({skip_limit:!0}),{data:N=[]}=we({skip_limit:!0}),{data:k=[]}=Ne(),{data:E=[]}=re();Oe.useEffect(()=>{c(s)},[s]),j.useEffect(()=>{if(s&&s.length>0){const u=s[0]||[],D=s.slice(1).map(V=>{const M={};return u.forEach((R,A)=>{M[String(R)]=V[A]||""}),M});x(D)}},[s]);const f=u=>{if(typeof u=="string"){const o=u.toLowerCase().trim();return o==="có"||o==="yes"||o==="1"||o==="true"?1:0}return u?1:0},l=async()=>{if(i){i(a),B.success("Data saved successfully"),t(!1);return}if(!(b!=null&&b.id)||!(w!=null&&w.id)){B.error("Thiếu thông tin công ty hoặc thương hiệu");return}if(d.length===0){B.error("Không có dữ liệu để import");return}y(!0);try{const u=d.map(o=>{const D=E.find(F=>F.city_name===o["Thành phố"]);if(!D)throw new Error(`Không tìm thấy thành phố: ${o["Thành phố"]}`);const V=k.find(F=>F.unit_id===o["Đơn vị"]),M=k.find(F=>F.unit_id==="MON"),R=p.find(F=>F.item_type_id===o.Nhóm||F.item_type_name===o.Nhóm),A=p.find(F=>F.item_type_name==="LOẠI KHÁC"),H=N.find(F=>F.item_class_id===o["Loại món"]||F.item_class_name===o["Loại món"]);return{company_uid:b.id,brand_uid:w.id,city_uid:D.id,item_id:o["Mã món"],unit_uid:(V==null?void 0:V.id)||(M==null?void 0:M.id)||"",ots_price:Number(o.Giá)||0,ta_price:Number(o.Giá)||0,ots_tax:(Number(o["VAT (%)"])||0)/100,ta_tax:(Number(o["VAT (%)"])||0)/100,item_name:o.Tên,item_id_barcode:o["Mã barcode"]||"",is_eat_with:f(o["Món ăn kèm"]),item_type_uid:(R==null?void 0:R.id)||(A==null?void 0:A.id)||"",item_class_uid:(H==null?void 0:H.id)||null,description:o["Mô tả"]||"",item_id_mapping:String(o.SKU||""),time_cooking:(Number(o["Thời gian chế biến (phút)"])||0)*6e4,time_sale_date_week:Number(o.Ngày)||0,time_sale_hour_day:Number(o.Giờ)||0,sort:Number(o["Thứ tự"])||1,image_path_thumb:"",image_path:o["Hình ảnh"]||"",extra_data:{no_update_quantity_toping:f(o["Không cập nhật số lượng món ăn kèm"]),enable_edit_price:f(o["Cho phép sửa giá khi bán"]),is_virtual_item:f(o["Cấu hình món ảo"]),is_item_service:f(o["Cấu hình món dịch vụ"]),is_buffet_item:f(o["Cấu hình món ăn là vé buffet"])}}});await g(u,{onSuccess:()=>{y(!1),t(!1)},onError:o=>{console.error("Error creating items:",o),y(!1)}})}catch(u){console.error("Error creating items:",u),B.error(u instanceof Error?u.message:"Có lỗi xảy ra khi tạo món ăn")}},C=()=>{t(!1)},_=Oe.useCallback(u=>{const o=a.filter((D,V)=>V!==u);c(o)},[a]),{tableData:O,columns:r}=j.useMemo(()=>{if(!a||a.length===0)return{tableData:[],columns:[]};const u=a[0]||[],o=a.slice(1),D=[{id:"actions",header:"-",cell:({row:M})=>e.jsx(P,{variant:"ghost",size:"sm",onClick:()=>_(M.original._originalIndex),className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:e.jsx(At,{className:"h-4 w-4"})}),enableSorting:!1,enableHiding:!1,size:50,meta:{className:"w-12 text-center sticky left-0 bg-background z-20 border-r"}},...u.map((M,R)=>({id:`col_${R}`,accessorKey:`col_${R}`,header:String(M),cell:({row:A})=>e.jsx("div",{className:"min-w-[150px] whitespace-nowrap",children:A.getValue(`col_${R}`)}),enableSorting:!1,enableHiding:!1,meta:{className:"min-w-[150px] px-4 whitespace-nowrap"}}))];return{tableData:o.map((M,R)=>{const A={_originalIndex:R+1};return M.forEach((H,F)=>{A[`col_${F}`]=H}),A}),columns:D}},[a,_]),T=qe({data:O,columns:r,getCoreRowModel:Ge()});return!a||a.length===0?null:e.jsx(X,{open:n,onOpenChange:t,children:e.jsxs(Y,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(J,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(Z,{className:"text-xl font-semibold",children:"Thêm món từ file"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(ie,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(ye,{children:[e.jsx(je,{className:"sticky top-0 z-10 bg-white",children:T.getHeaderGroups().map(u=>e.jsx(G,{children:u.headers.map(o=>{var D;return e.jsx(te,{className:((D=o.column.columnDef.meta)==null?void 0:D.className)||"",children:o.isPlaceholder?null:fe(o.column.columnDef.header,o.getContext())},o.id)})},u.id))}),e.jsx(be,{children:(m=T.getRowModel().rows)!=null&&m.length?T.getRowModel().rows.map(u=>e.jsx(G,{className:"hover:bg-muted/50",children:u.getVisibleCells().map(o=>{var D;return e.jsx(W,{className:((D=o.column.columnDef.meta)==null?void 0:D.className)||"",children:fe(o.column.columnDef.cell,o.getContext())},o.id)})},u.id)):e.jsx(G,{children:e.jsx(W,{colSpan:r.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx($,{orientation:"horizontal"}),e.jsx($,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(P,{variant:"outline",onClick:C,children:"Đóng"}),e.jsx(P,{onClick:l,disabled:h||v,className:"bg-green-600 hover:bg-green-700",children:h||v?"Đang lưu...":"Lưu"})]})]})]})})}function Ls(){const{open:n,setOpen:t}=ge(),[s,i]=j.useState(!1),[a,c]=j.useState([]),d=j.useRef(null),{data:x=[]}=ne(),{data:h=[]}=we(),{data:y=[]}=Ne(),{data:b=[]}=re(),{downloadImportTemplateAsync:S,isPending:w}=yt(),g=async()=>{try{await S({itemTypes:x,itemClasses:h,units:y,cities:b})}catch{B.error("Lỗi khi tải template")}},v=()=>{var N;(N=d.current)==null||N.click()},p=N=>{var f;const k=(f=N.target.files)==null?void 0:f[0];if(!k)return;const E=new FileReader;E.onload=l=>{var C;try{const _=new Uint8Array((C=l.target)==null?void 0:C.result),O=Te(_,{type:"array"}),r=O.SheetNames[0],T=O.Sheets[r],m=De.sheet_to_json(T,{defval:"",raw:!1});if(m.length===0){B.error("File không có dữ liệu");return}const u=new Set,o=m.map(D=>{const V=(D["Mã món"]??"").toString().trim();V&&u.add(V);let M=V;if(!M){let R=Be();for(;u.has(R);)R=Be();u.add(R),M=R}return{...D,"Mã món":M}});if(o.length>0){const D=Object.keys(o[0]),V=[D,...o.map(M=>D.map(R=>M[R]||""))];c(V)}else c([]);t(null),i(!0),d.current&&(d.current.value="")}catch{B.error("Lỗi khi đọc file. Vui lòng kiểm tra định dạng file.")}},E.readAsArrayBuffer(k)};return e.jsxs(e.Fragment,{children:[e.jsx(X,{open:n==="import",onOpenChange:N=>t(N?"import":null),children:e.jsxs(Y,{className:"max-w-2xl",children:[e.jsx(J,{children:e.jsx(Z,{children:"Thêm món"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Tải file mẫu"}),e.jsx(P,{variant:"outline",size:"sm",onClick:g,disabled:w,className:"flex items-center gap-2",children:w?"Đang tải...":e.jsxs(e.Fragment,{children:["Tải xuống",e.jsx(Se,{className:"h-4 w-4"})]})})]})}),e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Không được để trống các cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Tên, Thành phố"}),"."]}),e.jsxs("p",{children:["Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã nhóm, mã loại, mã đơn vị đã có vào cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Nhóm, Loại món"}),"."]}),e.jsxs("p",{children:["Mã đơn vị món, mã thành phố có thể xem trong sheet"," ",e.jsx("span",{className:"font-mono text-blue-600",children:"Guide"})," của file mẫu."]})]})]}),e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"})]}),e.jsxs(P,{variant:"outline",size:"sm",onClick:v,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(ke,{className:"h-4 w-4"})]})]})})]})]})}),e.jsx("input",{ref:d,type:"file",accept:".xlsx,.xls",onChange:p,style:{display:"none"}}),e.jsx(Ks,{open:s,onOpenChange:i,data:a})]})}function Hs({open:n,onOpenChange:t,data:s}){const[i,a]=j.useState(s),[c,d]=j.useState(!1),{user:x,company:h}=ae(f=>f.auth),{selectedBrand:y}=Ke(),{mutate:b,isPending:S}=Ue(),{data:w=[]}=ne({skip_limit:!0}),{data:g=[]}=we({skip_limit:!0}),{data:v=[]}=Ne(),{data:p=[]}=re();j.useEffect(()=>{a(s)},[s]);const N=f=>{a(l=>l.filter((C,_)=>_!==f))},k=async()=>{if(!h||!y){B.error("Thiếu thông tin cần thiết để cập nhật");return}d(!0);const f=i.map(l=>{const C=v.find(u=>u.unit_id===l.unit_id),_=p.find(u=>u.city_name===l.city_name),O=w.find(u=>u.item_type_id===l.item_type_id||u.item_type_name===l.item_type_name),r=g.find(u=>u.item_class_id===l.item_class_id||u.item_class_name===l.item_class_name),T=v.find(u=>u.unit_id==="MON"),m=w.find(u=>u.item_type_name==="LOẠI KHÁC");return{item_id:l.item_id,item_name:l.item_name,description:l.description||"",ots_price:l.ots_price||0,ots_tax:(l.ots_tax||0)/100,ta_price:l.ots_price||0,ta_tax:(l.ots_tax||0)/100,time_sale_hour_day:String(l.time_sale_hour_day??0),time_sale_date_week:String(l.time_sale_date_week??0),allow_take_away:1,is_eat_with:l.is_eat_with||0,image_path:l.image_path||"",image_path_thumb:l.image_path?`${l.image_path}?width=185`:"",item_color:"",list_order:l.list_order||0,is_service:l.is_item_service||0,is_material:0,active:l.active||1,user_id:"",is_foreign:0,quantity_default:0,price_change:l.price_change||0,currency_type_id:"",point:0,is_gift:0,is_fc:0,show_on_web:0,show_price_on_web:0,cost_price:0,is_print_label:l.is_print_label||0,quantity_limit:0,is_kit:0,time_cooking:(l.time_cooking||0)*6e4,item_id_barcode:l.item_id_barcode||"",process_index:0,is_allow_discount:l.is_allow_discount||0,quantity_per_day:0,item_id_eat_with:"",is_parent:0,is_sub:0,item_id_mapping:String(l.sku||""),effective_date:0,expire_date:0,sort:l.list_order||1,sort_online:1e3,extra_data:{cross_price:l.cross_price||[],formula_qrcode:l.inqr_formula||"",is_buffet_item:l.is_buffet_item||0,up_size_buffet:[],is_item_service:l.is_item_service||0,is_virtual_item:l.is_virtual_item||0,price_by_source:l.price_by_source||[],enable_edit_price:l.price_change||0,exclude_items_buffet:l.exclude_items_buffet||[],no_update_quantity_toping:l.no_update_quantity_toping||0},revision:0,unit_uid:(C==null?void 0:C.id)||(T==null?void 0:T.id)||"",unit_secondary_uid:null,item_type_uid:(O==null?void 0:O.id)||(m==null?void 0:m.id)||"",item_class_uid:(r==null?void 0:r.id)||void 0,source_uid:null,brand_uid:y.id,city_uid:(_==null?void 0:_.id)||"",company_uid:h.id,customization_uid:l.customization_uid||"",is_fabi:1,deleted:!1,created_by:(x==null?void 0:x.email)||"",updated_by:(x==null?void 0:x.email)||"",deleted_by:null,created_at:l.created_at||Math.floor(Date.now()/1e3),updated_at:Math.floor(Date.now()/1e3),deleted_at:null,cities:_?[{id:_.id,city_id:_.city_id||"",fb_city_id:_.fb_city_id||"",city_name:_.city_name,image_path:_.image_path,description:_.description||"",active:_.active||1,extra_data:_.extra_data,revision:_.revision||0,sort:_.sort||0,created_by:_.created_by,updated_by:_.updated_by,deleted_by:_.deleted_by,created_at:_.created_at||0,updated_at:_.updated_at||0,deleted_at:_.deleted_at,items_cities:{item_uid:l.id,city_uid:_.id}}]:[],id:l.id}});b(f,{onSuccess:()=>{d(!1),t(!1)},onError:l=>{console.error("Error updating items:",l),B.error(`Có lỗi xảy ra khi cập nhật món ăn: ${l}`),d(!1)}})},E=[{key:"item_id",label:"Mã món",width:"120px"},{key:"city_name",label:"Thành phố",width:"120px"},{key:"item_name",label:"Tên",width:"200px"},{key:"ots_price",label:"Giá",width:"100px"},{key:"active",label:"Trạng thái",width:"100px"},{key:"item_id_barcode",label:"Mã barcode",width:"120px"},{key:"is_eat_with",label:"Món ăn kèm",width:"120px"},{key:"no_update_quantity_toping",label:"Không cập nhật số lượng",width:"180px"},{key:"unit_name",label:"Đơn vị",width:"100px"},{key:"item_type_id",label:"Nhóm",width:"120px"},{key:"item_type_name",label:"Tên nhóm",width:"150px"},{key:"item_class_id",label:"Loại món",width:"120px"},{key:"item_class_name",label:"Tên loại",width:"150px"},{key:"description",label:"Mô tả",width:"200px"},{key:"sku",label:"SKU",width:"100px"},{key:"ots_tax",label:"VAT (%)",width:"80px"},{key:"time_cooking",label:"Thời gian chế biến (phút)",width:"180px"},{key:"price_change",label:"Cho phép sửa giá khi bán",width:"180px"},{key:"is_virtual_item",label:"Cấu hình món ảo",width:"150px"},{key:"is_item_service",label:"Cấu hình món dịch vụ",width:"180px"},{key:"is_buffet_item",label:"Cấu hình món ăn là vé buffet",width:"200px"},{key:"time_sale_hour_day",label:"Giờ",width:"80px"},{key:"time_sale_date_week",label:"Ngày",width:"80px"},{key:"list_order",label:"Thứ tự",width:"80px"},{key:"image_path",label:"Hình ảnh",width:"120px"},{key:"inqr_formula",label:"Công thức inQR cho máy pha trà",width:"220px"}];return e.jsx(X,{open:n,onOpenChange:t,children:e.jsxs(Y,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(J,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(Z,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(ie,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(ye,{children:[e.jsx(je,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(G,{children:[e.jsx(te,{className:"w-12"}),E.map(f=>e.jsx(te,{style:{width:f.width},children:f.label},f.key))]})}),e.jsx(be,{children:i.map((f,l)=>e.jsxs(G,{children:[e.jsx(W,{children:e.jsx(P,{variant:"ghost",size:"icon",onClick:()=>N(l),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(Ie,{className:"h-4 w-4"})})}),E.map(C=>e.jsx(W,{style:{width:C.width},children:(()=>{var O;const _=f[C.key];return C.key==="ots_price"?e.jsxs("span",{className:"text-right",children:[((O=Number(_))==null?void 0:O.toLocaleString("vi-VN"))||0," ₫"]}):C.key==="active"?e.jsx("span",{children:_}):C.key==="item_id"||C.key==="item_id_barcode"?e.jsx("span",{className:"font-mono text-sm",children:_||""}):C.key==="item_name"?e.jsx("span",{className:"font-medium",children:_||""}):["is_eat_with","no_update_quantity_toping","price_change","is_virtual_item","is_item_service","is_buffet_item"].includes(C.key)?e.jsx("span",{className:"text-center",children:_}):e.jsx("span",{children:_||""})})()},C.key))]},l))})]}),e.jsx($,{orientation:"horizontal"}),e.jsx($,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(P,{variant:"outline",onClick:()=>t(!1),children:"Đóng"}),e.jsx(P,{onClick:k,disabled:c||S,children:c||S?"Đang lưu...":"Lưu"})]})]})]})})}function Us({open:n,onOpenChange:t}){const[s,i]=j.useState("all"),[a,c]=j.useState("all"),[d,x]=j.useState("all"),[h,y]=j.useState([]),[b,S]=j.useState(!1),w=j.useRef(null),{data:g=[]}=ne(),{data:v=[]}=we(),{data:p=[]}=Ne(),{data:N=[]}=re(),{fetchItemsDataAsync:k,isPending:E}=jt(),f=[{label:"Tất cả nhóm món",value:"all"},...g.filter(m=>m.active===1).map(m=>({label:m.item_type_name,value:m.id}))],l=[{label:"Tất cả thành phố",value:"all"},...N.filter(m=>m.active===1).map(m=>({label:m.city_name,value:m.id}))],C=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}],_=async()=>{try{const m=await k({city_uid:s!=="all"?s:void 0,item_type_uid:a!=="all"?a:void 0,active:d!=="all"?d:void 0});await bt({itemTypes:g,itemClasses:v,units:p},m),B.success("Tải file thành công!")}catch{B.error("Lỗi khi tải file")}},O=m=>({ID:"id","Mã món":"item_id","Thành phố":"city_name",Tên:"item_name",Giá:"ots_price","Trạng thái":"active","Mã barcode":"item_id_barcode","Món ăn kèm":"is_eat_with","Không cập nhật số lượng món ăn kèm":"no_update_quantity_toping","Đơn vị":"unit_name",Nhóm:"item_type_id","Tên nhóm":"item_type_name","Loại món":"item_class_id","Tên loại":"item_class_name","Mô tả":"description",SKU:"sku","VAT (%)":"ots_tax","Thời gian chế biến (phút)":"time_cooking","Cho phép sửa giá khi bán":"price_change","Cấu hình món ảo":"is_virtual_item","Cấu hình món dịch vụ":"is_item_service","Cấu hình món ăn là vé buffet":"is_buffet_item",Giờ:"time_sale_hour_day",Ngày:"time_sale_date_week","Thứ tự":"list_order","Hình ảnh":"image_path","Công thức inQR cho máy pha trà":"inqr_formula"})[m]||m.toLowerCase().replace(/\s+/g,"_"),r=m=>{var D;const u=(D=m.target.files)==null?void 0:D[0];if(!u)return;const o=new FileReader;o.onload=V=>{var M;try{const R=new Uint8Array((M=V.target)==null?void 0:M.result),A=Te(R,{type:"array"}),H=A.SheetNames[0],F=A.Sheets[H],K=De.sheet_to_json(F,{header:1});if(K.length>0){const U=K,z=U[0]||[],ue=U.slice(1).map((nt,ve)=>{const ce={id:`temp_${ve}`};return z.forEach((Me,rt)=>{const L=O(String(Me)),xe=nt[rt];ve===0&&console.log(`Header: "${Me}" -> Key: "${L}", Value: "${xe}"`),L==="ots_price"||L==="ots_tax"||L==="time_cooking"||L==="time_sale_hour_day"||L==="time_sale_date_week"||L==="list_order"||L==="active"||L==="is_eat_with"||L==="no_update_quantity_toping"||L==="price_change"||L==="is_virtual_item"||L==="is_item_service"||L==="is_buffet_item"?ce[L]=Number(xe)||0:ce[L]=xe||""}),ve===0&&console.log("First item after processing:",ce),ce});y(ue),S(!0),B.success("File uploaded successfully")}}catch{B.error("Error parsing file")}},o.readAsArrayBuffer(u)},T=()=>{var m;(m=w.current)==null||m.click()};return e.jsxs(e.Fragment,{children:[e.jsx(X,{open:n,onOpenChange:t,children:e.jsxs(Y,{className:"max-w-2xl lg:max-w-xl",children:[e.jsx(J,{children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(Z,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(Ce,{options:l,value:s,onValueChange:i,placeholder:"Tất cả thành phố",searchPlaceholder:"Tìm thành phố...",className:"flex-1"}),e.jsx(Ce,{options:f,value:a,onValueChange:c,placeholder:"Tất cả nhóm món",searchPlaceholder:"Tìm nhóm món...",className:"flex-1"}),e.jsx(Ce,{options:C,value:d,onValueChange:x,placeholder:"Tất cả trạng thái",searchPlaceholder:"Tìm trạng thái...",className:"flex-1"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải xuống"}),e.jsxs(P,{variant:"outline",size:"sm",onClick:_,disabled:E,className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),E&&"Đang tải..."]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không sửa các cột :"}),e.jsx("p",{className:"font-mono text-sm text-blue-600",children:"ID, Mã món, Thành phố, Đơn vị, Tên nhóm, Tên loại."})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsxs(P,{variant:"outline",size:"sm",onClick:T,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(ke,{className:"h-4 w-4"})]}),e.jsx("input",{ref:w,type:"file",accept:".xlsx,.xls",onChange:r,style:{display:"none"}})]})]})]})]})}),e.jsx(Hs,{open:b,onOpenChange:S,data:h})]})}function qs({open:n,onOpenChange:t,data:s,originalItems:i,sources:a}){const[c,d]=j.useState(s),{bulkUpdatePriceBySource:x,isUpdating:h}=ks();j.useEffect(()=>{d(s)},[s]);const y=g=>{d(v=>v.filter((p,N)=>N!==g))},b=async()=>{if(c.length===0){B.error("Không có dữ liệu để lưu");return}try{await x({previewItems:c,originalItems:i,sources:a},{onSuccess:()=>{t(!1)},onError:g=>{console.error("Error saving price by source configuration:",g)}})}catch(g){console.error("Error saving price by source configuration:",g)}},S=[{key:"item_uid",label:"item_uid",width:"280px"},{key:"item_id",label:"item_id",width:"120px"},{key:"item_name",label:"item_name",width:"200px"},{key:"ots_price",label:"Giá gốc",width:"100px"},{key:"ots_tax",label:"Vat",width:"80px"},{key:"ZALO [10000045]",label:"ZALO [10000045]",width:"120px"},{key:"FACEBOOK [10000049]",label:"FACEBOOK [10000049]",width:"140px"},{key:"SO [10000134]",label:"SO [10000134]",width:"120px"},{key:"CRM [10000162]",label:"CRM [10000162]",width:"120px"},{key:"VNPAY [10000165]",label:"VNPAY [10000165]",width:"120px"},{key:"GOJEK (GOVIET) [10000168]",label:"GOJEK (GOVIET) [10000168]",width:"180px"},{key:"ShopeeFood [10000169]",label:"ShopeeFood [10000169]",width:"160px"},{key:"MANG VỀ [10000171]",label:"MANG VỀ [10000171]",width:"140px"},{key:"TẠI CHỖ [10000172]",label:"TẠI CHỖ [10000172]",width:"140px"},{key:"CALL CENTER [10000176]",label:"CALL CENTER [10000176]",width:"160px"},{key:"O2O [10000216]",label:"O2O [10000216]",width:"120px"},{key:"BEFOOD [10000253]",label:"BEFOOD [10000253]",width:"140px"}],w=g=>{if(g==null||g==="")return"";const v=typeof g=="string"?parseFloat(g):g;return isNaN(v)?"":v.toLocaleString("vi-VN")};return e.jsx(X,{open:n,onOpenChange:t,children:e.jsxs(Y,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(J,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(Z,{className:"text-xl font-semibold",children:"Thêm cấu hình giá món theo nguồn đơn"})}),e.jsxs("div",{className:"flex h-full flex-col space-y-4 overflow-hidden",children:[e.jsxs("div",{className:"flex-shrink-0 text-sm text-gray-600",children:["Tổng số món: ",e.jsx("span",{className:"font-medium",children:c.length})]}),e.jsx("div",{className:"min-h-0 flex-1",children:e.jsxs(ie,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(ye,{children:[e.jsx(je,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(G,{children:[e.jsx(te,{className:"w-12"}),S.map(g=>e.jsx(te,{style:{width:g.width},children:g.label},g.key))]})}),e.jsx(be,{children:c.map((g,v)=>e.jsxs(G,{children:[e.jsx(W,{children:e.jsx(P,{variant:"ghost",size:"icon",onClick:()=>y(v),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(Ie,{className:"h-4 w-4"})})}),S.map(p=>e.jsxs(W,{style:{width:p.width},children:[p.key==="item_uid"&&e.jsx("span",{className:"font-mono text-xs text-gray-600",children:g[p.key]}),p.key==="item_id"&&e.jsx("span",{className:"font-mono text-sm",children:g[p.key]}),p.key==="item_name"&&e.jsx("span",{className:"font-medium",children:g[p.key]}),p.key==="item_type_name"&&e.jsx("span",{className:"text-sm",children:g[p.key]}),p.key==="ots_price"&&e.jsx("span",{className:"text-right font-medium",children:w(g[p.key])}),p.key==="ots_tax"&&e.jsx("span",{className:"text-center",children:g[p.key]}),p.key.includes("[")&&e.jsx("span",{className:"text-right font-medium text-blue-600",children:g[p.key]?`${w(g[p.key])}`:""}),!p.key.includes("[")&&p.key!=="item_uid"&&p.key!=="item_id"&&p.key!=="item_name"&&p.key!=="item_type_name"&&p.key!=="ots_price"&&p.key!=="ots_tax"&&e.jsx("span",{children:g[p.key]||""})]},p.key))]},v))})]}),e.jsx($,{orientation:"horizontal"}),e.jsx($,{orientation:"vertical"})]})}),e.jsxs("div",{className:"flex flex-shrink-0 items-center justify-between border-t pt-4",children:[e.jsx(P,{variant:"outline",onClick:()=>t(!1),children:"Đóng"}),e.jsx(P,{onClick:b,disabled:h||c.length===0,children:h?"Đang lưu...":"Lưu"})]})]})]})})}function Gs({open:n,onOpenChange:t,cities:s}){var C;const[i,a]=j.useState(""),[c,d]=j.useState(null),[x,h]=j.useState(!1),[y,b]=j.useState(!1),[S,w]=j.useState([]),[g,v]=j.useState(!1),{items:p,sources:N,itemTypes:k,isLoading:E}=Cs(i,!!i),f=async()=>{if(!i){B.error("Vui lòng chọn thành phố trước");return}if(E){B.error("Đang tải dữ liệu, vui lòng chờ...");return}if(!p.length){B.error("Không có món nào trong thành phố này");return}try{h(!0);const _=s.find(r=>r.id===i),O=(_==null?void 0:_.city_name)||"Unknown";await fs(p,k,O),B.success("Đã tải xuống file template thành công")}catch(_){console.error("Error downloading template:",_),B.error("Có lỗi xảy ra khi tải xuống file template")}finally{h(!1)}},l=()=>{if(!i){B.error("Vui lòng chọn thành phố trước");return}const _=document.createElement("input");_.type="file",_.accept=".xlsx,.xls",_.onchange=async O=>{var m;const r=(m=O.target.files)==null?void 0:m[0];if(!r)return;const T=ys(r);if(!T.isValid){B.error(T.error);return}try{b(!0),d(r);const u=await gs(r);if(u.length===0){B.error("File không có dữ liệu hợp lệ");return}w(u),v(!0),t(!1),B.success(`Đã xử lý file thành công: ${u.length} món`)}catch(u){console.error("Error processing file:",u),B.error("Có lỗi xảy ra khi xử lý file")}finally{b(!1)}},_.click()};return e.jsxs(X,{open:n,onOpenChange:t,children:[e.jsxs(Y,{className:"max-w-2xl",children:[e.jsx(J,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(Z,{className:"text-xl font-semibold",children:"Thêm cấu hình giá món theo nguồn đơn"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 1. Chọn thành phố"}),e.jsxs(oe,{value:i,onValueChange:a,children:[e.jsx(de,{className:"w-full",children:e.jsx(me,{placeholder:"Chọn thành phố"})}),e.jsx(he,{children:s.map(_=>e.jsx(Q,{value:_.id,children:_.city_name},_.id))})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 2. Tải file dữ liệu để lấy món và nguồn đã có"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"text-sm text-gray-600",children:"Tải xuống"})}),e.jsxs(P,{variant:"outline",onClick:f,disabled:x||!i||E,className:"flex items-center gap-2",children:[e.jsx(ls,{className:"h-4 w-4"}),x?"Đang tải...":E?"Đang tải dữ liệu...":"Tải xuống"]})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Nhập giá theo nguồn tương ứng với từng món vào cột ",e.jsx("strong",{children:"price"}),"."]}),e.jsx("p",{children:"Bỏ trống hoặc xoá dòng với những nguồn không có cấu hình giá."}),e.jsx("p",{children:e.jsx("strong",{className:"text-red-600",children:"Không sửa các cột item_uid, item_name."})})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),c&&e.jsxs("span",{className:"mt-1 text-xs text-gray-500",children:["File đã chọn: ",c.name]})]}),e.jsxs(P,{onClick:l,disabled:!i||y,className:"flex items-center gap-2",children:[e.jsx(cs,{className:"h-4 w-4"}),y?"Đang xử lý...":"Tải file lên"]})]})]})]})]}),e.jsx(qs,{open:g,onOpenChange:v,data:S,originalItems:p,sources:N,storeName:(C=s.find(_=>_.id===i))==null?void 0:C.city_name})]})}function $s({open:n,onOpenChange:t}){const[s,i]=j.useState(""),[a,c]=j.useState(""),[d,x]=j.useState([]),[h,y]=j.useState(!1),[b,S]=j.useState(!1),{cities:w}=os(),{data:g=[]}=ne({...s&&s!=="all"?{city_uid:s}:{},enabled:n&&!!s}),{data:v=[]}=wt({params:{...s&&{city_uid:s},active:1,skip_limit:!0},enabled:n&&!!s}),p=Ue(),N=js(),k=We(_e(Xe),_e(Qe,{coordinateGetter:et})),[E,f]=j.useState([]);j.useEffect(()=>{if(!a){f([]),S(!1);return}const r=(v||[]).filter(m=>m.item_type_uid===a);if(r.length===0){f([]),S(!1);return}const T=[...r].sort((m,u)=>m.sort!==u.sort?m.sort-u.sort:m.id.localeCompare(u.id));f(T),S(!1)},[v.length,a]),j.useEffect(()=>{n||(i(""),c(""),f([]),x([]),y(!1),S(!1))},[n]),j.useEffect(()=>{c(""),f([])},[s]);const l=r=>{const{active:T,over:m}=r;m&&T.id!==m.id&&f(u=>{const o=u.findIndex(M=>M.id===T.id),D=u.findIndex(M=>M.id===m.id),V=st(u,o,D);return b||S(!0),V})},C=()=>E.map((r,T)=>({id:r.id,item_id:r.item_id,item_name:r.item_name,description:r.description,ots_price:r.ots_price,ots_tax:r.ots_tax,ta_price:r.ta_price,ta_tax:r.ta_tax,time_sale_hour_day:r.time_sale_hour_day,time_sale_date_week:r.time_sale_date_week,allow_take_away:r.allow_take_away,is_eat_with:r.is_eat_with,image_path:r.image_path,image_path_thumb:r.image_path_thumb,item_color:r.item_color,list_order:T+1,is_service:r.is_service,is_material:r.is_material,active:r.active,user_id:r.user_id,is_foreign:r.is_foreign,quantity_default:r.quantity_default,price_change:r.price_change,currency_type_id:r.currency_type_id,point:r.point,is_gift:r.is_gift,is_fc:r.is_fc,show_on_web:r.show_on_web,show_price_on_web:r.show_price_on_web,cost_price:r.cost_price,is_print_label:r.is_print_label,quantity_limit:r.quantity_limit,is_kit:r.is_kit,time_cooking:r.time_cooking,item_id_barcode:r.item_id_barcode,process_index:r.process_index,is_allow_discount:r.is_allow_discount,quantity_per_day:r.quantity_per_day,item_id_eat_with:r.item_id_eat_with,is_parent:r.is_parent,is_sub:r.is_sub,item_id_mapping:r.item_id_mapping,effective_date:r.effective_date,expire_date:r.expire_date,sort:T+1,extra_data:{...r.extra_data,formula_qrcode:""},revision:r.revision,unit_uid:r.unit_uid,unit_secondary_uid:r.unit_secondary_uid,item_type_uid:r.item_type_uid,item_class_uid:r.item_class_uid,source_uid:r.source_uid,brand_uid:r.brand_uid,company_uid:r.company_uid,customization_uid:r.customization_uid,is_fabi:r.is_fabi,deleted:r.deleted,created_by:r.created_by,updated_by:r.updated_by,deleted_by:r.deleted_by,created_at:r.created_at,updated_at:r.updated_at,deleted_at:r.deleted_at,city_uid:r.city_uid})),_=()=>{if(!s){t(!1);return}const r=h,T=b&&E.length>0&&!!a;if(!r&&!T){t(!1);return}let m=0;const u=Number(r)+Number(T),o=()=>{m+=1,m>=u&&t(!1)};if(r){const V=((d.length>0?d:g)||[]).map((M,R)=>({id:M.id,sort:R+1}));N.mutate({city_uid:s,data:V},{onSuccess:()=>o()})}if(T){const D=C();p.mutate(D,{onSuccess:()=>o()})}},O=()=>{t(!1)};return e.jsx(X,{open:n,onOpenChange:t,children:e.jsxs(Y,{className:"flex h-[90vh] max-w-7xl flex-col sm:max-w-4xl",children:[e.jsx(J,{className:"flex-shrink-0",children:e.jsx(Z,{className:"text-center text-lg font-medium",children:"Sắp xếp thực đơn bán hàng"})}),e.jsx("div",{className:"mb-4 flex-shrink-0 rounded-md border border-yellow-200 bg-yellow-50 p-3",children:e.jsx("p",{className:"text-sm text-yellow-800",children:"Các món tại thành phố bị thay đổi vị trí sẽ chuyển thành món tại cửa hàng"})}),e.jsxs("div",{className:"mb-4 flex flex-shrink-0 items-center gap-4",children:[e.jsx("span",{className:"text-sm font-medium whitespace-nowrap",children:"Thứ tự hiển thị sẽ được áp dụng trên thực đơn của thiết bị bán hàng"}),e.jsx("div",{className:"flex-1"}),e.jsx("div",{className:"w-64",children:e.jsxs(oe,{value:s,onValueChange:i,children:[e.jsx(de,{children:e.jsx(me,{placeholder:"Chọn thành phố"})}),e.jsx(he,{children:w.map(r=>e.jsx(Q,{value:r.id,children:r.city_name},r.id))})]})})]}),e.jsx("div",{className:"min-h-0 flex-1 overflow-hidden",children:s?e.jsxs("div",{className:"grid h-full grid-cols-12 gap-4",children:[e.jsx("div",{className:"col-span-3 h-full",children:e.jsxs("div",{className:"flex h-full flex-col rounded-lg border",children:[e.jsx("div",{className:"flex-shrink-0 border-b bg-gray-50 p-3",children:e.jsx("h3",{className:"text-sm font-medium",children:"Tên nhóm món"})}),e.jsx("div",{className:"min-h-0 flex-1",children:e.jsx(Rs,{itemTypes:g,selectedCityUid:s,selectedItemTypeUid:a,onSelect:r=>c(r),onOrderChangeDetailed:r=>{x(r);try{const T=g.map(o=>o.id),m=r.map(o=>o.id),u=T.length!==m.length||T.some((o,D)=>o!==m[D]);y(u)}catch{}}})})]})}),e.jsx("div",{className:"col-span-9 h-full",children:e.jsx(zs,{selectedItemTypeUid:a,itemTypes:g.map(r=>({id:r.id,item_type_name:r.item_type_name})),sortedItems:E,sensors:k,onDragEnd:l})})]}):e.jsx("div",{className:"flex h-full items-center justify-center text-gray-500",children:e.jsx("p",{children:"Chưa chọn thành phố"})})}),e.jsxs("div",{className:"flex flex-shrink-0 justify-end gap-2 border-t pt-4",children:[e.jsx(P,{variant:"outline",onClick:O,disabled:p.isPending,children:"Hủy"}),e.jsx(P,{onClick:_,disabled:!s||!h&&!(b&&E.length>0&&a),children:N.isPending||p.isPending?"Đang lưu...":"Lưu"})]})]})})}function Ws(){const{open:n,setOpen:t,currentRow:s,setCurrentRow:i}=ge(),{deleteItemAsync:a}=Nt(),{data:c=[]}=re();return e.jsxs(e.Fragment,{children:[e.jsx(Us,{open:n==="export-dialog",onOpenChange:()=>t(null)}),e.jsx(Ls,{}),e.jsx($s,{open:n==="sort-menu",onOpenChange:()=>t(null)}),e.jsx(Gs,{open:n==="price-by-source-config",onOpenChange:()=>t(null),cities:c}),s&&e.jsx(e.Fragment,{children:e.jsx($e,{destructive:!0,open:n==="delete",onOpenChange:d=>{d||(t(null),setTimeout(()=>{i(null)},500))},handleConfirm:async()=>{t(null),setTimeout(()=>{i(null)},500),await a(s.id||"")},className:"max-w-md",title:"Bạn có muốn xoá ?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")})]})}function Qs(){const n=Le(),[t,s]=j.useState(1),{setOpen:i,setCurrentRow:a}=ge(),{updateStatusAsync:c}=Ct(),{updateItemAsync:d}=St(),{isCustomizationDialogOpen:x,isBuffetItem:h,isBuffetConfigModalOpen:y,setIsCustomizationDialogOpen:b,setIsBuffetItem:S,selectedMenuItem:w,setSelectedMenuItem:g,setIsBuffetConfigModalOpen:v,selectedBuffetMenuItem:p,setSelectedBuffetMenuItem:N,selectedItemTypeUid:k,setSelectedItemTypeUid:E,selectedCityUid:f,setSelectedCityUid:l,selectedDaysOfWeek:C,setSelectedDaysOfWeek:_,selectedStatus:O,setSelectedStatus:r}=bs(),T=j.useMemo(()=>({...k!=="all"&&{item_type_uid:k},...f!=="all"&&{city_uid:f},...C.length>0&&{time_sale_date_week:C.join(",")},...O!=="all"&&{active:parseInt(O,10)},page:t}),[k,f,C,O,t]);j.useEffect(()=>{s(1)},[k,f,C,O]);const{data:m=[],isLoading:u,error:o,hasNextPage:D}=kt({params:T}),{data:V=[]}=mt({skip_limit:!0,list_city_uid:f!=="all"?[f]:void 0}),M=z=>{g(z),b(!0)},R=z=>{var le,ue;g(z),N(((le=z==null?void 0:z.extra_data)==null?void 0:le.exclude_items_buffet)||[]),S(((ue=z==null?void 0:z.extra_data)==null?void 0:ue.is_buffet_item)===1),v(!0)},A=z=>{n({to:"/menu/items/items-in-city/detail",search:{id:z.id||""}})},H=z=>{a(z),i("delete")},F=z=>{n({to:"/menu/items/items-in-city/detail/$id",params:{id:z.id||""}})},K=async z=>{const le=z.active?0:1;await c({id:z.id||"",active:le})},U=Ds({onBuffetConfigClick:R});return o?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:o&&`Món ăn: ${(o==null?void 0:o.message)||"Lỗi không xác định"}`})]})}):e.jsxs(e.Fragment,{children:[e.jsx(ht,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(pt,{}),e.jsx(ft,{}),e.jsx(xt,{})]})}),e.jsxs(ut,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Món ăn tại thành phố"})}),e.jsx(Is,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[u&&e.jsx(Bs,{}),!u&&e.jsx(Os,{columns:U,data:m,onCustomizationClick:M,onCopyClick:A,onToggleStatus:K,onRowClick:F,onDeleteClick:H,customizations:V,selectedItemTypeUid:k,onItemTypeChange:E,selectedCityUid:f,onCityChange:l,selectedDaysOfWeek:C,onDaysOfWeekChange:_,selectedStatus:O,onStatusChange:r,hasNextPageOverride:D,currentPage:t,onPageChange:s})]})]}),e.jsx(Ws,{}),x&&w&&e.jsx(It,{open:x,onOpenChange:b,item:w,customizations:V}),y&&p&&e.jsx(Tt,{itemsBuffet:p,open:y,onOpenChange:v,onItemsChange:async z=>{await d({...w,extra_data:{is_buffet_item:h?1:0,exclude_items_buffet:z}})},items:m,hide:!1,enable:h,onEnableChange:S})]})}function Xs(){return e.jsx(vt,{children:e.jsx(Qs,{})})}const ji=Xs;export{ji as component};
