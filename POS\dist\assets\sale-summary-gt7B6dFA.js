import{a as se,b as de,a3 as We,r as g,j as e,c as oe,B as P,l as Ve}from"./index-D0Grd55b.js";import{T as Xe,a as Je,c as ne,b as ae}from"./tabs-b_k7_Aeh.js";import{H as Ze}from"./header-yApU5MZq.js";import{M as Re}from"./main-Czv3HpP4.js";import{g as we,a as et,u as tt,b as Pe,c as Me,R as st,d as nt,T as at}from"./use-all-stores-B2DjtLaR.js";import{P as rt}from"./profile-dropdown-dGP8eyih.js";import{S as lt,T as ot}from"./search-BEMocVbv.js";import{u as je}from"./useQuery-Ck3BpOfq.js";import{s as _e}from"./sale-sources-api-CQbs1-CU.js";import{L as J}from"./form-Bk1C9kLO.js";import{S as Te,a as Ce,b as Le,c as Ae,d as z}from"./select-DBO-8fSu.js";import{C as ke}from"./calendar-5lpy20z0.js";import{P as $e,a as Ee,b as Fe}from"./popover-CMTiAV3j.js";import{C as Ie}from"./calendar-Bxala8X3.js";import{f as re,v as he,l as it}from"./isSameMonth-C8JQo-AN.js";import{v as X}from"./date-range-picker-CruKYeHR.js";import{C as B,a as U,b as O,d as K,c as W}from"./card-Z5f_tC2-.js";import{D as ct}from"./dollar-sign-BEX4adrC.js";import{c as dt}from"./createLucideIcon-DNzDbUBG.js";import{y as mt,X as me,Y as ue,F as ut,R as ve,B as Ne,H as Se}from"./generateCategoricalChart-CMXX3hIw.js";import{L as He}from"./LineChart-CdzR7PuL.js";import{C as De}from"./CartesianGrid-CLy0aZan.js";import{L as qe}from"./Line-DnHAVRtp.js";import{r as ie}from"./revenue-api-BGrrlqj7.js";import{A as Ge}from"./Area-DVL7sOOj.js";import{b as Ye}from"./pos-api-B09qRspF.js";import{A as ht,b as xt}from"./alert-cGYrLjSy.js";import{B as Z}from"./badge-DoEZpO1Y.js";import{T as Be}from"./triangle-alert-w5cKV9eA.js";import{C as ft}from"./circle-check-big-CX7RpMKu.js";import"./index-DxQNaO1C.js";import"./index-CI2TkimM.js";import"./separator-CiUiq7rT.js";import"./dropdown-menu-BvqrmFsX.js";import"./index-CW7Xpojs.js";import"./check-TFQPNqMS.js";import"./createReactComponent-eJgt86Cn.js";import"./stores-api-DKloh9FA.js";import"./avatar-C7dnn6zI.js";import"./search-context-CjM0jrYw.js";import"./command-Bcq7GTcy.js";import"./dialog-C8IVKkOo.js";import"./search-C4dfJjaw.js";import"./scroll-area-r2ikcXUQ.js";import"./IconChevronRight-DnVUSvDn.js";import"./IconSearch-ClUZlEwa.js";import"./utils-km2FGkQ4.js";import"./index-C-UyCxtf.js";import"./chevron-right-D2H_xC9V.js";import"./react-icons.esm-BOnXQgjl.js";import"./index-Chjiymov.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pt=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]],gt=dt("users",pt);var yt=mt({chartName:"AreaChart",GraphicalChild:Ge,axisComponents:[{axisType:"xAxis",AxisComp:me},{axisType:"yAxis",AxisComp:ue}],formatAxisMap:ut});const ce={all:["sources"],lists:()=>[...ce.all,"list"],list:t=>[...ce.lists(),t]};function Qe(t={}){const{dateRange:s,selectedStores:r=["all-stores"],selectedSources:m=["all-sources"],filterType:u="daily",autoFetch:d=!0}=t,{selectedBrand:i,currentBrandStores:o}=se(),{company:n}=de(),x=We(),f=g.useMemo(()=>{var N;return(N=s==null?void 0:s.from)==null?void 0:N.getTime()},[s==null?void 0:s.from]),l=g.useMemo(()=>{var N;return(N=s==null?void 0:s.to)==null?void 0:N.getTime()},[s==null?void 0:s.to]),a=i==null?void 0:i.id,y=n==null?void 0:n.id,v=g.useMemo(()=>{if(r&&r.length>0&&!r.includes("all-stores"))return r.filter(N=>N!=="all-stores"&&N!=="no-stores");{const N=o.filter(j=>j.active===1);return N.length>0?N.map(j=>j.id):void 0}},[r,o]);g.useEffect(()=>{const N=()=>{x.invalidateQueries({queryKey:ce.all})};return window.addEventListener("brandChanged",N),()=>{window.removeEventListener("brandChanged",N)}},[x]);const{data:p,isLoading:c,error:w,refetch:D}=je({queryKey:ce.list({companyUid:y,brandUid:a,startDate:f,endDate:l,storeUids:v,byDays:u==="daily"?1:0,filterType:u}),queryFn:async()=>{if(!a||!y)throw new Error("Brand or company not selected");if(!f||!l)throw new Error("Date range is required");return await _e.getSourcesSummary({companyUid:y,brandUid:a,startDate:f,endDate:l,storeUids:v,byDays:u==="daily"?1:0,limit:100})},enabled:d&&!!a&&!!y&&!!f&&!!l,staleTime:5*60*1e3,gcTime:10*60*1e3,retry:(N,j)=>{var S,C;return(S=j==null?void 0:j.message)!=null&&S.includes("401")||(C=j==null?void 0:j.message)!=null&&C.includes("403")?!1:N<3}}),h=g.useMemo(()=>p!=null&&p.data?_e.processSourcesSummary(p.data):null,[p==null?void 0:p.data]),b=g.useMemo(()=>(h==null?void 0:h.sourceData)||[],[h==null?void 0:h.sourceData]),T=g.useMemo(()=>(p==null?void 0:p.data)||[],[p==null?void 0:p.data]),k=g.useMemo(()=>(h==null?void 0:h.totalRevenue)||0,[h==null?void 0:h.totalRevenue]),F=g.useMemo(()=>(h==null?void 0:h.totalBills)||0,[h==null?void 0:h.totalBills]),$=g.useMemo(()=>(h==null?void 0:h.sourceCount)||0,[h==null?void 0:h.sourceCount]),M=g.useMemo(()=>{if(!T||T.length===0)return{dailyData:[],sourceData:b||[],totalRevenue:0,totalBills:0};const N=m[0]==="all-sources"?T:T.filter(_=>_.source_id===m[0]);let j=0,S=0;N.forEach(_=>{j+=_.revenue_gross||0,S+=_.total_bill||0});const C=new Map,L=new Set;if(s){const _=new Date(s.from),V=new Date(s.to);for(;_<=V;){const E=_.toISOString().split("T")[0];L.add(E),_.setDate(_.getDate()+1)}}L.forEach(_=>{C.set(_,{date:_}),N.forEach(V=>{const E=V.source_name;C.get(_)[E]=0})}),N.forEach(_=>{const V=_.source_name;_.list_data&&_.list_data.length>0&&_.list_data.forEach(E=>{const I=C.get(E.date);I&&(I[V]=(I[V]||0)+E.revenue_gross)})});const A=Array.from(C.values()).sort((_,V)=>_.date.localeCompare(V.date)),Q=m[0]==="all-sources"?b||[]:(b||[]).filter(_=>m.includes(_.sourceId));if(u==="monthly"){const _=new Map;return(s?A.filter(I=>{const H=new Date(I.date);return H>=s.from&&H<=s.to}):A).forEach(I=>{const H=I.date.split("-"),ee=`${H[0]}-${H[1]}`,te=_.get(ee);if(te)N.forEach(G=>{const Y=G.source_name;I[Y]&&(te[Y]=(te[Y]||0)+I[Y])});else{const G={date:ee};N.forEach(Y=>{const be=Y.source_name;G[be]=I[be]||0}),_.set(ee,G)}}),{dailyData:Array.from(_.values()).sort((I,H)=>I.date.localeCompare(H.date)),sourceData:Q,totalRevenue:j,totalBills:S}}else return{dailyData:A,sourceData:Q,totalRevenue:j,totalBills:S}},[T,b,u,m,s]);return{sources:T,processedSources:b,chartData:M,isLoading:c,error:(w==null?void 0:w.message)||null,totalRevenue:k,totalBills:F,sourceCount:$,refetch:()=>{D()},selectedBrand:i,currentBrandStores:o}}function jt({dateRange:t,onDateRangeChange:s,filterType:r="daily",className:m}){const u=i=>{s(i(t))},d=(i,o)=>{if(i)if(r==="monthly")if(o==="from"){const n=he(i);u(x=>({from:n,to:x.to}))}else{const n=it(i);u(x=>({from:x.from,to:n}))}else u(n=>({...n,[o]:i}))};return e.jsxs("div",{className:oe("space-y-4",m),children:[e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{className:"text-sm font-medium",children:r==="monthly"?"Từ tháng":"Từ ngày"}),e.jsxs($e,{children:[e.jsx(Ee,{asChild:!0,children:e.jsxs(P,{variant:"outline",className:oe("w-fit justify-start text-left font-normal",!t.from&&"text-muted-foreground"),children:[e.jsx(Ie,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:t.from?r==="monthly"?re(t.from,"MM/yyyy",{locale:X}):re(t.from,"dd/MM/yyyy",{locale:X}):r==="monthly"?"Chọn tháng bắt đầu":"Chọn ngày bắt đầu"})]})}),e.jsx(Fe,{className:"w-auto p-0",align:"start",children:e.jsx(ke,{mode:"single",selected:t.from,onSelect:i=>d(i,"from"),initialFocus:!0,locale:X})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{className:"text-sm font-medium",children:r==="monthly"?"Đến tháng":"Đến ngày"}),e.jsxs($e,{children:[e.jsx(Ee,{asChild:!0,children:e.jsxs(P,{variant:"outline",className:oe("w-fit justify-start text-left font-normal",!t.to&&"text-muted-foreground"),children:[e.jsx(Ie,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:t.to?r==="monthly"?re(t.to,"MM/yyyy",{locale:X}):re(t.to,"dd/MM/yyyy",{locale:X}):r==="monthly"?"Chọn tháng kết thúc":"Chọn ngày kết thúc"})]})}),e.jsx(Fe,{className:"w-auto p-0",align:"start",children:e.jsx(ke,{mode:"single",selected:t.to,onSelect:i=>d(i,"to"),initialFocus:!0,locale:X,disabled:i=>r==="monthly"?he(i)<he(t.from):i<t.from})})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{className:"text-sm font-medium",children:"Chọn nhanh"}),e.jsx("div",{className:"grid grid-cols-2 gap-2 sm:flex sm:flex-wrap",children:r==="daily"?e.jsxs(e.Fragment,{children:[e.jsx(P,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(we(7)),children:"7 ngày"}),e.jsx(P,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(we(28)),children:"28 ngày"}),e.jsx(P,{variant:"outline",size:"sm",className:"col-span-2 sm:col-span-1 sm:flex-none",onClick:()=>{const i=new Date,o=new Date(i.getFullYear(),i.getMonth(),1);s({from:o,to:i})},children:"Tháng này"})]}):e.jsxs(e.Fragment,{children:[e.jsx(P,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(et()),children:"3 tháng gần đây"}),e.jsx(P,{variant:"outline",size:"sm",className:"col-span-2 sm:col-span-1 sm:flex-none",onClick:()=>{const i=new Date,o=i.getFullYear(),n=new Date(o,i.getMonth()-6,1);s({from:n,to:i})},children:"6 tháng gần đây"})]})})]})]})}function vt({dateRange:t,onDateRangeChange:s,selectedStores:r,onStoreChange:m,selectedSources:u=[],onSourceChange:d,filterType:i,onFilterTypeChange:o,className:n}){const{currentBrandStores:x}=se(),{stores:f}=tt(),l=f.length>0?f:x,a=g.useMemo(()=>l.filter(c=>c.active===1),[l]),{processedSources:y,isLoading:v}=Qe({dateRange:t,selectedStores:r,filterType:i,autoFetch:!0}),p=g.useMemo(()=>y||[],[y]);return e.jsx("div",{className:n,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{className:"text-sm font-medium",children:"Loại bộ lọc"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(P,{variant:i==="monthly"?"default":"outline",size:"sm",onClick:()=>o("monthly"),children:"Theo tháng"}),e.jsx(P,{variant:i==="daily"?"default":"outline",size:"sm",onClick:()=>o("daily"),children:"Theo ngày"})]})]}),e.jsx(jt,{dateRange:t,onDateRangeChange:s,filterType:i}),e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"store-select",className:"text-sm font-medium",children:"Cửa hàng"}),e.jsxs(Te,{value:r[0]||"all-stores",onValueChange:c=>m([c]),children:[e.jsx(Ce,{id:"store-select",className:"w-[180px]",children:e.jsx(Le,{placeholder:"Chọn cửa hàng"})}),e.jsxs(Ae,{children:[e.jsx(z,{value:"all-stores",children:"Tất cả cửa hàng"}),a.map(c=>e.jsx(z,{value:c.id,children:c.store_name},c.id)),a.length===0&&e.jsx(z,{value:"no-stores",disabled:!0,children:"Không có cửa hàng"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"source-select",className:"text-sm font-medium",children:"Nguồn bán hàng"}),e.jsxs(Te,{value:u[0]||"all-sources",onValueChange:c=>{d&&d(c==="all-sources"?["all-sources"]:[c])},children:[e.jsx(Ce,{id:"source-select",className:"w-[180px]",children:e.jsx(Le,{placeholder:"Chọn nguồn"})}),e.jsxs(Ae,{children:[e.jsx(z,{value:"all-sources",children:"Tất cả nguồn"}),p.map(c=>e.jsx(z,{value:c.sourceId,children:c.sourceName},c.sourceId)),v&&e.jsx(z,{value:"loading-sources",disabled:!0,children:"Đang tải nguồn..."}),!v&&p.length===0&&e.jsx(z,{value:"no-sources",disabled:!0,children:"Không có nguồn"})]})]})]})]})]})})}function Nt({sources:t,filterType:s="daily",className:r}){var i;const m=g.useMemo(()=>{var o;return((o=t.data)==null?void 0:o.sourceData)||[]},[(i=t.data)==null?void 0:i.sourceData]),u=g.useMemo(()=>{var o;return!((o=t.data)!=null&&o.dailyData)||t.data.dailyData.length===0?[]:t.data.dailyData},[t.data]),d=["#f97316","#3b82f6","#10b981","#f59e0b","#ef4444","#8b5cf6","#06b6d4"];return e.jsx("div",{className:r,children:e.jsxs("div",{className:"grid gap-3 sm:gap-4 lg:grid-cols-5",children:[e.jsxs("div",{className:"space-y-3 sm:space-y-4 lg:col-span-2",children:[e.jsxs("div",{className:"grid gap-3 sm:grid-cols-2 sm:gap-4",children:[e.jsxs(B,{children:[e.jsxs(U,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(O,{className:"truncate pr-2 text-sm font-medium",title:"Tổng tiền GROSS",children:"Tổng tiền GROSS"}),e.jsx(ct,{className:"h-4 w-4 flex-shrink-0"})]}),e.jsxs(K,{className:"flex flex-1 flex-col justify-center",children:[e.jsx("div",{className:"truncate text-2xl font-bold",title:t.isLoading?"...":t.data?`${t.data.totalRevenue.toLocaleString("vi-VN")} ₫`:"0 ₫",children:t.isLoading?"...":t.data?`${t.data.totalRevenue.toLocaleString("vi-VN")}`:"0 ₫"}),e.jsx("p",{className:"text-muted-foreground truncate text-xs",title:t.error?"Lỗi tải dữ liệu":"Tổng doanh thu brutto",children:t.error?"Lỗi tải dữ liệu":"Tổng doanh thu brutto"})]})]}),e.jsxs(B,{children:[e.jsxs(U,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(O,{className:"truncate pr-2 text-sm font-medium",title:"Số đơn",children:"Số đơn"}),e.jsx(gt,{className:"h-4 w-4 flex-shrink-0"})]}),e.jsxs(K,{className:"flex flex-1 flex-col justify-center",children:[e.jsx("div",{className:"truncate text-2xl font-bold",title:t.isLoading?"...":t.data?t.data.totalBills.toLocaleString("vi-VN"):"0",children:t.isLoading?"...":t.data?t.data.totalBills.toLocaleString("vi-VN"):"0"}),e.jsx("p",{className:"text-muted-foreground truncate text-xs",title:t.error?"Lỗi tải dữ liệu":"Tổng số đơn hàng",children:t.error?"Lỗi tải dữ liệu":"Tổng số đơn hàng"})]})]})]}),e.jsxs(B,{className:"flex h-[300px] flex-col sm:h-[360px]",children:[e.jsx(U,{className:"flex-shrink-0 pb-2",children:e.jsx(O,{className:"truncate text-sm font-medium",title:"Nguồn bán hàng",children:"Nguồn bán hàng"})}),e.jsx(K,{className:"flex flex-1 flex-col",children:e.jsx("div",{className:"flex flex-1 flex-col",children:t.isLoading?e.jsx("div",{className:"flex flex-1 items-center justify-center",children:e.jsx("span",{className:"text-muted-foreground text-sm",children:"Đang tải..."})}):t.error?e.jsx("div",{className:"flex flex-1 items-center justify-center",children:e.jsx("span",{className:"text-sm text-red-500",children:"Lỗi tải dữ liệu nguồn bán hàng"})}):m.length>0?e.jsx(e.Fragment,{children:e.jsx("div",{className:"flex-1 overflow-x-auto overflow-y-auto",children:e.jsxs("table",{className:"w-full min-w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b",children:[e.jsx("th",{className:"text-muted-foreground px-1 py-2 text-left text-xs font-medium sm:px-2 sm:text-sm",children:e.jsx("span",{className:"block truncate",title:"Nguồn",children:"Nguồn"})}),e.jsx("th",{className:"text-muted-foreground px-1 py-2 text-right text-xs font-medium sm:px-2 sm:text-sm",children:e.jsx("span",{className:"block truncate",title:"Tổng tiền GROSS",children:"Tổng tiền GROSS"})}),e.jsx("th",{className:"text-muted-foreground px-1 py-2 text-right text-xs font-medium sm:px-2 sm:text-sm",children:e.jsx("span",{className:"block truncate",title:"Số đơn",children:"Số đơn"})})]})}),e.jsx("tbody",{children:m.map((o,n)=>e.jsxs("tr",{className:"border-b last:border-b-0",children:[e.jsx("td",{className:"px-1 py-2 text-xs sm:px-2 sm:py-3 sm:text-sm",children:e.jsx("span",{className:n===0?"block truncate font-medium text-orange-600":"block truncate font-medium",title:o.sourceName,children:o.sourceName})}),e.jsx("td",{className:"px-1 py-2 text-right text-xs sm:px-2 sm:py-3 sm:text-sm",children:e.jsxs("span",{className:`block truncate font-medium ${n===0?"text-orange-600":""}`,title:`${o.revenue.toLocaleString("vi-VN")} ₫`,children:[o.revenue.toLocaleString("vi-VN")," ₫"]})}),e.jsx("td",{className:"text-muted-foreground px-1 py-2 text-right text-xs sm:px-2 sm:py-3 sm:text-sm",children:e.jsx("span",{className:"block truncate",title:o.bills.toLocaleString("vi-VN"),children:o.bills.toLocaleString("vi-VN")})})]},o.sourceId))})]})})}):e.jsx("div",{className:"flex flex-1 items-center justify-center",children:e.jsx("span",{className:"text-muted-foreground text-sm",children:"Không có dữ liệu nguồn bán hàng"})})})})]})]}),e.jsxs(B,{className:"flex h-[525px] flex-col lg:col-span-3",children:[e.jsx(U,{className:"flex-shrink-0 pb-2",children:e.jsx(O,{className:"truncate text-sm font-medium",title:s==="monthly"?"Tổng tiền GROSS theo nguồn (theo tháng)":"Tổng tiền GROSS theo nguồn (theo ngày)",children:s==="monthly"?"Tổng tiền GROSS theo nguồn (theo tháng)":"Tổng tiền GROSS theo nguồn (theo ngày)"})}),e.jsx(K,{className:"flex flex-1 flex-col",children:t.isLoading?e.jsx("div",{className:"flex flex-1 items-center justify-center",children:e.jsx("span",{className:"text-muted-foreground text-sm",children:"Đang tải..."})}):t.error?e.jsx("div",{className:"flex flex-1 items-center justify-center",children:e.jsx("span",{className:"text-sm text-red-500",children:"Lỗi tải dữ liệu biểu đồ"})}):u.length>0?e.jsx("div",{className:"flex-1",children:e.jsx(ve,{width:"100%",height:"100%",children:e.jsxs(He,{data:u,children:[e.jsx(De,{strokeDasharray:"3 3"}),e.jsx(me,{dataKey:"date",tickFormatter:o=>{if(s==="monthly"){const[n,x]=o.split("-");return`${x}/${n.slice(-2)}`}else{const n=new Date(o),x=n.getDate(),f=n.getMonth()+1;return`${x}/${f}`}},fontSize:12,tickLine:!1,axisLine:!1,interval:0,angle:-35,textAnchor:"end",height:60}),e.jsx(ue,{fontSize:12,tickLine:!1,axisLine:!1,tickCount:6,domain:["dataMin","dataMax"],tickFormatter:o=>o>=1e6?`${Math.round(o/1e6)}tr`:o>=1e3?`${Math.round(o/1e3)}k`:o===0?"0":Math.round(o).toString()}),e.jsx(Ne,{formatter:(o,n)=>[typeof o=="number"?o.toLocaleString("vi-VN")+" ₫":o,n],labelFormatter:o=>{if(s==="monthly"){const[n,x]=o.split("-");return`Tháng ${x}/${n}`}else{const n=new Date(o);return`Ngày ${n.getDate()}/${n.getMonth()+1}/${n.getFullYear()}`}}}),e.jsx(Se,{wrapperStyle:{fontSize:"12px"}}),m.map((o,n)=>e.jsx(qe,{type:"monotone",dataKey:o.sourceName,name:o.sourceName,stroke:d[n%d.length],strokeWidth:2,dot:!1,activeDot:{r:5}},o.sourceId))]})})}):e.jsx("div",{className:"flex flex-1 items-center justify-center",children:e.jsx("span",{className:"text-muted-foreground text-sm",children:"Không có dữ liệu biểu đồ"})})})]})]})})}function St({sources:t,dateRange:s,filterType:r="daily",className:m}){const u=g.useMemo(()=>{if(!t.data||t.data.length===0)return[];const d=new Date(s.startDate),i=new Date(s.endDate);if(r==="monthly"){const o=new Map;t.data.forEach(l=>{l.list_data&&l.list_data.length>0&&l.list_data.forEach(a=>{const y=new Date(a.date),v=`${y.getFullYear()}-${String(y.getMonth()+1).padStart(2,"0")}`,p=o.get(v);p?p.revenue+=a.revenue_gross:o.set(v,{revenue:a.revenue_gross})})});const n=[],x=new Date(d.getFullYear(),d.getMonth(),1),f=new Date(i.getFullYear(),i.getMonth(),1);for(;x<=f;){const l=`${x.getFullYear()}-${String(x.getMonth()+1).padStart(2,"0")}`;n.push(l),x.setMonth(x.getMonth()+1)}return n.map(l=>{const a=o.get(l);return{date:l,revenue:(a==null?void 0:a.revenue)||0}})}else{const o=new Map;t.data.forEach(l=>{l.list_data&&l.list_data.length>0&&l.list_data.forEach(a=>{const y=o.get(a.date);y?y.revenue+=a.revenue_gross:o.set(a.date,{revenue:a.revenue_gross})})});const n=[],x=new Date(d),f=new Date(i);for(;x<=f;){const l=x.getFullYear(),a=String(x.getMonth()+1).padStart(2,"0"),y=String(x.getDate()).padStart(2,"0");n.push(`${l}-${a}-${y}`),x.setDate(x.getDate()+1)}return n.map(l=>{const a=o.get(l);return{date:l,revenue:(a==null?void 0:a.revenue)||0}})}},[t.data,s.startDate,s.endDate,r]);return e.jsx("div",{className:m,children:e.jsx("div",{className:"grid gap-4 lg:grid-cols-5",children:e.jsxs(B,{className:"flex h-[256px] flex-col sm:col-span-1 lg:col-span-5",children:[e.jsx(U,{className:"flex-shrink-0 pb-2",children:e.jsx(O,{className:"text-sm font-medium",children:r==="monthly"?"Biểu đồ doanh thu theo tháng":"Biểu đồ doanh thu theo ngày"})}),e.jsx(K,{className:"flex flex-1 flex-col",children:t.isLoading?e.jsx("div",{className:"flex flex-1 items-center justify-center",children:e.jsx("span",{className:"text-muted-foreground text-sm",children:"Đang tải..."})}):t.error?e.jsx("div",{className:"flex flex-1 items-center justify-center",children:e.jsx("span",{className:"text-sm text-red-500",children:"Lỗi tải dữ liệu biểu đồ"})}):u.length>0?e.jsx("div",{className:"flex-1",children:e.jsx(ve,{width:"100%",height:"100%",children:e.jsxs(yt,{data:u,children:[e.jsx(De,{strokeDasharray:"3 3"}),e.jsx(me,{dataKey:"date",tickFormatter:d=>{if(r==="monthly"){const[i,o]=d.split("-");return`${o}/${i.slice(-2)}`}else{const i=new Date(d),o=i.getDate(),n=i.getMonth()+1;return`${o}/${n}`}},fontSize:12,tickLine:!1,axisLine:!1,interval:0,angle:-35,textAnchor:"end",height:60}),e.jsx(ue,{fontSize:12,tickLine:!1,axisLine:!1,tickCount:6,domain:["dataMin","dataMax"],tickFormatter:d=>d>=1e6?`${Math.round(d/1e6)}`:d>=1e3?`${Math.round(d/1e3)}`:d===0?"0":Math.round(d).toString()}),e.jsx(Ne,{formatter:(d,i)=>[typeof d=="number"?ie.formatCurrency(d):d,"Tổng GROSS"],labelFormatter:d=>{if(r==="monthly"){const[i,o]=d.split("-");return`Tháng ${o}/${i}`}else{const i=new Date(d);return`Ngày ${i.getDate()}/${i.getMonth()+1}/${i.getFullYear()}`}}}),e.jsx(Se,{wrapperStyle:{fontSize:"12px"}}),e.jsx(Ge,{type:"monotone",dataKey:"revenue",stroke:"#f97316",strokeWidth:2,fill:"#f97316",fillOpacity:.3,name:"Doanh thu"})]})})}):e.jsx("div",{className:"flex flex-1 items-center justify-center",children:e.jsx("span",{className:"text-muted-foreground text-sm",children:"Không có dữ liệu biểu đồ"})})})]})})})}const Ue=new Map,xe=new Map,Dt=5*60*1e3,bt={getDeletedOrders:async t=>{const s=`${t.companyUid}-${t.brandUid}-${t.storeUid||"all"}-${t.startDate}-${t.endDate}-${t.page||1}`,r=Ue.get(s);if(r&&Date.now()-r.timestamp<Dt)return r.data;const m=xe.get(s);if(m)return m;const u=(async()=>{var d,i,o,n,x;try{const f=new URLSearchParams({company_uid:t.companyUid,brand_uid:t.brandUid,start_date:t.startDate.toString(),end_date:t.endDate.toString(),page:(t.page||1).toString(),store_open_at:"0"});t.storeUid&&f.set("store_uid",t.storeUid);const l=await Ye.get(`/v3/pos-cms/report/sale-edit-delete?${f.toString()}`,{headers:{Accept:"application/json, text/plain, */*","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});if(!l.data||typeof l.data!="object")throw new Error("Invalid response format from deleted orders API");return Ue.set(s,{data:l.data,timestamp:Date.now()}),l.data}catch(f){throw f.code==="ECONNABORTED"||(d=f.message)!=null&&d.includes("timeout")?new Error("Request timeout - server is taking too long to respond. Try reducing the date range."):((i=f.response)==null?void 0:i.status)===504?new Error("Gateway timeout (504) - server is overloaded. Please try again later."):((o=f.response)==null?void 0:o.status)===503?new Error("Service unavailable (503) - server is temporarily down. Please try again later."):((n=f.response)==null?void 0:n.status)>=500?new Error(`Server error (${f.response.status}) - please try again later.`):((x=f.response)==null?void 0:x.status)===429?new Error("Too many requests - please wait a moment before trying again."):f}finally{xe.delete(s)}})();return xe.set(s,u),u},processDeletedOrdersSummary:t=>{if(!t.data||t.data.length===0)return{storeStats:[],employeeStats:[],reasonStats:[],totalDeleted:0,totalDeletedAmount:0,totalOriginalAmount:0,overallDeletedPercentage:0};const s=t.data.filter(l=>l.status==="DELETE"),r=new Map,m=new Map,u=new Map;let d=0,i=0,o=0;s.forEach(l=>{r.has(l.store_uid)||r.set(l.store_uid,[]),r.get(l.store_uid).push(l),m.has(l.employee_uid)||m.set(l.employee_uid,[]),m.get(l.employee_uid).push(l);const a=l.sale_note||"Không có lý do";u.has(a)||u.set(a,[]),u.get(a).push(l),d++,i+=l.total_amount_origin,o+=l.total_amount_origin});const n=Array.from(r.entries()).map(([l,a])=>{const y=a[0].store_name,v=a.reduce((h,b)=>h+b.total_amount_origin,0),p=new Map;a.forEach(h=>{p.has(h.employee_uid)||p.set(h.employee_uid,[]),p.get(h.employee_uid).push(h)});const c=Array.from(p.entries()).map(([h,b])=>({employeeUid:h,employeeName:b[0].employee_name,deletedCount:b.length,deletedAmount:b.reduce((T,k)=>T+k.total_amount_origin,0)})).sort((h,b)=>b.deletedCount-h.deletedCount).slice(0,5),w=new Map;a.forEach(h=>{const b=h.sale_note||"Không có lý do";w.has(b)||w.set(b,[]),w.get(b).push(h)});const D=Array.from(w.entries()).map(([h,b])=>({reason:h,count:b.length,amount:b.reduce((T,k)=>T+k.total_amount_origin,0)})).sort((h,b)=>b.count-h.count).slice(0,5);return{storeUid:l,storeName:y,totalDeleted:a.length,totalDeletedAmount:v,totalOriginalAmount:v,deletedPercentage:100,topEmployees:c,topReasons:D}}).sort((l,a)=>a.totalDeleted-l.totalDeleted),x=Array.from(m.entries()).map(([l,a])=>{const y=a[0].employee_name,v=a.reduce((c,w)=>c+w.total_amount_origin,0),p=[...new Set(a.map(c=>c.store_name))];return{employeeUid:l,employeeName:y,totalDeleted:a.length,totalDeletedAmount:v,storeCount:p.length,stores:p}}).sort((l,a)=>a.totalDeleted-l.totalDeleted),f=Array.from(u.entries()).map(([l,a])=>{const y=a.reduce((p,c)=>p+c.total_amount_origin,0),v=[...new Set(a.map(p=>p.store_uid))];return{reason:l,count:a.length,amount:y,storeCount:v.length}}).sort((l,a)=>a.count-l.count);return{storeStats:n,employeeStats:x,reasonStats:f,totalDeleted:d,totalDeletedAmount:i,totalOriginalAmount:o,overallDeletedPercentage:o>0?i/o*100:0}},formatCurrency:t=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND",minimumFractionDigits:0,maximumFractionDigits:0}).format(t)},wt=(t,s)=>`deleted-stores-${t}-${s}`,Mt=(t,s)=>{try{localStorage.setItem(t,JSON.stringify(s))}catch{}},_t=t=>{try{const s=localStorage.getItem(t);if(s){const r=JSON.parse(s),m=24*60*60*1e3;if(Date.now()-r.timestamp<m)return r}}catch{}return null},ge={all:["deleted-stores"],lists:()=>[...ge.all,"list"],list:t=>[...ge.lists(),t]};function Tt(t={}){const{dateRange:s,selectedStores:r=["all-stores"],limit:m=5,autoFetch:u=!0}=t,{selectedBrand:d,currentBrandStores:i}=se(),{company:o}=de(),[n,x]=g.useState(null),[f,l]=g.useState(!0),{startTime:a,endTime:y}=g.useMemo(()=>{if(!(s!=null&&s.from)||!(s!=null&&s.to))return{startTime:null,endTime:null};const j=new Date(s.from);j.setHours(0,0,0,0);const S=new Date(s.to);return S.setHours(23,59,59,999),{startTime:j.getTime(),endTime:S.getTime()}},[s]),v=d==null?void 0:d.id,p=o==null?void 0:o.id,c=g.useMemo(()=>!a||!y?null:wt(a,y),[a,y]);g.useEffect(()=>{if(c){const j=_t(c);j&&x(j)}l(!1)},[c]);const w=g.useMemo(()=>{if(r&&r.length>0&&!r.includes("all-stores"))return r.filter(j=>j!=="all-stores"&&j!=="no-stores");{const j=i.filter(S=>S.active===1);return j.length>0?j.map(S=>S.id):void 0}},[r,i]),{data:D=[],isLoading:h,error:b,refetch:T}=je({queryKey:ge.list({companyUid:p,brandUid:v,startDate:a||void 0,endDate:y||void 0,storeUids:w,limit:m,cacheKey:c}),queryFn:async()=>{if(!v||!p||!a||!y)throw new Error("Brand, company, or date range not selected");const j=w?i.filter(A=>w.includes(A.id)&&A.active===1):i.filter(A=>A.active===1);if(j.length===0)return[];const S=[],C=3;for(let A=0;A<j.length;A+=C){const _=j.slice(A,A+C).map(async E=>{try{const H=(await bt.getDeletedOrders({companyUid:p,brandUid:v,storeUid:E.id,startDate:a,endDate:y,page:1})).data.filter(G=>G.status==="DELETE"),ee=H.length,te=H.reduce((G,Y)=>G+Y.total_amount_origin,0);return{storeUid:E.id,storeName:E.store_name,totalDeleted:ee,totalDeletedAmount:te}}catch{return{storeUid:E.id,storeName:E.store_name,totalDeleted:0,totalDeletedAmount:0}}}),V=await Promise.all(_);S.push(...V),A+C<j.length&&await new Promise(E=>setTimeout(E,300))}const L=S.filter(A=>A.totalDeleted>0).sort((A,Q)=>Q.totalDeleted-A.totalDeleted).slice(0,m);if(c&&L.length>0){const A={topStores:L,totalStores:L.length,totalDeleted:L.reduce((Q,_)=>Q+_.totalDeleted,0),timestamp:Date.now()};Mt(c,A)}return L},enabled:u&&!!v&&!!p&&!!a&&!!y,staleTime:5*60*1e3,gcTime:10*60*1e3,refetchInterval:15*60*1e3,refetchIntervalInBackground:!0,retry:(j,S)=>{var C,L;return(C=S==null?void 0:S.message)!=null&&C.includes("401")||(L=S==null?void 0:S.message)!=null&&L.includes("403")?!1:j<2}}),k=D.length>0,F=k?D:(n==null?void 0:n.topStores)||[],$=k?D.length:(n==null?void 0:n.totalStores)||0,M=k?D.reduce((j,S)=>j+S.totalDeleted,0):(n==null?void 0:n.totalDeleted)||0;return{topStores:F,isLoading:f||h,error:(b==null?void 0:b.message)||null,totalStores:$,totalDeleted:M,refetch:()=>{T()}}}const Ct={title:"text-sm sm:text-base lg:text-lg font-medium",titleTruncate:"text-sm sm:text-base lg:text-lg font-medium truncate",subtitle:"text-xs sm:text-sm text-muted-foreground",subtitleTruncate:"text-xs sm:text-sm text-muted-foreground truncate",value:"text-lg sm:text-xl lg:text-2xl font-bold",valueTruncate:"text-lg sm:text-xl lg:text-2xl font-bold truncate",valueSmall:"text-sm sm:text-base lg:text-lg font-semibold",valueSmallTruncate:"text-sm sm:text-base lg:text-lg font-semibold truncate"},Lt={textBreak:"break-words"},q={typography:Ct,utility:Lt};function fe({children:t,className:s,variant:r="title",truncate:m=!1,title:u}){const d={title:m?q.typography.titleTruncate:q.typography.title,subtitle:m?q.typography.subtitleTruncate:q.typography.subtitle,value:m?q.typography.valueTruncate:q.typography.value,"value-small":m?q.typography.valueSmallTruncate:q.typography.valueSmall};return e.jsx("div",{className:oe(d[r],!m&&q.utility.textBreak,s),title:u||(m&&typeof t=="string"?t:void 0),children:t})}function At({dateRange:t,selectedStores:s,className:r}){var f,l;const m=g.useMemo(()=>!(t!=null&&t.from)||!(t!=null&&t.to)?!1:(t.to.getTime()-t.from.getTime())/(1e3*60*60*24)>90,[t]),{topStores:u,isLoading:d,error:i,refetch:o}=Tt({dateRange:t,selectedStores:s,limit:7,autoFetch:!m}),n=(f=t==null?void 0:t.from)==null?void 0:f.getTime(),x=(l=t==null?void 0:t.to)==null?void 0:l.getTime();return g.useEffect(()=>{t!=null&&t.from&&(t!=null&&t.to)&&!m&&o()},[n,x,m,o,t==null?void 0:t.from,t==null?void 0:t.to]),typeof window<"u"&&(window.forceRefreshDeletedStores=o),m?e.jsx("div",{className:r,children:e.jsxs(ht,{className:"border-yellow-200 bg-yellow-50",children:[e.jsx(Be,{className:"h-4 w-4 text-yellow-600"}),e.jsx(xt,{className:"text-xs text-yellow-800",children:"Khoảng thời gian quá lớn (hơn 3 tháng). Vui lòng chọn khoảng thời gian nhỏ hơn để tải dữ liệu hoá đơn huỷ."})]})}):d?e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((a,y)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"h-6 w-6 animate-pulse rounded-full bg-gray-200"}),e.jsx("div",{className:"h-4 w-32 animate-pulse rounded bg-gray-200"})]}),e.jsx("div",{className:"h-6 w-16 animate-pulse rounded bg-gray-200"})]},y))}):i?e.jsx("div",{className:r,children:e.jsxs("div",{className:"py-4 text-center",children:[e.jsx(Be,{className:"text-destructive mx-auto mb-2 h-6 w-6"}),e.jsx(fe,{variant:"subtitle",className:"text-destructive text-xs",children:"Không thể tải dữ liệu"})]})}):u.length===0?e.jsx("div",{className:r,children:e.jsxs("div",{className:"py-4 text-center",children:[e.jsx(ft,{className:"mx-auto mb-2 h-6 w-6 text-green-500"}),e.jsx(fe,{variant:"subtitle",className:"text-xs text-green-600",children:"Không có đơn hàng bị huỷ"})]})}):e.jsx("div",{className:r,children:e.jsxs("div",{className:"space-y-3",children:[u.map((a,y)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex min-w-0 flex-1 items-center space-x-3",children:[e.jsx(Z,{variant:y===0?"destructive":y===1?"secondary":"outline",className:"flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full p-0 text-xs font-medium",children:y+1}),e.jsx("div",{className:"min-w-0 flex-1",children:e.jsx(fe,{variant:"subtitle",truncate:!0,title:a.storeName,className:"font-medium",children:a.storeName})})]}),e.jsx("div",{className:"text-right",children:e.jsx(Z,{variant:y===0?"destructive":"secondary",className:"text-xs",children:a.totalDeleted})})]},a.storeUid)),u.length===0&&e.jsx("div",{className:"py-4 text-center",children:e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không có dữ liệu"})})]})})}const Oe=new Map,pe=new Map,kt=5*60*1e3,Ke={getItemsSummary:async t=>{var d;const s=`${t.companyUid}-${t.brandUid}-${t.startDate}-${t.endDate}-${((d=t.storeUids)==null?void 0:d.join(","))||"all"}-${t.byDays||1}`,r=Oe.get(s);if(r&&Date.now()-r.timestamp<kt)return r.data;const m=pe.get(s);if(m)return m;const u=(async()=>{var i,o,n,x,f;try{const l=new URLSearchParams({brand_uid:t.brandUid,company_uid:t.companyUid,start_date:t.startDate.toString(),end_date:t.endDate.toString(),store_open_at:"0",by_days:(t.byDays||0).toString(),limit:(t.limit||100).toString(),order_by:"quantity_sold"});t.storeUids&&t.storeUids.length>0&&l.set("list_store_uid",t.storeUids.join(","));const a=await Ye.get(`/v1/reports/sale-summary/items?${l.toString()}`,{headers:{Accept:"application/json, text/plain, */*","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:45e3});if(!a.data||typeof a.data!="object")throw new Error("Invalid response format from items API");return Oe.set(s,{data:a.data,timestamp:Date.now()}),a.data}catch(l){throw l.code==="ECONNABORTED"||(i=l.message)!=null&&i.includes("timeout")?new Error("Request timeout - server is taking too long to respond. Try reducing the date range or number of stores."):((o=l.response)==null?void 0:o.status)===504?new Error("Gateway timeout (504) - server is overloaded. Please try again later or reduce the data range."):((n=l.response)==null?void 0:n.status)===503?new Error("Service unavailable (503) - server is temporarily down. Please try again later."):((x=l.response)==null?void 0:x.status)>=500?new Error(`Server error (${l.response.status}) - please try again later.`):((f=l.response)==null?void 0:f.status)===429?new Error("Too many requests - please wait a moment before trying again."):l}finally{pe.delete(s)}})();return pe.set(s,u),u},processItemsSummary:t=>{if(!t.data||!t.data.list_data_item_return||t.data.list_data_item_return.length===0)return{itemData:[],totalQuantity:0,totalRevenue:0,totalRevenueNet:0,itemCount:0};const s=t.data.list_data_item_return;let r=0,m=0,u=0;const d=s.map(i=>{const o=i.quantity_sold||0,n=i.revenue_gross||0,x=i.revenue_net||0;return r+=o,m+=n,u+=x,{itemId:i.item_id,itemName:i.item_name,itemClass:i.item_class_name,itemType:i.item_type_name,quantitySold:o,revenue:n,revenueNet:x,percentage:0,unit:i.unit_name}});return m>0&&d.forEach(i=>{i.percentage=i.revenue/m*100}),{itemData:d,totalQuantity:r,totalRevenue:m,totalRevenueNet:u,itemCount:s.length}},formatCurrency:t=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND",minimumFractionDigits:0,maximumFractionDigits:0}).format(t)},ye={all:["items"],lists:()=>[...ye.all,"list"],list:t=>[...ye.lists(),t]};function $t(t={}){var $;const{dateRange:s,selectedStores:r=["all-stores"],autoFetch:m=!0}=t,{selectedBrand:u,currentBrandStores:d}=se(),{company:i}=de(),o=g.useMemo(()=>{var M;return(M=s==null?void 0:s.from)==null?void 0:M.getTime()},[s==null?void 0:s.from]),n=g.useMemo(()=>{var M;return(M=s==null?void 0:s.to)==null?void 0:M.getTime()},[s==null?void 0:s.to]),x=u==null?void 0:u.id,f=i==null?void 0:i.id,l=g.useMemo(()=>{if(r&&r.length>0&&!r.includes("all-stores"))return r.filter(M=>M!=="all-stores"&&M!=="no-stores");{const M=d.filter(N=>N.active===1);return M.length>0?M.map(N=>N.id):void 0}},[r,d]),{data:a,isLoading:y,error:v,refetch:p}=je({queryKey:ye.list({companyUid:f,brandUid:x,startDate:o,endDate:n,storeUids:l,byDays:0,orderBy:"quantity_sold"}),queryFn:async()=>{if(!x||!f)throw new Error("Brand or company not selected");if(!o||!n)throw new Error("Date range is required");const M=Math.ceil((n-o)/(1e3*60*60*24)),N=(l==null?void 0:l.length)||d.filter(S=>S.active===1).length||1;let j=1e3;return(M>90||N>10)&&(j=500),(M>180||N>20)&&(j=200),await Ke.getItemsSummary({companyUid:f,brandUid:x,startDate:o,endDate:n,storeUids:l,byDays:0,limit:j})},enabled:m&&!!x&&!!f&&!!o&&!!n,staleTime:5*60*1e3,gcTime:10*60*1e3,retry:(M,N)=>{var j,S;return(j=N==null?void 0:N.message)!=null&&j.includes("401")||(S=N==null?void 0:N.message)!=null&&S.includes("403")?!1:M<3}}),c=g.useMemo(()=>a?Ke.processItemsSummary(a):null,[a]),w=g.useMemo(()=>(c==null?void 0:c.itemData)||[],[c==null?void 0:c.itemData]),D=g.useMemo(()=>{var M;return((M=a==null?void 0:a.data)==null?void 0:M.list_data_item_return)||[]},[($=a==null?void 0:a.data)==null?void 0:$.list_data_item_return]),h=g.useMemo(()=>(c==null?void 0:c.totalQuantity)||0,[c==null?void 0:c.totalQuantity]),b=g.useMemo(()=>(c==null?void 0:c.totalRevenue)||0,[c==null?void 0:c.totalRevenue]),T=g.useMemo(()=>(c==null?void 0:c.totalRevenueNet)||0,[c==null?void 0:c.totalRevenueNet]),k=g.useMemo(()=>(c==null?void 0:c.itemCount)||0,[c==null?void 0:c.itemCount]),F=g.useMemo(()=>({dailyData:[],itemData:w||[],totalQuantity:h,totalRevenue:b,totalRevenueNet:T}),[w,h,b,T]);return{items:D,processedItems:w,chartData:F,isLoading:y,error:(v==null?void 0:v.message)||null,totalQuantity:h,totalRevenue:b,totalRevenueNet:T,itemCount:k,refetch:()=>{p()},selectedBrand:u,currentBrandStores:d}}function Et(t){const{processedItems:s,isLoading:r,error:m}=$t({dateRange:t,autoFetch:!0});return{topItems:g.useMemo(()=>!s||s.length===0?[]:s.slice(-5).reverse(),[s]),isLoading:r,error:m}}function Ft({dateRange:t}){const{topItems:s,isLoading:r,error:m}=Et(t);return r?e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((u,d)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"h-6 w-6 animate-pulse rounded-full bg-gray-200"}),e.jsx("div",{className:"h-4 w-32 animate-pulse rounded bg-gray-200"})]}),e.jsx("div",{className:"h-6 w-16 animate-pulse rounded bg-gray-200"})]},d))}):m?e.jsx("div",{className:"py-8 text-center",children:e.jsxs("p",{className:"text-sm text-red-500",children:["Lỗi tải dữ liệu: ",m]})}):!s||s.length===0?e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không có dữ liệu mặt hàng"})}):e.jsx("div",{className:"space-y-3",children:s.map((u,d)=>e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsxs("div",{className:"flex min-w-0 flex-1 items-center space-x-2",children:[e.jsx(Z,{variant:d===0?"destructive":d===1?"secondary":"outline",className:"flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full p-0 text-xs font-medium",children:d+1}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"cursor-help truncate text-sm font-medium",title:u.itemName,children:u.itemName}),e.jsxs("div",{className:"text-muted-foreground truncate text-xs",children:[u.itemClass," • ",u.itemType]})]})]}),e.jsx("div",{className:"flex flex-shrink-0 items-center justify-end",children:e.jsxs(Z,{variant:"outline",className:"bg-red-100 text-xs font-bold whitespace-nowrap text-red-800",children:[u.quantitySold.toLocaleString("vi-VN")," ",u.unit]})})]},u.itemId))})}function It(t={}){const{period:s="this-month",customDateRange:r,storeIds:m,filterType:u="daily",autoFetch:d=!0}=t,[i,o]=g.useState(null),[n,x]=g.useState(null),[f,l]=g.useState(!1),[a,y]=g.useState(null),{selectedBrand:v}=Ve(),{company:p}=de(),{currentBrandStores:c}=se(),w=g.useMemo(()=>c.filter(T=>T.active===1),[c]),D=g.useMemo(()=>r?{startDate:r.startDate,endDate:r.endDate}:null,[r==null?void 0:r.startDate,r==null?void 0:r.endDate]),h=g.useMemo(()=>!m||m.length===0?null:[...m].sort(),[m?m.sort().join(","):null]),b=g.useCallback(async()=>{if(!v||!p){y("Brand or company not selected");return}l(!0),y(null);try{const{startDate:T,endDate:k}=D||ie.getDateRange(s);let F;h&&h.length>0?(F=h.filter(N=>N!=="all-stores"&&N!=="no-stores"),F.length===0&&(F=void 0)):F=w.length>0?w.map(N=>N.id):void 0;const $=await ie.getRevenueSummary({companyUid:p.id,brandUid:v.id,startDate:T,endDate:k,storeUids:F,byDays:u==="daily"?1:0,limit:1e3}),M=ie.processRevenueSummary($.data);x($),o(M)}catch(T){const k=T instanceof Error?T.message:"Failed to fetch revenue data";y(k)}finally{l(!1)}},[v==null?void 0:v.id,p==null?void 0:p.id,D==null?void 0:D.startDate,D==null?void 0:D.endDate,s,h==null?void 0:h.join(","),w.length,u]);return g.useEffect(()=>{d&&v&&p&&b()},[d,v==null?void 0:v.id,p==null?void 0:p.id,D==null?void 0:D.startDate,D==null?void 0:D.endDate,s,h==null?void 0:h.join(","),u]),{summary:i,rawData:n,isLoading:f,error:a,refetch:b}}function R(t={}){const{dateRange:s,limit:r=5,sortBy:m="sales",order:u="asc",filterType:d="daily",selectedStores:i=[]}=t,o=g.useMemo(()=>{if(!(!(s!=null&&s.from)||!(s!=null&&s.to)))return{startDate:s.from.getTime(),endDate:s.to.getTime()}},[s==null?void 0:s.from,s==null?void 0:s.to]),{rawData:n,isLoading:x,error:f,refetch:l}=It({customDateRange:o,storeIds:i,filterType:d,autoFetch:!0}),a=g.useMemo(()=>!(n!=null&&n.data)||n.data.length===0?[]:[...n.data.map(c=>{var b,T,k,F;let w=c.revenue_gross,D=c.total_sales,h=c.peo_count;if(c.list_data&&c.list_data.length>0)if(d==="monthly"){const $=new Map;c.list_data.forEach(j=>{const S=new Date(j.tran_date),C=`${S.getFullYear()}-${String(S.getMonth()+1).padStart(2,"0")}`;$.has(C)||$.set(C,{revenue:0,sales:0,bills:0});const L=$.get(C);L.revenue+=j.revenue_gross,L.sales+=j.total_sales,L.bills+=j.peo_count});const M=`${(b=s==null?void 0:s.from)==null?void 0:b.getFullYear()}-${String((((T=s==null?void 0:s.from)==null?void 0:T.getMonth())||0)+1).padStart(2,"0")}`,N=`${(k=s==null?void 0:s.to)==null?void 0:k.getFullYear()}-${String((((F=s==null?void 0:s.to)==null?void 0:F.getMonth())||0)+1).padStart(2,"0")}`;w=0,D=0,h=0;for(const[j,S]of $)j>=M&&j<=N&&(w+=S.revenue,D+=S.sales,h+=S.bills)}else{const $=new Date((s==null?void 0:s.from)||new Date);$.setHours(0,0,0,0);const M=new Date((s==null?void 0:s.to)||new Date);M.setHours(23,59,59,999);const N=$.getTime(),j=M.getTime(),S=c.list_data.filter(C=>{const L=C.tran_date;return L>=N&&L<=j});w=S.reduce((C,L)=>C+L.revenue_gross,0),D=S.reduce((C,L)=>C+L.total_sales,0),h=S.reduce((C,L)=>C+L.peo_count,0)}return{storeId:c.store_uid,storeName:c.store_name,totalSales:D,totalBills:h,totalRevenue:w}})].sort((c,w)=>{let D,h;switch(m){case"bills":D=c.totalBills,h=w.totalBills;break;case"revenue":D=c.totalRevenue,h=w.totalRevenue;break;case"sales":default:D=c.totalSales,h=w.totalSales;break}return u==="asc"?D-h:h-D}).slice(0,r),[n==null?void 0:n.data,m,u,r,s==null?void 0:s.from,s==null?void 0:s.to,d]);return g.useMemo(()=>({topStores:a,isLoading:x,error:f,refetch:l,rawData:n}),[a,x,f,l,n])}function Bt(t,s){return R({dateRange:t,limit:5,sortBy:"sales",order:"asc",filterType:s})}function Ut(t,s){return R({dateRange:t,limit:5,sortBy:"sales",order:"desc",filterType:s})}function Ot(t,s){return R({dateRange:t,limit:5,sortBy:"bills",order:"asc",filterType:s})}function Kt(t,s){return R({dateRange:t,limit:5,sortBy:"bills",order:"desc",filterType:s})}function ze(t,s){return R({dateRange:t,limit:5,sortBy:"revenue",order:"asc",filterType:s})}function Vt(t,s){return R({dateRange:t,limit:5,sortBy:"revenue",order:"desc",filterType:s})}function le({dateRange:t,filterType:s,type:r}){const m=Bt(t,s),u=Ut(t,s),d=Ot(t,s),i=Kt(t,s),o=ze(t,s),n=Vt(t,s),x=()=>{switch(r){case"least-sales":return m;case"most-sales":return u;case"least-orders":return d;case"most-orders":return i;case"least-revenue":return o;case"most-revenue":return n;default:return m}},{topStores:f,isLoading:l,error:a}=x(),v=(()=>{switch(r){case"least-sales":return{primaryMetric:"sales",badgeVariant:"destructive",isAscending:!0};case"most-sales":return{primaryMetric:"sales",badgeVariant:"default",isAscending:!1};case"least-orders":return{primaryMetric:"bills",badgeVariant:"destructive",isAscending:!0};case"most-orders":return{primaryMetric:"bills",badgeVariant:"default",isAscending:!1};case"least-revenue":return{primaryMetric:"revenue",badgeVariant:"destructive",isAscending:!0};case"most-revenue":return{primaryMetric:"revenue",badgeVariant:"default",isAscending:!1};default:return{primaryMetric:"sales",badgeVariant:"destructive",isAscending:!0}}})();return l?e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((p,c)=>e.jsxs("div",{className:"flex animate-pulse items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"bg-muted h-6 w-6 rounded-full"}),e.jsx("div",{className:"bg-muted h-4 w-32 rounded"})]}),e.jsx("div",{className:"bg-muted h-4 w-16 rounded"})]},c))})}):a?e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"text-center text-sm text-red-500",children:["Lỗi tải dữ liệu: ",a]})}):f.length===0?e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"text-muted-foreground text-center text-sm",children:"Không có dữ liệu cửa hàng"})}):e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"space-y-3",children:f.map((p,c)=>{const D=(()=>{switch(v.primaryMetric){case"sales":return p.totalSales;case"bills":return p.totalBills;case"revenue":return p.totalRevenue;default:return p.totalSales}})();return e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsxs("div",{className:"flex min-w-0 flex-1 items-center space-x-2",children:[e.jsx(Z,{variant:c===0?v.badgeVariant:c===1?"secondary":"outline",className:"flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full p-0 text-xs font-medium",children:c+1}),e.jsx("div",{className:"min-w-0 cursor-help truncate text-sm font-medium",title:p.storeName,children:p.storeName})]}),e.jsx("div",{className:"flex flex-shrink-0 items-center justify-end",children:e.jsx(Z,{variant:"outline",className:"bg-blue-100 text-xs font-bold whitespace-nowrap text-blue-800",children:(v.primaryMetric==="revenue",D.toLocaleString("vi-VN"))})})]},p.storeId)})})})}function Pt(){const{dateRange:t,filterType:s}=Pe(),{topStores:r,isLoading:m,error:u,rawData:d}=ze(t,s),i=g.useMemo(()=>{if(!r||r.length===0||!(d!=null&&d.data))return[];const n=[],x=new Map;if(d.data.forEach(f=>{x.set(f.store_uid,f)}),s==="monthly"){const f=new Map;r.slice(0,5).forEach(l=>{const a=x.get(l.storeId);a!=null&&a.list_data&&a.list_data.forEach(y=>{const v=new Date(y.tran_date),p=`${v.getFullYear()}-${String(v.getMonth()+1).padStart(2,"0")}`;f.has(p)||f.set(p,{date:p});const c=f.get(p);c[l.storeName]=(c[l.storeName]||0)+y.revenue_gross})}),n.push(...Array.from(f.values()).sort((l,a)=>l.date.localeCompare(a.date)))}else{const f=new Map;r.slice(0,5).forEach(l=>{const a=x.get(l.storeId);a!=null&&a.list_data&&a.list_data.forEach(y=>{const v=y.date;f.has(v)||f.set(v,{date:v});const p=f.get(v);p[l.storeName]=y.revenue_gross})}),n.push(...Array.from(f.values()).sort((l,a)=>l.date.localeCompare(a.date)))}return n},[r,s,d]),o=["#ef4444","#f97316","#f59e0b","#eab308","#84cc16"];return m?e.jsx("div",{className:"flex h-[300px] items-center justify-center",children:e.jsx("span",{className:"text-muted-foreground text-sm",children:"Đang tải..."})}):u?e.jsx("div",{className:"flex h-[300px] items-center justify-center",children:e.jsx("span",{className:"text-sm text-red-500",children:"Lỗi tải dữ liệu biểu đồ"})}):i.length===0||r.length===0?e.jsx("div",{className:"flex h-[300px] items-center justify-center",children:e.jsx("span",{className:"text-muted-foreground text-sm",children:"Không có dữ liệu biểu đồ"})}):e.jsx("div",{className:"h-[300px]",children:e.jsx(ve,{width:"100%",height:"100%",children:e.jsxs(He,{data:i,children:[e.jsx(De,{strokeDasharray:"3 3"}),e.jsx(me,{dataKey:"date",tickFormatter:n=>{if(s==="monthly"){const[x,f]=n.split("-");return`${f}/${x.slice(-2)}`}else return n},fontSize:12,tickLine:!1,axisLine:!1,interval:0,angle:-35,textAnchor:"end",height:60}),e.jsx(ue,{fontSize:12,tickLine:!1,axisLine:!1,tickCount:6,domain:["dataMin","dataMax"],tickFormatter:n=>n>=1e6?`${Math.round(n/1e6)}tr`:n>=1e3?`${Math.round(n/1e3)}k`:n===0?"0":Math.round(n).toString()}),e.jsx(Ne,{formatter:(n,x)=>[typeof n=="number"?n.toLocaleString("vi-VN")+" ₫":n,x],labelFormatter:n=>{if(s==="monthly"){const[x,f]=n.split("-");return`Tháng ${f}/${x}`}else return`Ngày ${n}`}}),e.jsx(Se,{wrapperStyle:{fontSize:"12px"}}),r.slice(0,5).map((n,x)=>e.jsx(qe,{type:"monotone",dataKey:n.storeName,name:n.storeName,stroke:o[x%o.length],strokeWidth:2,dot:!1,activeDot:{r:4}},n.storeId))]})})})}function Ht(){const{dateRange:t,filterType:s}=Pe();return e.jsxs("div",{className:"space-y-4 sm:space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:[e.jsxs(B,{className:"h-full",children:[e.jsxs(U,{className:"pb-3",children:[e.jsx(O,{className:"text-sm font-medium",children:"5 CH ÍT ĐƠN NHẤT"}),e.jsx(W,{className:"text-xs",children:"Cửa hàng có số đơn thấp nhất"})]}),e.jsx(K,{className:"pt-0",children:e.jsx(le,{type:"least-sales",dateRange:t,filterType:s})})]}),e.jsxs(B,{className:"h-full",children:[e.jsxs(U,{className:"pb-3",children:[e.jsx(O,{className:"text-sm font-medium",children:"5 CH NHIỀU ĐƠN NHẤT"}),e.jsx(W,{className:"text-xs",children:"Cửa hàng có số đơn cao nhất"})]}),e.jsx(K,{className:"pt-0",children:e.jsx(le,{type:"most-sales",dateRange:t,filterType:s})})]}),e.jsxs(B,{className:"h-full",children:[e.jsxs(U,{className:"pb-3",children:[e.jsx(O,{className:"text-sm font-medium",children:"5 CH DS THẤP NHẤT"}),e.jsx(W,{className:"text-xs",children:"Cửa hàng có doanh số thấp nhất"})]}),e.jsx(K,{className:"pt-0",children:e.jsx(le,{type:"least-revenue",dateRange:t,filterType:s})})]}),e.jsxs(B,{className:"h-full",children:[e.jsxs(U,{className:"pb-3",children:[e.jsx(O,{className:"text-sm font-medium",children:"5 CH DS CAO NHẤT"}),e.jsx(W,{className:"text-xs",children:"Cửa hàng có doanh số cao nhất"})]}),e.jsx(K,{className:"pt-0",children:e.jsx(le,{type:"most-revenue",dateRange:t,filterType:s})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 lg:grid-cols-4",children:[e.jsxs(B,{className:"h-full lg:col-span-2",children:[e.jsxs(U,{className:"pb-3",children:[e.jsx(O,{className:"text-sm font-medium",children:"TOP 5 CH DOANH SỐ BÁN CHẬM"}),e.jsx(W,{className:"text-xs",children:"Biểu đồ doanh số 5 cửa hàng bán chậm nhất"})]}),e.jsx(K,{className:"pt-0",children:e.jsx(Pt,{})})]}),e.jsxs(B,{className:"h-full",children:[e.jsxs(U,{className:"pb-3",children:[e.jsx(O,{className:"text-sm font-medium",children:"DS MẶT HÀNG BÁN CHẬM NHẤT"}),e.jsx(W,{className:"text-xs",children:"5 mặt hàng có số lượng bán thấp nhất"})]}),e.jsx(K,{className:"pt-0",children:e.jsx(Ft,{dateRange:t,filterType:s})})]}),e.jsxs(B,{className:"h-full",children:[e.jsxs(U,{className:"pb-3",children:[e.jsx(O,{className:"text-sm font-medium",children:"DS CH HUỶ ĐƠN NHIỀU NHẤT"}),e.jsx(W,{className:"text-xs",children:"Cửa hàng có số lượng đơn hàng bị huỷ cao nhất"})]}),e.jsx(K,{className:"pt-0",children:e.jsx(At,{dateRange:t})})]})]})]})}function qt(){const[t,s]=g.useState("monthly"),[r,m]=g.useState(Me()),[u,d]=g.useState(["all-stores"]),[i,o]=g.useState(["all-sources"]),{sources:n,chartData:x,isLoading:f,error:l}=Qe({dateRange:r,selectedStores:u,selectedSources:i,filterType:t,autoFetch:!0}),a=v=>{s(v),v==="monthly"?m(Me()):v==="daily"&&m(nt())},y=g.useMemo(()=>({dateRange:r,filterType:t,selectedStores:u,selectedSources:i}),[r,t,u,i]);return e.jsxs(st.Provider,{value:y,children:[e.jsx(vt,{dateRange:r,onDateRangeChange:m,selectedStores:u,onStoreChange:d,selectedSources:i,onSourceChange:o,filterType:t,onFilterTypeChange:a}),e.jsx(Nt,{sources:{data:x,isLoading:f,error:l},filterType:t,className:"w-full"}),e.jsx(St,{sources:{data:n,isLoading:f,error:l},dateRange:{startDate:r.from.getTime(),endDate:r.to.getTime()},filterType:t,className:"w-full"}),e.jsx(Ht,{})]})}function Gt(){const{selectedBrand:t}=Ve(),s=t==null?void 0:t.name,r=s?`Báo Cáo Doanh Thu ${s}`:"Báo Cáo Doanh Thu";return e.jsxs(e.Fragment,{children:[e.jsxs(Ze,{children:[e.jsx(at,{links:Yt}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(lt,{}),e.jsx(ot,{}),e.jsx(rt,{})]})]}),e.jsxs(Re,{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between space-y-2",children:[e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:r}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{variant:"outline",children:"Xuất Excel"}),e.jsx(P,{children:"Tải Báo Cáo"})]})]}),e.jsxs(Xe,{orientation:"vertical",defaultValue:"overview",className:"space-y-4",children:[e.jsx("div",{className:"w-full overflow-x-auto pb-2",children:e.jsxs(Je,{children:[e.jsx(ne,{value:"overview",children:"Tổng Quan"}),e.jsx(ne,{value:"stores",children:"Theo Cửa Hàng"}),e.jsx(ne,{value:"products",children:"Theo Sản Phẩm"}),e.jsx(ne,{value:"time",children:"Theo Thời Gian"})]})}),e.jsx(ae,{value:"overview",className:"space-y-4",children:e.jsx(qt,{})}),e.jsx(ae,{value:"stores",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo cửa hàng"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})}),e.jsx(ae,{value:"products",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo sản phẩm"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})}),e.jsx(ae,{value:"time",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo thời gian"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})})]})]})]})}const Yt=[{title:"Tổng Quan",href:"/bao-cao/doanh-thu",isActive:!0,disabled:!1},{title:"Doanh Thu Net",href:"/bao-cao/doanh-thu/net",isActive:!1,disabled:!0},{title:"Theo Cửa Hàng",href:"/bao-cao/doanh-thu/cua-hang",isActive:!1,disabled:!0},{title:"Theo Khu Vực",href:"/bao-cao/doanh-thu/khu-vuc",isActive:!1,disabled:!0}],qs=Gt;export{qs as component};
