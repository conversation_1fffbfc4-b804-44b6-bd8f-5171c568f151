import{r as s,I as z,A,j as a,C as M,P as w,F as R,E as O,af as B,c as j}from"./index-DZ2N7iEN.js";import{u as H}from"./index-DIwSqRjm.js";import{C as K}from"./check-CP51yA4o.js";var E="Checkbox",[L,U]=M(E),[q,F]=L(E),I=s.forwardRef((e,i)=>{const{__scopeCheckbox:t,name:u,checked:p,defaultChecked:o,required:h,disabled:d,value:k="on",onCheckedChange:C,form:l,...m}=e,[n,x]=s.useState(null),v=z(i,r=>x(r)),y=s.useRef(!1),P=n?l||!!n.closest("form"):!0,[f=!1,g]=A({prop:p,defaultProp:o,onChange:C}),D=s.useRef(f);return s.useEffect(()=>{const r=n==null?void 0:n.form;if(r){const b=()=>g(D.current);return r.addEventListener("reset",b),()=>r.removeEventListener("reset",b)}},[n,g]),a.jsxs(q,{scope:t,state:f,disabled:d,children:[a.jsx(w.button,{type:"button",role:"checkbox","aria-checked":c(f)?"mixed":f,"aria-required":h,"data-state":_(f),"data-disabled":d?"":void 0,disabled:d,value:k,...m,ref:v,onKeyDown:R(e.onKeyDown,r=>{r.key==="Enter"&&r.preventDefault()}),onClick:R(e.onClick,r=>{g(b=>c(b)?!0:!b),P&&(y.current=r.isPropagationStopped(),y.current||r.stopPropagation())})}),P&&a.jsx(T,{control:n,bubbles:!y.current,name:u,value:k,checked:f,required:h,disabled:d,form:l,style:{transform:"translateX(-100%)"},defaultChecked:c(o)?!1:o})]})});I.displayName=E;var S="CheckboxIndicator",N=s.forwardRef((e,i)=>{const{__scopeCheckbox:t,forceMount:u,...p}=e,o=F(S,t);return a.jsx(O,{present:u||c(o.state)||o.state===!0,children:a.jsx(w.span,{"data-state":_(o.state),"data-disabled":o.disabled?"":void 0,...p,ref:i,style:{pointerEvents:"none",...e.style}})})});N.displayName=S;var T=e=>{const{control:i,checked:t,bubbles:u=!0,defaultChecked:p,...o}=e,h=s.useRef(null),d=H(t),k=B(i);s.useEffect(()=>{const l=h.current,m=window.HTMLInputElement.prototype,x=Object.getOwnPropertyDescriptor(m,"checked").set;if(d!==t&&x){const v=new Event("click",{bubbles:u});l.indeterminate=c(t),x.call(l,c(t)?!1:t),l.dispatchEvent(v)}},[d,t,u]);const C=s.useRef(c(t)?!1:t);return a.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:p??C.current,...o,tabIndex:-1,ref:h,style:{...e.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function c(e){return e==="indeterminate"}function _(e){return c(e)?"indeterminate":e?"checked":"unchecked"}var X=I,$=N;function V({className:e,classNameIcon:i,...t}){return a.jsx(X,{"data-slot":"checkbox",className:j("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:a.jsx($,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current",children:a.jsx(K,{className:j("size-3.5",i)})})})}export{V as C};
