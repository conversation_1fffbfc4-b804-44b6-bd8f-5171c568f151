import{j as t,B as m,r as c,L as p}from"./index-D0Grd55b.js";import{H as l}from"./header-yApU5MZq.js";import{P as d}from"./profile-dropdown-dGP8eyih.js";import{S as h,T as x}from"./search-BEMocVbv.js";import"./date-range-picker-CruKYeHR.js";import"./form-Bk1C9kLO.js";import{D as g}from"./data-table-ByJYTqNe.js";import{B as u}from"./badge-DoEZpO1Y.js";import{S as j}from"./settings-FfwbJ8zD.js";import{u as f,n as v,A as y}from"./navigation-DNLzJqhm.js";import"./separator-CiUiq7rT.js";import"./avatar-C7dnn6zI.js";import"./dropdown-menu-BvqrmFsX.js";import"./index-CI2TkimM.js";import"./index-CW7Xpojs.js";import"./index-DxQNaO1C.js";import"./check-TFQPNqMS.js";import"./createLucideIcon-DNzDbUBG.js";import"./search-context-CjM0jrYw.js";import"./command-Bcq7GTcy.js";import"./calendar-5lpy20z0.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-C8IVKkOo.js";import"./search-C4dfJjaw.js";import"./createReactComponent-eJgt86Cn.js";import"./pos-api-B09qRspF.js";import"./scroll-area-r2ikcXUQ.js";import"./select-DBO-8fSu.js";import"./index-C-UyCxtf.js";import"./IconChevronRight-DnVUSvDn.js";import"./IconSearch-ClUZlEwa.js";import"./chevron-right-D2H_xC9V.js";import"./react-icons.esm-BOnXQgjl.js";import"./popover-CMTiAV3j.js";import"./table-pagination-CNd5jDfH.js";import"./pagination-ByVZh6mK.js";import"./table-CLNYB6yq.js";function T({users:i,isLoading:s,onEditUser:o,onToggleStatus:a}){const n=[{key:"username",header:"Tên người dùng",width:"200px"},{key:"email",header:"Email",width:"250px"},{key:"status",header:"Trạng thái",width:"120px",render:r=>t.jsx(u,{variant:r==="active"?"default":"secondary",children:r==="active"?"Hoạt động":"Không hoạt động"})},{key:"actions",header:"",width:"100px",render:(r,e)=>t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(m,{variant:"ghost",size:"icon",onClick:()=>o(e),className:"h-8 w-8",children:t.jsx(j,{className:"h-4 w-4"})}),t.jsx(m,{variant:"ghost",size:"icon",onClick:()=>a(e.id),className:"h-8 w-8",children:e.status==="active"?"Hủy":"Kích hoạt"})]})}];return s?t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{children:"Đang tải dữ liệu..."})}):t.jsx(g,{data:i,columns:n,isLoading:s,pageSize:20,emptyMessage:"Không có tài khoản nào",loadingMessage:"Đang tải..."})}const N=({error:i})=>t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{className:"text-red-600",children:i})})}),k=()=>t.jsx("div",{className:"mb-6",children:t.jsx(p,{to:y.CREATE,children:t.jsx(m,{children:"Tạo tài khoản"})})});function S(){const{users:i,isLoading:s,error:o,toggleUserStatus:a}=f(),n=c.useCallback(e=>{v(e.id)},[]),r=c.useCallback(async e=>{await a(e)},[a]);return o?t.jsx(N,{error:o}):t.jsxs(t.Fragment,{children:[t.jsxs(l,{children:[t.jsx(h,{}),t.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[t.jsx(x,{}),t.jsx(d,{})]})]}),t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx(k,{}),t.jsx(T,{users:i,isLoading:s,onEditUser:n,onToggleStatus:r})]})]})}const mt=S;export{mt as component};
