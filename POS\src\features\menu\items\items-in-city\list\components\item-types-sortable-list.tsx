import { useEffect, useMemo, useState } from 'react'

import { IconPinned, IconCaretUpDown } from '@tabler/icons-react'

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core'
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
  arrayMove
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'

type ItemTypeLite = { id: string; item_type_name: string; city_uid: string | null }

interface ItemTypesSortableListProps {
  itemTypes: Array<ItemTypeLite>
  selectedCityUid: string
  selectedItemTypeUid: string
  onSelect: (id: string) => void
  className?: string
  onOrderChange?: (ids: string[]) => void
  onOrderChangeDetailed?: (ordered: ItemTypeLite[]) => void
}

export function ItemTypesSortableList({
  itemTypes,
  selectedCityUid,
  selectedItemTypeUid,
  onSelect,
  className,
  onOrderChange,
  onOrderChangeDetailed
}: ItemTypesSortableListProps) {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { distance: 6 }
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  const [sortedItemTypes, setSortedItemTypes] = useState<ItemTypeLite[]>([])

  useEffect(() => {
    setSortedItemTypes([...itemTypes])
  }, [itemTypes])

  const ids = useMemo(() => sortedItemTypes.map(itemType => itemType.id), [sortedItemTypes])

  const handleItemTypesDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (!over || active.id === over.id) return

    setSortedItemTypes(items => {
      const activeIndex = items.findIndex(i => i.id === String(active.id))
      const overIndex = items.findIndex(i => i.id === String(over.id))
      if (activeIndex === -1 || overIndex === -1) return items

      const isEligible = (idx: number) => items[idx]?.city_uid === selectedCityUid

      if (!isEligible(activeIndex)) return items

      const eligibleIndices: number[] = items.map((_, idx: number) => idx).filter((idx: number) => isEligible(idx))

      if (eligibleIndices.length <= 1) return items

      const oldEligiblePos = eligibleIndices.indexOf(activeIndex)
      if (oldEligiblePos === -1) return items

      let targetEligiblePos = eligibleIndices.findIndex(idx => idx >= overIndex)
      if (targetEligiblePos === -1) targetEligiblePos = eligibleIndices.length - 1

      if (oldEligiblePos === targetEligiblePos) return items

      const eligibleItems = eligibleIndices.map(idx => items[idx])
      const newEligibleItems = arrayMove(eligibleItems, oldEligiblePos, targetEligiblePos)

      const next = items.slice()
      let take = 0
      for (let i = 0; i < next.length; i++) {
        if (isEligible(i)) {
          next[i] = newEligibleItems[take++]
        }
      }

      if (onOrderChange) onOrderChange(next.map(i => i.id))
      if (onOrderChangeDetailed) onOrderChangeDetailed(next)
      return next
    })
  }

  return (
    <div className={className}>
      <ScrollArea className='h-[50vh] w-full'>
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleItemTypesDragEnd}>
          <SortableContext items={ids} strategy={verticalListSortingStrategy}>
            <div className='space-y-1 p-2'>
              {sortedItemTypes.map(itemType => (
                <SortableItemTypeButton
                  key={itemType.id}
                  itemType={itemType}
                  selectedCityUid={selectedCityUid}
                  isSelected={selectedItemTypeUid === itemType.id}
                  onSelect={() => onSelect(itemType.id)}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
        <ScrollBar orientation='vertical' />
      </ScrollArea>
    </div>
  )
}

function SortableItemTypeButton({
  itemType,
  selectedCityUid,
  isSelected,
  onSelect
}: {
  itemType: ItemTypeLite
  selectedCityUid: string
  isSelected: boolean
  onSelect: () => void
}) {
  const isDraggable = itemType.city_uid === selectedCityUid
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: itemType.id })

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.6 : 1,
    zIndex: isDragging ? 1000 : 1,
    cursor: isDraggable ? 'move' : 'default'
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...(isDraggable ? attributes : {})}
      {...(isDraggable ? listeners : {})}
      className={`
        flex items-center justify-between rounded-md border px-3 py-2 text-sm transition-colors
        ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 bg-white hover:bg-gray-50'}
        ${isDraggable ? 'cursor-move' : 'cursor-pointer'}
      `}
      onClick={onSelect}
    >
      <span className='flex-1 truncate'>{itemType.item_type_name}</span>
      <div className='flex items-center gap-1'>
        {itemType.city_uid !== selectedCityUid && (
          <IconPinned className='h-3 w-3 text-gray-400' title='Nhóm món tại thành phố khác' />
        )}
        {isDraggable && <IconCaretUpDown className='h-3 w-3 text-gray-400' />}
      </div>
    </div>
  )
}
