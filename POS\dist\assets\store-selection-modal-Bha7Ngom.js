import{r as t,j as e,B as c}from"./index-D0Grd55b.js";import{C as f}from"./checkbox-C_eqgJmW.js";import{C as j,a as g,b as C}from"./collapsible-MezBM5sx.js";import{D as _,a as L,e as R}from"./dialog-C8IVKkOo.js";import{I as T}from"./input-C-0UnKOB.js";import{C as N}from"./chevron-right-D2H_xC9V.js";import{C as w}from"./select-DBO-8fSu.js";function K({open:v,onOpenChange:i,selectedStoreIds:a,onStoreSelectionChange:y}){const[n,S]=t.useState(""),[x,k]=t.useState(!1),[p,b]=t.useState(!1),o=t.useMemo(()=>{try{const s=localStorage.getItem("pos_stores_data");if(s){const r=JSON.parse(s);return Array.isArray(r)?r.filter(d=>d.active===1):[]}return[]}catch{return[]}},[]),l=t.useMemo(()=>n.trim()?o.filter(s=>s.store_name.toLowerCase().includes(n.toLowerCase())):o,[o,n]),m=t.useMemo(()=>l.filter(s=>a.includes(s.id)),[l,a]),h=t.useMemo(()=>l.filter(s=>!a.includes(s.id)),[l,a]),u=s=>{const r=a.includes(s)?a.filter(d=>d!==s):[...a,s];y(r)},D=()=>{i(!1)},M=()=>{i(!1)};return e.jsx(_,{open:v,onOpenChange:i,children:e.jsxs(L,{className:"max-w-md",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(T,{placeholder:"Tìm kiếm cửa hàng...",value:n,onChange:s=>S(s.target.value),className:"w-full"})}),e.jsxs(j,{open:!x,onOpenChange:k,children:[e.jsx(g,{asChild:!0,children:e.jsxs(c,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",m.length,")"]}),x?e.jsx(N,{className:"h-4 w-4"}):e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(C,{className:"space-y-2",children:[m.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(f,{checked:!0,onCheckedChange:()=>u(s.id)}),e.jsx("span",{className:"text-sm",children:s.store_name})]},s.id)),m.length===0&&e.jsx("p",{className:"text-muted-foreground p-2 text-sm",children:"Chưa chọn cửa hàng nào"})]})]}),e.jsxs(j,{open:!p,onOpenChange:b,children:[e.jsx(g,{asChild:!0,children:e.jsxs(c,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",h.length,")"]}),p?e.jsx(N,{className:"h-4 w-4"}):e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(C,{className:"space-y-2",children:[h.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(f,{checked:!1,onCheckedChange:()=>u(s.id)}),e.jsx("span",{className:"text-sm",children:s.store_name})]},s.id)),h.length===0&&e.jsx("p",{className:"text-muted-foreground p-2 text-sm",children:"Không có cửa hàng nào"})]})]})]}),e.jsxs(R,{className:"gap-2",children:[e.jsx(c,{variant:"outline",onClick:M,children:"Hủy"}),e.jsx(c,{onClick:D,children:"Lưu"})]})]})})}export{K as S};
