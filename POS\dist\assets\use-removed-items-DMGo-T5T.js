import{u as g}from"./useQuery-CCx3k6lE.js";import{a3 as f}from"./index-DZ2N7iEN.js";import{u as m}from"./useMutation-zADCXE7W.js";import{a as I}from"./vietqr-api-y35earyI.js";import{a as u}from"./pos-api-PZMeNc3U.js";import{Q as p}from"./query-keys-3lmd-xp6.js";const _=()=>{try{const t=localStorage.getItem("pos_cities_data");if(t)return JSON.parse(t).map(r=>r.id)}catch{}return[]},h=async(t={})=>{var i;const e=localStorage.getItem("pos_user_data"),r=localStorage.getItem("pos_brands_data");let a="",o="";if(e)try{a=JSON.parse(e).company_uid||""}catch{}if(r)try{const c=JSON.parse(r);Array.isArray(c)&&c.length>0&&(o=c[0].id||"")}catch{}if(!a||!o)throw new Error("Company or brand UID not found in localStorage");const s=new URLSearchParams({company_uid:a,brand_uid:o,page:(t.page||1).toString()});if(t.searchTerm&&s.append("search",t.searchTerm),t.listStoreUid&&t.listStoreUid.length>0)s.append("list_store_uid",t.listStoreUid.join(","));else if(t.listCityUid&&t.listCityUid.length>0){const c=_();t.listCityUid.length===c.length&&t.listCityUid.every(S=>c.includes(S))?s.append("list_city_uid",c.join(",")):s.append("list_city_uid",t.listCityUid.join(","))}else{const c=_();if(c.length>0)s.append("list_city_uid",c.join(","));else throw new Error("No cities or stores available for filtering")}const n=`/mdata/v1/item-removed?${s.toString()}`,d=await u.get(n);return(i=d.data)!=null&&i.data?d.data.data.map(y=>I(y)):[]},D=async t=>{const e=localStorage.getItem("pos_user_data"),r=localStorage.getItem("pos_brands_data");let a="",o="";if(e)try{a=JSON.parse(e).company_uid||""}catch{}if(r)try{const n=JSON.parse(r);Array.isArray(n)&&n.length>0&&(o=n[0].id||"")}catch{}if(!a||!o)throw new Error("Company or brand UID not found in localStorage");const s={company_uid:a,brand_uid:o,item_id:t};await u.post("/mdata/v1/item-restore",s)},b=async t=>{const e=localStorage.getItem("pos_user_data"),r=localStorage.getItem("pos_brands_data");let a="",o="";if(e)try{a=JSON.parse(e).company_uid||""}catch{}if(r)try{const d=JSON.parse(r);Array.isArray(d)&&d.length>0&&(o=d[0].id||"")}catch{}if(!a||!o)throw new Error("Company or brand UID not found in localStorage");if(!t||t.length===0)throw new Error("No items selected for restore");const s=t.join(","),n=`/mdata/v1/item-removed?company_uid=${a}&brand_uid=${o}&apply_with_store=true&list_item_uid=${s}`;await u.patch(n)},v=async t=>{const e=localStorage.getItem("pos_user_data"),r=localStorage.getItem("pos_brands_data");let a="",o="";if(e)try{a=JSON.parse(e).company_uid||""}catch{}if(r)try{const i=JSON.parse(r);Array.isArray(i)&&i.length>0&&(o=i[0].id||"")}catch{}if(!a||!o)throw new Error("Company or brand UID not found in localStorage");if(!t||t.length===0)throw new Error("City UIDs are required for export");const s=t.join(","),n=`/mdata/v1/item-removed?company_uid=${a}&brand_uid=${o}&list_city_uid=${s}&results_per_page=15000`;return(await u.get(n,{responseType:"blob"})).data},U=async t=>{const e=localStorage.getItem("pos_user_data"),r=localStorage.getItem("pos_brands_data");let a="",o="";if(e)try{a=JSON.parse(e).company_uid||""}catch{}if(r)try{const i=JSON.parse(r);Array.isArray(i)&&i.length>0&&(o=i[0].id||"")}catch{}if(!a||!o)throw new Error("Company or brand UID not found in localStorage");if(!t||t.length===0)throw new Error("Store UIDs are required for export");const s=t.join(","),n=`/mdata/v1/item-removed?company_uid=${a}&brand_uid=${o}&list_store_uid=${s}&results_per_page=15000`;return(await u.get(n,{responseType:"blob"})).data},R=()=>{const t=localStorage.getItem("pos_cities_data");if(t)try{const e=JSON.parse(t);if(Array.isArray(e))return e}catch{}return[]},l={getRemovedItems:h,restoreRemovedItem:D,bulkRestoreRemovedItems:b,exportRemovedItemsReport:v,exportRemovedItemsReportByStores:U,getCitiesFromLocalStorage:R};function T(t={}){const{enabled:e=!0,...r}=t;return g({queryKey:[p.REMOVED_ITEMS,r],queryFn:()=>l.getRemovedItems(r),enabled:e,staleTime:5*60*1e3,gcTime:10*60*1e3})}function J(){const t=f();return m({mutationFn:e=>l.restoreRemovedItem(e),onSuccess:()=>{t.invalidateQueries({queryKey:[p.REMOVED_ITEMS]})}})}function q(){const t=f();return m({mutationFn:e=>l.bulkRestoreRemovedItems(e),onSuccess:()=>{t.invalidateQueries({queryKey:[p.REMOVED_ITEMS]})}})}function $(){return m({mutationFn:t=>l.exportRemovedItemsReport(t)})}function x(){return m({mutationFn:t=>l.exportRemovedItemsReportByStores(t)})}function B(){return g({queryKey:[p.CITIES_LOCAL],queryFn:()=>l.getCitiesFromLocalStorage(),staleTime:30*60*1e3,gcTime:60*60*1e3})}export{J as a,q as b,x as c,$ as d,T as e,l as r,B as u};
