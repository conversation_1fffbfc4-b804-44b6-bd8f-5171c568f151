import{j as e,c as x,r,L as d,h as f,i as g,B as l}from"./index-DZ2N7iEN.js";import{u as j,S as v,U as N}from"./index-_PrUA_f4.js";import{H as k}from"./header-DUOMiURq.js";import{M as w}from"./main-DEy6aM-s.js";import{L as p}from"./learn-more-wosAB8UN.js";import{S as U,T as C}from"./search-BE7sNBUd.js";import{u as y,a as S,U as b,b as L,c as I,d as R,e as A}from"./users-B5Hcoazk.js";import{I as M}from"./IconLoader2-CyNHcnK_.js";import{c as B}from"./createReactComponent-CQMxkbLi.js";import"./index-BZV3Nu1l.js";import"./separator-Bnr4MN7f.js";import"./date-range-picker-extpnOqj.js";import"./calendar-CoHe9sRq.js";import"./createLucideIcon-2r9cCEY3.js";import"./index-CC24pdSB.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-D-SI6PAy.js";import"./react-icons.esm-Ck8h5xF8.js";import"./popover-BK8t3srL.js";import"./select-B60YVMMU.js";import"./index-Csh0LmDQ.js";import"./index-DIwSqRjm.js";import"./check-CP51yA4o.js";import"./dropdown-menu-Ci4yPenc.js";import"./index-DeeqTHK3.js";import"./search-context-bUKMT4ET.js";import"./command-By9h7q-C.js";import"./dialog-DDb7K8l1.js";import"./search-Cr9zvKK2.js";import"./pos-api-PZMeNc3U.js";import"./scroll-area-CoEnZUVR.js";import"./IconChevronRight-Cvwf7k8w.js";import"./form-CUvXDg29.js";import"./IconSearch-Do03nWQy.js";import"./badge-5IdLxoVq.js";import"./checkbox-Bqls14Dj.js";import"./IconEdit-CyinnQPC.js";import"./IconTrash-C4E5Mz81.js";import"./zod-Dyorah_P.js";import"./input-DpObCffE.js";import"./password-input-DK0Gb47B.js";import"./alert-OywbHCtE.js";import"./confirm-dialog-DAqmAcjM.js";import"./alert-dialog-3tViT-1a.js";import"./textarea-DOc8ucX0.js";import"./use-dialog-state-CIsfF4Gm.js";import"./index-AHlYCYkx.js";import"./table-DQgmazAN.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var O=B("outline","arrow-up-right","IconArrowUpRight",[["path",{d:"M17 7l-10 10",key:"svg-0"}],["path",{d:"M8 7l9 0l0 9",key:"svg-1"}]]);function T({className:s,...i}){return e.jsxs("svg",{role:"img",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",id:"clerk",height:"24",width:"24",className:x("[&>path]:fill-foreground",s),...i,children:[e.jsx("title",{children:"Clerk"}),e.jsx("path",{d:"m21.47 20.829 -2.881 -2.881a0.572 0.572 0 0 0 -0.7 -0.084 6.854 6.854 0 0 1 -7.081 0 0.576 0.576 0 0 0 -0.7 0.084l-2.881 2.881a0.576 0.576 0 0 0 -0.103 0.69 0.57 0.57 0 0 0 0.166 0.186 12 12 0 0 0 14.113 0 0.58 0.58 0 0 0 0.239 -0.423 0.576 0.576 0 0 0 -0.172 -0.453Zm0.002 -17.668 -2.88 2.88a0.569 0.569 0 0 1 -0.701 0.084A6.857 6.857 0 0 0 8.724 8.08a6.862 6.862 0 0 0 -1.222 3.692 6.86 6.86 0 0 0 0.978 3.764 0.573 0.573 0 0 1 -0.083 0.699l-2.881 2.88a0.567 0.567 0 0 1 -0.864 -0.063A11.993 11.993 0 0 1 6.771 2.7a11.99 11.99 0 0 1 14.637 -0.405 0.566 0.566 0 0 1 0.232 0.418 0.57 0.57 0 0 1 -0.168 0.448Zm-7.118 12.261a3.427 3.427 0 1 0 0 -6.854 3.427 3.427 0 0 0 0 6.854Z",strokeWidth:"1"})]})}const P=5;function z(){const s=f(),{history:i}=g(),[t,a]=r.useState(!0),[n,c]=r.useState(!1),[o,h]=r.useState(P);return r.useEffect(()=>{if(n||t)return;const u=setInterval(()=>{h(m=>m>0?m-1:0)},1e3);return()=>clearInterval(u)},[n,t]),r.useEffect(()=>{o>0||s({to:"/clerk/sign-in"})},[o,s]),e.jsx("div",{className:"h-svh",children:e.jsxs("div",{className:"m-auto flex h-full w-full flex-col items-center justify-center gap-2",children:[e.jsx("h1",{className:"text-[7rem] leading-tight font-bold",children:"401"}),e.jsx("span",{className:"font-medium",children:"Unauthorized Access"}),e.jsxs("p",{className:"text-muted-foreground text-center",children:["You must be authenticated via Clerk"," ",e.jsx("sup",{children:e.jsxs(p,{open:t,onOpenChange:a,children:[e.jsxs("p",{children:["This is the same as"," ",e.jsx(d,{to:"/users",className:"text-blue-500 underline decoration-dashed underline-offset-2",children:"'/users'"}),"."," "]}),e.jsx("p",{children:"You must first sign in using Clerk to access this route. "}),e.jsx("p",{className:"mt-4",children:"After signing in, you'll be able to sign out or delete your account via the User Profile dropdown on this page."})]})}),e.jsx("br",{}),"to access this resource."]}),e.jsxs("div",{className:"mt-6 flex gap-4",children:[e.jsx(l,{variant:"outline",onClick:()=>i.go(-1),children:"Go Back"}),e.jsxs(l,{onClick:()=>s({to:"/clerk/sign-in"}),children:[e.jsx(T,{className:"invert"})," Sign in"]})]}),e.jsx("div",{className:"mt-4 h-8 text-center",children:!n&&!t&&e.jsxs(e.Fragment,{children:[e.jsx("p",{children:o>0?`Redirecting to Sign In page in ${o}s`:"Redirecting..."}),e.jsx(l,{variant:"link",onClick:()=>c(!0),children:"Cancel Redirect"})]})})]})})}const Me=function(){const[i,t]=r.useState(!0),{isLoaded:a,isSignedIn:n}=j();if(!a)return e.jsx("div",{className:"flex h-svh items-center justify-center",children:e.jsx(M,{className:"size-8 animate-spin"})});if(!n)return e.jsx(z,{});const c=y.parse(S);return e.jsx(e.Fragment,{children:e.jsx(v,{children:e.jsxs(b,{children:[e.jsxs(k,{fixed:!0,children:[e.jsx(U,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(C,{}),e.jsx(N,{})]})]}),e.jsxs(w,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"User List"}),e.jsxs("div",{className:"flex gap-1",children:[e.jsx("p",{className:"text-muted-foreground",children:"Manage your users and their roles here."}),e.jsxs(p,{open:i,onOpenChange:t,contentProps:{side:"right"},children:[e.jsxs("p",{children:["This is the same as"," ",e.jsx(d,{to:"/users",className:"text-blue-500 underline decoration-dashed underline-offset-2",children:"'/users'"})]}),e.jsxs("p",{className:"mt-4",children:["You can sign out or manage/delete your account via the User Profile menu in the top-right corner of the page.",e.jsx(O,{className:"inline-block size-4"})]})]})]})]}),e.jsx(L,{})]}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:e.jsx(I,{data:c,columns:R})})]}),e.jsx(A,{})]})})})};export{Me as component};
