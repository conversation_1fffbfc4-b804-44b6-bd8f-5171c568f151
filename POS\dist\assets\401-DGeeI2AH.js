import{h as n,i as o,j as e,B as s}from"./index-DZ2N7iEN.js";function i(){const t=n(),{history:a}=o();return e.jsx("div",{className:"h-svh",children:e.jsxs("div",{className:"m-auto flex h-full w-full flex-col items-center justify-center gap-2",children:[e.jsx("h1",{className:"text-[7rem] leading-tight font-bold",children:"401"}),e.jsx("span",{className:"font-medium",children:"Unauthorized Access"}),e.jsxs("p",{className:"text-muted-foreground text-center",children:["Please log in with the appropriate credentials ",e.jsx("br",{})," to access this resource."]}),e.jsxs("div",{className:"mt-6 flex gap-4",children:[e.jsx(s,{variant:"outline",onClick:()=>a.go(-1),children:"Go Back"}),e.jsx(s,{onClick:()=>t({to:"/"}),children:"Back to Home"})]})]})})}const c=i;export{c as component};
