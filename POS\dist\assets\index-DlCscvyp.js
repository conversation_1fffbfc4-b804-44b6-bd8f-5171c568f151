import{aA as ye,e as fe,j as e,B as g,a as ve,c as L,T as je,o as H,p as Q,q as K,b as F,l as V,a3 as J,h as U,a4 as b,r as R}from"./index-DZ2N7iEN.js";import{d as Ne,c as De}from"./use-membership-discounts-C-BaI2Ux.js";import{D as _e}from"./discount-toggle-button-D9wjx4Xm.js";import{v as P}from"./date-range-picker-extpnOqj.js";import{L as m}from"./form-CUvXDg29.js";import{u as Te,a as Z,b as v,c as Ce,d as k,P as Pe}from"./discount-form-context-D7l-pxfi.js";import{X as Se,C as O}from"./calendar-CoHe9sRq.js";import{I as A}from"./input-DpObCffE.js";import{S as ee,a as te,b as se,c as ne,d as ae}from"./select-B60YVMMU.js";import{T as be,a as ke,c as Y}from"./tabs-BB7Je59V.js";import{C as z}from"./checkbox-Bqls14Dj.js";import{P as W,a as $,b as B}from"./popover-BK8t3srL.js";import{C as X}from"./calendar-3mjBqfJn.js";import{f as S}from"./isSameMonth-C8JQo-AN.js";import{C as G}from"./circle-help-CYiLfMW4.js";import"./pos-api-PZMeNc3U.js";import{j as h}from"./date-utils-DBbLjCz0.js";import{u as ie}from"./useQuery-CCx3k6lE.js";import{u as oe}from"./useMutation-zADCXE7W.js";import{Q as E}from"./query-keys-3lmd-xp6.js";function Ee(){const{handleBack:s,handleSave:i}=Te(),{isEditMode:t,isLoading:n,isFormValid:c}=Z(),{updateFormData:u}=v(),a=ye({strict:!1}),r=fe({strict:!1}),d=a.id,j=r==null?void 0:r.store_uid,{data:o}=Ne(d||"",j||""),p=De(),y=async()=>{if(!t||!o)return;const N=o.active===1?0:1,D={...o,active:N,is_update_same_discounts:!1};console.log("🔥 Membership Discount Toggle - API Data:",D);try{await p.mutateAsync(D),u({active:N})}catch(_){console.error("Failed to toggle discount status:",_)}};return e.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[e.jsx(g,{variant:"ghost",size:"sm",onClick:s,className:"flex items-center",children:e.jsx(Se,{className:"h-4 w-4"})}),e.jsxs("h1",{className:"text-3xl font-bold",children:[t&&"Chi tiết chiết khấu thanh toán",!t&&"Tạo chiết khấu thanh toán"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[t&&o&&e.jsx(_e,{isActive:o.active===1,onToggle:y,disabled:n||p.isPending}),e.jsxs(g,{type:"button",disabled:n||!c,className:"min-w-[100px]",onClick:i,children:[n&&t&&"Đang cập nhật...",n&&!t&&"Đang tạo...",!n&&t&&"Cập nhật",!n&&!t&&"Lưu"]})]})]})}function we({promotions:s,isLoading:i,disabled:t}){const{formData:n,updateFormData:c}=v(),u=a=>{if(a.startsWith("promotion-"))return;const r=s.find(d=>d.promotion_uid===a);console.log("🔥 Selected promotion:",r),c({promotionUid:a,promotionName:(r==null?void 0:r.promotion_name)||""})};return e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(m,{className:"min-w-[200px] text-sm font-medium",children:["CTKM ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(ee,{value:n.promotionUid,onValueChange:u,disabled:t||!n.storeUid||i,children:[e.jsx(te,{className:"flex-1",children:e.jsx(se,{placeholder:n.storeUid?i?"Đang tải...":s.length===0?"Không có CTKM":"Chọn CTKM":"Chọn cửa hàng trước"})}),e.jsx(ne,{children:s.length===0?e.jsx("div",{className:"px-2 py-1.5 text-sm text-gray-500",children:n.storeUid?"Không có CTKM":"Vui lòng chọn cửa hàng trước"}):s.filter(a=>a.promotion_uid).map(a=>e.jsx(ae,{value:a.promotion_uid,children:a.promotion_name||"Chương trình không tên"},a.promotion_uid))})]})]})}function Ae(){const{formData:s,updateFormData:i}=v(),{isEditMode:t}=Z(),{promotions:n,isLoadingPromotions:c}=Ce(),{currentBrandStores:u}=ve();return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin giảm giá"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(m,{className:"min-w-[200px] text-sm font-medium",children:["Áp dụng với hoá đơn từ ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(A,{type:"number",min:"0",value:s.fromAmount||"",onChange:a=>i({fromAmount:Number(a.target.value)}),placeholder:"0",className:"flex-1"}),e.jsxs(m,{className:"text-sm font-medium",children:["Đến ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(A,{type:"number",min:"0",value:s.toAmount||"",onChange:a=>i({toAmount:Number(a.target.value)}),placeholder:"0",className:"flex-1"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(m,{className:"min-w-[200px] text-sm font-medium",children:[s.discountType==="PERCENT"?"Phần trăm giảm giá":"Số tiền giảm giá"," ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"flex flex-1 gap-2",children:[e.jsx(A,{type:"number",min:"0",max:s.discountType==="PERCENT"?"100":void 0,value:s.discountValue||"",onChange:a=>{const r=Number(a.target.value);if(s.discountType==="PERCENT"){const d=r>100?100:r;i({discountValue:d})}else i({discountValue:r})},placeholder:"0",className:"flex-1"}),e.jsx(be,{value:s.discountType,onValueChange:a=>i({discountType:a}),className:"w-auto",children:e.jsxs(ke,{className:"grid w-fit grid-cols-2",children:[e.jsx(Y,{value:"PERCENT",children:"%"}),e.jsx(Y,{value:"AMOUNT",children:"đ"})]})})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(m,{className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(ee,{value:s.storeUid,onValueChange:a=>{i({storeUid:a,promotionUid:"",promotionName:""})},disabled:t,children:[e.jsx(te,{className:"flex-1",children:e.jsx(se,{placeholder:"Chọn cửa hàng"})}),e.jsx(ne,{children:u.map(a=>e.jsx(ae,{value:a.id,children:a.store_name},a.id))})]})]}),e.jsx(we,{promotions:n,isLoading:c,disabled:c}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(m,{className:"min-w-[200px] text-sm font-medium",children:"Là phiếu giảm giá (tính sau VAT)"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{id:"isDiscountVoucher",checked:s.isDiscountVoucher,onCheckedChange:a=>{i({isDiscountVoucher:a,requiresQuantityInput:a?s.requiresQuantityInput:!1})}}),e.jsx(m,{htmlFor:"isDiscountVoucher",className:"cursor-pointer text-sm font-normal",children:"Áp dụng phiếu giảm giá"})]})]}),s.isDiscountVoucher&&!t&&e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(m,{className:"min-w-[200px] text-sm font-medium",children:"Phiếu giảm giá yêu cầu nhập số lượng"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{id:"requiresQuantityInput",checked:s.requiresQuantityInput,onCheckedChange:a=>i({requiresQuantityInput:a})}),e.jsx(m,{htmlFor:"requiresQuantityInput",className:"cursor-pointer text-sm font-normal",children:"Yêu cầu nhập số lượng"})]})]})]})}function Fe(){const{formData:s,updateFormData:i}=v(),t=new Date;t.setHours(0,0,0,0);const n=s.startDate?new Date(s.startDate):void 0,c=s.endDate?new Date(s.endDate):void 0,u=r=>{if(r){const d=S(r,"yyyy-MM-dd");i({startDate:d})}},a=r=>{if(r){const d=S(r,"yyyy-MM-dd");i({endDate:d})}};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Ngày áp dụng"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(m,{className:"min-w-[120px] text-sm font-medium",children:"Ngày bắt đầu"}),e.jsxs(W,{children:[e.jsx($,{asChild:!0,children:e.jsxs(g,{variant:"outline",className:L("flex-1 justify-start text-left font-normal",!n&&"text-muted-foreground"),children:[e.jsx(X,{className:"mr-2 h-4 w-4"}),n?S(n,"dd/MM/yyyy",{locale:P}):"Chọn ngày bắt đầu"]})}),e.jsx(B,{className:"w-auto p-0",align:"start",children:e.jsx(O,{mode:"single",selected:n,onSelect:u,disabled:r=>r>t,initialFocus:!0,locale:P})})]})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(m,{className:"min-w-[120px] text-sm font-medium",children:"Ngày kết thúc"}),e.jsxs(W,{children:[e.jsx($,{asChild:!0,children:e.jsxs(g,{variant:"outline",className:L("flex-1 justify-start text-left font-normal",!c&&"text-muted-foreground"),children:[e.jsx(X,{className:"mr-2 h-4 w-4"}),c?S(c,"dd/MM/yyyy",{locale:P}):"Chọn ngày kết thúc"]})}),e.jsx(B,{className:"w-auto p-0",align:"start",children:e.jsx(O,{mode:"single",selected:c,onSelect:a,disabled:r=>r<t,initialFocus:!0,locale:P})})]})]})]})]})}const Ve=[{label:"T2",value:"0"},{label:"T3",value:"1"},{label:"T4",value:"2"},{label:"T5",value:"3"},{label:"T6",value:"4"},{label:"T7",value:"5"},{label:"CN",value:"6"}],Ue=[{value:"0"},{value:"1"},{value:"2"},{value:"3"},{value:"4"},{value:"5"},{value:"6"},{value:"7"},{value:"8"},{value:"9"},{value:"10"},{value:"11"},{value:"12"},{value:"13"},{value:"14"},{value:"15"},{value:"16"},{value:"17"},{value:"18"},{value:"19"},{value:"20"},{value:"21"},{value:"22"},{value:"23"}];function Me(){const{formData:s,updateFormData:i}=v(),t=c=>{const u=s.marketingDays||[],a=u.includes(c)?u.filter(r=>r!==c):[...u,c];i({marketingDays:a})},n=c=>{const u=s.marketingHours||[],a=u.includes(c)?u.filter(r=>r!==c):[...u,c];i({marketingHours:a})};return e.jsx(je,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Khung thời gian áp dụng"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(m,{className:"text-sm font-medium text-gray-500",children:"Chọn ngày"}),e.jsxs(H,{children:[e.jsx(Q,{asChild:!0,children:e.jsx(G,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(K,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần"})})]})]}),e.jsx("div",{className:"flex gap-2",children:Ve.map(c=>{var u;return e.jsx(g,{type:"button",variant:(u=s.marketingDays)!=null&&u.includes(c.value)?"default":"outline",size:"sm",onClick:()=>t(c.value),className:"flex-1",children:c.label},c.value)})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(m,{className:"text-sm font-medium text-gray-500",children:"Chọn giờ"}),e.jsxs(H,{children:[e.jsx(Q,{asChild:!0,children:e.jsx(G,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(K,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày"})})]})]}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:Ue.map(c=>{var u;return e.jsxs(g,{type:"button",variant:(u=s.marketingHours)!=null&&u.includes(c.value)?"default":"outline",size:"sm",onClick:()=>n(c.value),className:"text-xs",children:[c.value,":00"]},c.value)})})]})]})})}const Ie=s=>{const{company:i}=F(),{selectedBrand:t}=V();return ie({queryKey:[E.DISCOUNT_PAYMENT,"detail",s,i==null?void 0:i.id,t==null?void 0:t.id],queryFn:async()=>!s||!(i!=null&&i.id)||!(t!=null&&t.id)?null:k.getDiscountPaymentById({id:s,company_uid:i.id,brand_uid:t.id}),enabled:!!s&&!!(i!=null&&i.id)&&!!(t!=null&&t.id)})},qe=(s,i={})=>{const{company:t}=F(),{selectedBrand:n}=V(),{enabled:c=!0}=i;return ie({queryKey:[E.DISCOUNT_PAYMENT,"promotions",s,t==null?void 0:t.id,n==null?void 0:n.id],queryFn:()=>{if(!(t!=null&&t.id)||!(n!=null&&n.id)||!s)throw new Error("Thiếu thông tin cần thiết để lấy danh sách khuyến mãi");return k.getPaymentDiscountPromotions({companyUid:t.id,brandUid:n.id,storeUid:s})},enabled:c&&!!s&&!!(t!=null&&t.id)&&!!(n!=null&&n.id),staleTime:2*60*1e3,gcTime:5*60*1e3})},Le=(s={})=>{const i=J(),t=U();return oe({mutationFn:async n=>(console.log("Creating payment discount:",n),k.createPaymentDiscount(n)),onSuccess:()=>{i.invalidateQueries({queryKey:[E.DISCOUNT_PAYMENT]}),b.success("Đã tạo chiết khấu thanh toán thành công!"),s.onSuccess?s.onSuccess():t({to:"/sale/discount-payment"})},onError:n=>{console.error("Error creating payment discount:",n),b.error(n.message||"Lỗi khi tạo chiết khấu thanh toán")}})},He=(s={})=>{const i=J(),t=U();return oe({mutationFn:async n=>(console.log("Updating payment discount:",n),k.updateDiscountPayment(n)),onSuccess:()=>{i.invalidateQueries({queryKey:[E.DISCOUNT_PAYMENT]}),b.success("Đã cập nhật chiết khấu thanh toán thành công!"),s.onSuccess?s.onSuccess():t({to:"/sale/discount-payment"})},onError:n=>{console.error("Error updating payment discount:",n),b.error(n.message||"Lỗi khi cập nhật chiết khấu thanh toán")}})};function Qe({discountId:s,initialStoreUid:i,enablePromotionsFetch:t=!1}={}){const n=U(),{company:c}=F(),{selectedBrand:u}=V(),a=!!s;console.log("🔥 Payment Discount Edit Mode - API Call Params:",{discountId:s,initialStoreUid:i,isEditMode:a});const{data:r,isLoading:d}=Ie(s||""),j=(r==null?void 0:r.store_uid)||i||"",[o,p]=R.useState({storeUid:i||"",fromAmount:0,toAmount:0,isDiscountVoucher:!1,requiresQuantityInput:!1,discountType:"PERCENT",discountValue:0,startDate:new Date().toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],marketingDays:[],marketingHours:[],promotionUid:"",promotionName:"",active:1,filterState:{is_all:0,is_item:0,is_type:0,type_id:"",item_id:"",is_combo:0,combo_id:""}}),y=a?j:o.storeUid;console.log("🔥 Payment Discount Promotions Fetch:",{isEditMode:a,storeUidForPromotions:j,formDataStoreUid:o.storeUid,promotionsStoreUid:y,enablePromotionsFetch:t});const{data:N=[],isLoading:D}=qe(y,{enabled:t&&!!y}),_=l=>{const x=new Date(l);return new Date(x.getTime()+7*60*60*1e3).toISOString().split("T")[0]},T=l=>new Date(l+"T00:00:00+07:00"),re=l=>{var q;const x=_(l.from_date),f=_(l.to_date),C=h.convertNumbersToDayStrings(h.convertBitFlagsToDays(l.time_sale_date_week)),w=h.convertNumbersToHourStrings(h.convertBitFlagsToHours(l.time_sale_hour_day)),pe=l.type==="PERCENT"?(l.value||0)*100:l.value||0;return{storeUid:l.store_uid,fromAmount:l.from_amount||0,toAmount:l.to_amount||0,isDiscountVoucher:l.is_coupon===1,requiresQuantityInput:((q=l.extra_data)==null?void 0:q.voucher_input_number)===1,discountType:l.type,discountValue:pe,startDate:x,endDate:f,marketingDays:C,marketingHours:w,promotionUid:l.promotion_uid,promotionName:"",active:l.active,filterState:{is_all:0,is_item:0,is_type:0,type_id:"",item_id:"",is_combo:0,combo_id:""}}};R.useEffect(()=>{if(r&&a){console.log("🔥 Payment Discount Edit Mode - Raw API Data:",r);const l=re(r);console.log("🔥 Payment Discount Edit Mode - Transformed Form Data:",l),p(l)}},[r,a]);const{mutate:ce,isPending:le}=He({onSuccess:()=>{n({to:"/sale/discount-payment"})}}),{mutate:ue,isPending:me}=Le({onSuccess:()=>{n({to:"/sale/discount-payment"})}}),de=()=>{n({to:"/sale/discount-payment"})},he=()=>{const l=o.marketingDays.length>0?h.convertDayStringsToNumbers(o.marketingDays):[0,1,2,3,4,5,6],x=o.marketingHours.length>0?h.convertHourStringsToNumbers(o.marketingHours):h.getAllHours(),f=h.convertDaysToBitFlags(l),C=h.convertHoursToBitFlags(x);if(!a){const I=o.discountType==="PERCENT"?o.discountValue/100:o.discountValue;return{type:o.discountType,type_id:"",item_id:"",is_coupon:o.isDiscountVoucher?1:0,from_amount:o.fromAmount,to_amount:o.toAmount,promotion_uid:o.promotionUid,store_uid:o.storeUid,extra_data:{voucher_input_number:o.requiresQuantityInput?1:0},from_date:T(o.startDate).getTime(),to_date:T(o.endDate).getTime(),time_sale_date_week:f,time_sale_hour_day:C,company_uid:(c==null?void 0:c.id)||"",brand_uid:(u==null?void 0:u.id)||"",value:I,is_update_same_discount_payments:!1}}if(!r)return{};const w=o.discountType==="PERCENT"?o.discountValue/100:o.discountValue;return{...r,type:o.discountType,value:w,from_date:T(o.startDate).getTime(),to_date:T(o.endDate).getTime(),from_amount:o.fromAmount,to_amount:o.toAmount,time_sale_hour_day:C,time_sale_date_week:f,active:o.active,promotion_uid:o.promotionUid,store_uid:o.storeUid,is_coupon:o.isDiscountVoucher?1:0,extra_data:{voucher_input_number:o.requiresQuantityInput?1:0},is_update_same_discount_payments:!1}},xe=async()=>{if(!M)return;const l=he();console.log("🔥 Payment Discount Save - API Data:",l),a?ce(l):ue(l)},ge=l=>{p(x=>({...x,...l}))},M=o.storeUid!==""&&o.promotionUid!==""&&o.discountValue>0;return{formData:o,updateFormData:ge,handleBack:de,handleSave:xe,isFormValid:M,isLoading:me||le||d,isEditMode:a,promotions:t?N:[],isLoadingPromotions:t?D:!1}}function ct({discountId:s,storeUid:i}={}){const t=Qe({discountId:s,initialStoreUid:i,enablePromotionsFetch:!0});return e.jsx(Pe,{value:{...t,promotions:t.promotions||[],isLoadingPromotions:t.isLoadingPromotions||!1},children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Ee,{}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(Ae,{}),e.jsx(Fe,{}),e.jsx(Me,{})]})})})]})})}export{ct as P};
