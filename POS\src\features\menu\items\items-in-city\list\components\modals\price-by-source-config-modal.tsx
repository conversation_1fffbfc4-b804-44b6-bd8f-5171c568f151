import { useState } from 'react'

import { Download, Upload } from 'lucide-react'
import { toast } from 'sonner'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Button
} from '@/components/ui'

import { usePriceBySourceData } from '../../../hooks/use-price-by-source-data'
import { parsePriceBySourceExcel, validatePriceBySourceExcelFile } from '../../../utils/parse-price-by-source-excel'
import { generatePriceBySourceExcelFile } from '../../../utils/price-by-source-excel-utils'
import { PriceBySourcePreviewDialog } from './price-by-source-preview-dialog'
import type { PriceBySourcePreviewItem } from './price-by-source-preview-dialog'
import { CityData } from '@/types'

interface PriceBySourceConfigModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  cities: CityData[]
}

export function PriceBySourceConfigModal({ open, onOpenChange, cities }: PriceBySourceConfigModalProps) {
  const [selectedCityId, setSelectedCityId] = useState<string>('')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isDownloading, setIsDownloading] = useState(false)
  const [isProcessingFile, setIsProcessingFile] = useState(false)
  const [previewData, setPreviewData] = useState<PriceBySourcePreviewItem[]>([])
  const [showPreview, setShowPreview] = useState(false)

  const { items, sources, itemTypes, isLoading } = usePriceBySourceData(selectedCityId, !!selectedCityId)

  const handleDownloadTemplate = async () => {
    if (!selectedCityId) {
      toast.error('Vui lòng chọn thành phố trước')
      return
    }

    if (isLoading) {
      toast.error('Đang tải dữ liệu, vui lòng chờ...')
      return
    }

    if (!items.length) {
      toast.error('Không có món nào trong thành phố này')
      return
    }

    try {
      setIsDownloading(true)

      const selectedCity = cities.find(city => city.id === selectedCityId)
      const cityName = selectedCity?.city_name || 'Unknown'

      await generatePriceBySourceExcelFile(items, itemTypes, cityName)

      toast.success('Đã tải xuống file template thành công')
    } catch (error) {
      console.error('Error downloading template:', error)
      toast.error('Có lỗi xảy ra khi tải xuống file template')
    } finally {
      setIsDownloading(false)
    }
  }

  const handleUploadFile = () => {
    if (!selectedCityId) {
      toast.error('Vui lòng chọn thành phố trước')
      return
    }

    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.xlsx,.xls'
    input.onchange = async e => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      // Validate file
      const validation = validatePriceBySourceExcelFile(file)
      if (!validation.isValid) {
        toast.error(validation.error)
        return
      }

      try {
        setIsProcessingFile(true)
        setSelectedFile(file)

        // Parse Excel file
        const parsedData = await parsePriceBySourceExcel(file)

        if (parsedData.length === 0) {
          toast.error('File không có dữ liệu hợp lệ')
          return
        }

        setPreviewData(parsedData)
        setShowPreview(true)
        onOpenChange(false) // Đóng dialog chính khi mở preview

        toast.success(`Đã xử lý file thành công: ${parsedData.length} món`)
      } catch (error) {
        console.error('Error processing file:', error)
        toast.error('Có lỗi xảy ra khi xử lý file')
      } finally {
        setIsProcessingFile(false)
      }
    }
    input.click()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <DialogTitle className='text-xl font-semibold'>Thêm cấu hình giá món theo nguồn đơn</DialogTitle>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Bước 1: Chọn thành phố */}
          <div className='rounded-lg bg-gray-50 p-4'>
            <h3 className='mb-3 text-lg font-medium'>Bước 1. Chọn thành phố</h3>
            <Select value={selectedCityId} onValueChange={setSelectedCityId}>
              <SelectTrigger className='w-full'>
                <SelectValue placeholder='Chọn thành phố' />
              </SelectTrigger>
              <SelectContent>
                {cities.map(city => (
                  <SelectItem key={city.id} value={city.id}>
                    {city.city_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Bước 2: Tải file dữ liệu */}
          <div className='rounded-lg bg-gray-50 p-4'>
            <h3 className='mb-3 text-lg font-medium'>Bước 2. Tải file dữ liệu để lấy món và nguồn đã có</h3>
            <div className='flex items-center justify-between'>
              <div className='flex flex-col'>
                <span className='text-sm text-gray-600'>Tải xuống</span>
              </div>
              <Button
                variant='outline'
                onClick={handleDownloadTemplate}
                disabled={isDownloading || !selectedCityId || isLoading}
                className='flex items-center gap-2'
              >
                <Download className='h-4 w-4' />
                {isDownloading ? 'Đang tải...' : isLoading ? 'Đang tải dữ liệu...' : 'Tải xuống'}
              </Button>
            </div>
          </div>

          {/* Bước 3: Thêm cấu hình vào file */}
          <div className='rounded-lg bg-gray-50 p-4'>
            <h3 className='mb-3 text-lg font-medium'>Bước 3. Thêm cấu hình vào file</h3>
            <div className='space-y-3 text-sm text-gray-600'>
              <p>
                Nhập giá theo nguồn tương ứng với từng món vào cột <strong>price</strong>.
              </p>
              <p>Bỏ trống hoặc xoá dòng với những nguồn không có cấu hình giá.</p>
              <p>
                <strong className='text-red-600'>Không sửa các cột item_uid, item_name.</strong>
              </p>
            </div>
          </div>

          {/* Bước 4: Tải file lên */}
          <div className='rounded-lg bg-gray-50 p-4'>
            <h3 className='mb-3 text-lg font-medium'>Bước 4. Tải file lên</h3>
            <div className='flex items-center justify-between'>
              <div className='flex flex-col'>
                <span className='text-sm text-gray-600'>Sau khi đã điền đầy đủ bạn có thể tải file lên</span>
                {selectedFile && <span className='mt-1 text-xs text-gray-500'>File đã chọn: {selectedFile.name}</span>}
              </div>
              <Button
                onClick={handleUploadFile}
                disabled={!selectedCityId || isProcessingFile}
                className='flex items-center gap-2'
              >
                <Upload className='h-4 w-4' />
                {isProcessingFile ? 'Đang xử lý...' : 'Tải file lên'}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>

      {/* Preview Dialog */}
      <PriceBySourcePreviewDialog
        open={showPreview}
        onOpenChange={setShowPreview}
        data={previewData}
        originalItems={items}
        sources={sources}
        storeName={cities.find(city => city.id === selectedCityId)?.city_name}
      />
    </Dialog>
  )
}
