'use client'

import React, { useState, useMemo, useEffect } from 'react'

import { TrashIcon } from '@radix-ui/react-icons'

import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'
import { useItemTypesData, useItemClassesData, useUnitsData, useCitiesData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { useBulkCreateItemsInCity, type BulkCreateItemInCityRequest } from '../../../hooks'

type RowData = Record<string, string | number> & {
  _originalIndex: number
}

export interface ImportItemData {
  'Mã món': string
  'Tên': string
  'Thành phố': string
  'Giá': number
  'Mã barcode'?: string
  'Món ăn kèm'?: string
  'Nhóm'?: string
  'Loại món'?: string
  'Mô tả'?: string
  'SKU'?: string
  'Đơn vị'?: string
  'VAT (%)'?: number
  'Thời gian chế biến (phút)'?: number
  'Cho phép sửa giá khi bán'?: string
  'Cấu hình món ảo'?: string
  'Cấu hình món dịch vụ'?: string
  'Cấu hình món ăn là vé buffet'?: string
  'Ngày'?: number
  'Giờ'?: number
  'Thứ tự'?: number
  'Hình ảnh'?: string
  [key: string]: any
}

interface ExcelPreviewImportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: (string | number)[][]
  onSave?: (data: (string | number)[][]) => void // Make optional since we'll handle API internally
}

export function ExcelPreviewImportDialog({ open, onOpenChange, data, onSave }: ExcelPreviewImportDialogProps) {
  const [uploadedData, setUploadedData] = useState<(string | number)[][]>(data)
  const [importData, setImportData] = useState<ImportItemData[]>([])
  const [isProcessing, setIsProcessing] = useState(false)

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { bulkCreateItemsInCity, isBulkCreating } = useBulkCreateItemsInCity()

  // Get reference data for mapping
  const { data: itemTypes = [] } = useItemTypesData({
    skip_limit: true
  })
  const { data: itemClasses = [] } = useItemClassesData({
    skip_limit: true
  })
  const { data: units = [] } = useUnitsData()
  const { data: cities = [] } = useCitiesData()

  React.useEffect(() => {
    setUploadedData(data)
  }, [data])

  useEffect(() => {
    if (data && data.length > 0) {
      const headers = data[0] || []
      const rows = data.slice(1)

      const transformedData: ImportItemData[] = rows.map(row => {
        const item: any = {}
        headers.forEach((header, index) => {
          item[String(header)] = row[index] || ''
        })
        return item as ImportItemData
      })

      setImportData(transformedData)
    }
  }, [data])

  const parseBooleanStatus = (value: any): number => {
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase().trim()
      return lowerValue === 'có' || lowerValue === 'yes' || lowerValue === '1' || lowerValue === 'true' ? 1 : 0
    }
    return value ? 1 : 0
  }

  const handleSaveData = async () => {
    if (onSave) {
      onSave(uploadedData)
      toast.success('Data saved successfully')
      onOpenChange(false)
      return
    }

    if (!company?.id || !selectedBrand?.id) {
      toast.error('Thiếu thông tin công ty hoặc thương hiệu')
      return
    }

    if (importData.length === 0) {
      toast.error('Không có dữ liệu để import')
      return
    }

    setIsProcessing(true)

    try {
      const transformedData: BulkCreateItemInCityRequest[] = importData.map(item => {
        const city = cities.find(c => c.city_name === item['Thành phố'])
        if (!city) {
          throw new Error(`Không tìm thấy thành phố: ${item['Thành phố']}`)
        }

        const unit = units.find(u => u.unit_id === item['Đơn vị'])
        const defaultUnit = units.find(u => u.unit_id === 'MON')

        const itemType = itemTypes.find(
          it => it.item_type_id === item['Nhóm'] || it.item_type_name === item['Nhóm']
        )
        const defaultItemType = itemTypes.find(it => it.item_type_name === 'LOẠI KHÁC')

        const itemClass = itemClasses.find(
          ic => ic.item_class_id === item['Loại món'] || ic.item_class_name === item['Loại món']
        )

        return {
          company_uid: company.id,
          brand_uid: selectedBrand.id,
          city_uid: city.id,
          item_id: item['Mã món'],
          unit_uid: unit?.id || defaultUnit?.id || '',
          ots_price: Number(item['Giá']) || 0,
          ta_price: Number(item['Giá']) || 0,
          ots_tax: (Number(item['VAT (%)']) || 0) / 100,
          ta_tax: (Number(item['VAT (%)']) || 0) / 100,
          item_name: item['Tên'],
          item_id_barcode: item['Mã barcode'] || '',
          is_eat_with: parseBooleanStatus(item['Món ăn kèm']),
          item_type_uid: itemType?.id || defaultItemType?.id || '',
          item_class_uid: itemClass?.id || null,
          description: item['Mô tả'] || '',
          item_id_mapping: String(item['SKU'] || ''),
          time_cooking: (Number(item['Thời gian chế biến (phút)']) || 0) * 60000,
          time_sale_date_week: Number(item['Ngày']) || 0,
          time_sale_hour_day: Number(item['Giờ']) || 0,
          sort: Number(item['Thứ tự']) || 1,
          image_path_thumb: '',
          image_path: item['Hình ảnh'] || '',
          extra_data: {
            no_update_quantity_toping: parseBooleanStatus(item['Không cập nhật số lượng món ăn kèm']),
            enable_edit_price: parseBooleanStatus(item['Cho phép sửa giá khi bán']),
            is_virtual_item: parseBooleanStatus(item['Cấu hình món ảo']),
            is_item_service: parseBooleanStatus(item['Cấu hình món dịch vụ']),
            is_buffet_item: parseBooleanStatus(item['Cấu hình món ăn là vé buffet'])
          }
        }
      })

      await bulkCreateItemsInCity(transformedData, {
        onSuccess: () => {
          setIsProcessing(false)
          onOpenChange(false)
        },
        onError: error => {
          console.error('Error creating items:', error)
          setIsProcessing(false)
        }
      })
    } catch (error) {
      console.error('Error creating items:', error)
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra khi tạo món ăn')
    }
  }

  const handleClose = () => {
    onOpenChange(false)
  }

  const handleRemoveRow = React.useCallback(
    (originalIndex: number) => {
      const newData = uploadedData.filter((_, i) => i !== originalIndex)
      setUploadedData(newData)
    },
    [uploadedData]
  )

  const { tableData, columns } = useMemo(() => {
    if (!uploadedData || uploadedData.length === 0) {
      return { tableData: [], columns: [] }
    }

    const headers = uploadedData[0] || []
    const rows = uploadedData.slice(1)

    const cols: ColumnDef<RowData>[] = [
      {
        id: 'actions',
        header: '-',
        cell: ({ row }) => (
          <Button
            variant='ghost'
            size='sm'
            onClick={() => handleRemoveRow(row.original._originalIndex)}
            className='h-6 w-6 p-0 text-red-500 hover:text-red-700'
          >
            <TrashIcon className='h-4 w-4' />
          </Button>
        ),
        enableSorting: false,
        enableHiding: false,
        size: 50,
        meta: {
          className: 'w-12 text-center sticky left-0 bg-background z-20 border-r'
        }
      },
      ...headers.map((header, index) => ({
        id: `col_${index}`,
        accessorKey: `col_${index}`,
        header: String(header),
        cell: ({ row }: { row: { getValue: (key: string) => string | number } }) => (
          <div className='min-w-[150px] whitespace-nowrap'>{row.getValue(`col_${index}`)}</div>
        ),
        enableSorting: false,
        enableHiding: false,
        meta: {
          className: 'min-w-[150px] px-4 whitespace-nowrap'
        }
      }))
    ]

    // Create table data
    const tableRows: RowData[] = rows.map((row, rowIndex) => {
      const rowData: RowData = {
        _originalIndex: rowIndex + 1 // +1 because we skip header
      }
      row.forEach((cell, cellIndex) => {
        rowData[`col_${cellIndex}`] = cell
      })
      return rowData
    })

    return { tableData: tableRows, columns: cols }
  }, [uploadedData, handleRemoveRow])

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel()
  })

  if (!uploadedData || uploadedData.length === 0) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] max-w-7xl sm:max-w-4xl'>
        <DialogHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <DialogTitle className='text-xl font-semibold'>Thêm món từ file</DialogTitle>
        </DialogHeader>

        <div className='space-y-4 overflow-hidden'>
          <ScrollArea className='h-[60vh] w-full rounded-md border'>
            <Table>
              <TableHeader className='sticky top-0 z-10 bg-white'>
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <TableHead key={header.id} className={header.column.columnDef.meta?.className || ''}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map(row => (
                    <TableRow key={row.id} className='hover:bg-muted/50'>
                      {row.getVisibleCells().map(cell => (
                        <TableCell key={cell.id} className={cell.column.columnDef.meta?.className || ''}>
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className='h-24 text-center'>
                      Không có dữ liệu.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <ScrollBar orientation='horizontal' />
            <ScrollBar orientation='vertical' />
          </ScrollArea>

          <div className='flex items-center justify-between border-t pt-4'>
            <Button variant='outline' onClick={handleClose}>
              Đóng
            </Button>
            <Button onClick={handleSaveData} disabled={isProcessing || isBulkCreating} className='bg-green-600 hover:bg-green-700'>
              {isProcessing || isBulkCreating ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
