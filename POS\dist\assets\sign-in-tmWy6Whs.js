import{j as r}from"./index-D0Grd55b.js";import{C as e,a as t,b as o,c as i,d as s,f as a}from"./card-Z5f_tC2-.js";import{A as n}from"./auth-layout-BOztBjSE.js";import{U as m}from"./user-auth-form-DnBXOYRg.js";import"./form-Bk1C9kLO.js";import"./zod-G2vIgQkk.js";import"./use-auth-DtiSzKyd.js";import"./useMutation-ATsU-ht7.js";import"./utils-km2FGkQ4.js";import"./pos-api-B09qRspF.js";import"./input-C-0UnKOB.js";import"./password-input-DVF7dySI.js";import"./createReactComponent-eJgt86Cn.js";import"./IconBrandGithub-CCiLC8VW.js";function c(){return r.jsx(n,{children:r.jsxs(e,{className:"gap-4",children:[r.jsxs(t,{children:[r.jsx(o,{className:"text-lg tracking-tight",children:"Login"}),r.jsxs(i,{children:["Enter your email and password below to ",r.jsx("br",{}),"log into your account"]})]}),r.jsx(s,{children:r.jsx(m,{})}),r.jsx(a,{children:r.jsxs("p",{className:"text-muted-foreground px-8 text-center text-sm",children:["By clicking login, you agree to our"," ",r.jsx("a",{href:"/terms",className:"hover:text-primary underline underline-offset-4",children:"Terms of Service"})," ","and"," ",r.jsx("a",{href:"/privacy",className:"hover:text-primary underline underline-offset-4",children:"Privacy Policy"}),"."]})})]})})}const A=c;export{A as component};
