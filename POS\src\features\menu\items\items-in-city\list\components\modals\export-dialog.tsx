'use client'

import { useState, useRef } from 'react'

import { DownloadIcon, UploadIcon } from '@radix-ui/react-icons'

import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import { useItemTypesData, useCitiesData, useItemClassesData, useUnitsData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

import { Combobox } from '@/components/pos/combobox'

import { useFetchItemsData } from '../../../hooks'
import { generateItemsExcelFile } from '../../../utils'
import { ExcelPreviewExportDialog, type ExportItem } from './excel-preview-export-dialog'

interface ExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ExportDialog({ open, onOpenChange }: ExportDialogProps) {
  const [selectedCity, setSelectedCity] = useState<string>('all')
  const [selectedItemType, setSelectedItemType] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [uploadedData, setUploadedData] = useState<ExportItem[]>([])
  const [showPreviewDialog, setShowPreviewDialog] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { data: itemTypesData = [] } = useItemTypesData()
  const { data: itemClassesData = [] } = useItemClassesData()
  const { data: unitsData = [] } = useUnitsData()
  const { data: citiesData = [] } = useCitiesData()
  const { fetchItemsDataAsync, isPending } = useFetchItemsData()

  const itemTypeOptions = [
    { label: 'Tất cả nhóm món', value: 'all' },
    ...itemTypesData
      .filter(itemType => itemType.active === 1)
      .map(itemType => ({
        label: itemType.item_type_name,
        value: itemType.id
      }))
  ]

  const cityOptions = [
    { label: 'Tất cả thành phố', value: 'all' },
    ...citiesData
      .filter(city => city.active === 1)
      .map(city => ({
        label: city.city_name,
        value: city.id
      }))
  ]

  const statusOptions = [
    { label: 'Tất cả trạng thái', value: 'all' },
    { label: 'Active', value: '1' },
    { label: 'Deactive', value: '0' }
  ]

  const handleDownloadTemplate = async () => {
    try {
      // Fetch actual data based on filters
      const itemsData = await fetchItemsDataAsync({
        city_uid: selectedCity !== 'all' ? selectedCity : undefined,
        item_type_uid: selectedItemType !== 'all' ? selectedItemType : undefined,
        active: selectedStatus !== 'all' ? selectedStatus : undefined
      })

      // Generate Excel file with actual data and reference data (includes template worksheet)
      await generateItemsExcelFile(
        {
          itemTypes: itemTypesData,
          itemClasses: itemClassesData,
          units: unitsData
        },
        itemsData
      )
      toast.success('Tải file thành công!')
    } catch (_error) {
      toast.error('Lỗi khi tải file')
    }
  }

  // Mapping function to convert Vietnamese headers to English keys
  const getKeyFromHeader = (header: string): string => {
    const headerMap: Record<string, string> = {
      'ID': 'id',
      'Mã món': 'item_id',
      'Thành phố': 'city_name',
      'Tên': 'item_name',
      'Giá': 'ots_price',
      'Trạng thái': 'active',
      'Mã barcode': 'item_id_barcode',
      'Món ăn kèm': 'is_eat_with',
      'Không cập nhật số lượng món ăn kèm': 'no_update_quantity_toping',
      'Đơn vị': 'unit_name',
      'Nhóm': 'item_type_id',
      'Tên nhóm': 'item_type_name',
      'Loại món': 'item_class_id',
      'Tên loại': 'item_class_name',
      'Mô tả': 'description',
      'SKU': 'sku',
      'VAT (%)': 'ots_tax',
      'Thời gian chế biến (phút)': 'time_cooking',
      'Cho phép sửa giá khi bán': 'price_change',
      'Cấu hình món ảo': 'is_virtual_item',
      'Cấu hình món dịch vụ': 'is_item_service',
      'Cấu hình món ăn là vé buffet': 'is_buffet_item',
      'Giờ': 'time_sale_hour_day',
      'Ngày': 'time_sale_date_week',
      'Thứ tự': 'list_order',
      'Hình ảnh': 'image_path',
      'Công thức inQR cho máy pha trà': 'inqr_formula'
    }

    return headerMap[header] || header.toLowerCase().replace(/\s+/g, '_')
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = e => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        if (jsonData.length > 0) {
          // Convert raw Excel data to ExportItem format
          const rawData = jsonData as (string | number)[][]
          const headers = rawData[0] || []
          const rows = rawData.slice(1)

          const exportItems: ExportItem[] = rows.map((row, index) => {
            const item: any = { id: `temp_${index}` }
            headers.forEach((header, headerIndex) => {
              const key = getKeyFromHeader(String(header))
              const value = row[headerIndex]

              // Debug logging
              if (index === 0) {
                console.log(`Header: "${header}" -> Key: "${key}", Value: "${value}"`)
              }

              // Convert specific fields to proper types
              if (key === 'ots_price' || key === 'ots_tax' || key === 'time_cooking' ||
                  key === 'time_sale_hour_day' || key === 'time_sale_date_week' || key === 'list_order') {
                item[key] = Number(value) || 0
              } else if (key === 'active' || key === 'is_eat_with' || key === 'no_update_quantity_toping' ||
                        key === 'price_change' || key === 'is_virtual_item' || key === 'is_item_service' ||
                        key === 'is_buffet_item') {
                item[key] = Number(value) || 0
              } else {
                item[key] = value || ''
              }
            })

            // Debug logging for first item
            if (index === 0) {
              console.log('First item after processing:', item)
            }

            return item as ExportItem
          })

          setUploadedData(exportItems)
          setShowPreviewDialog(true)
          toast.success('File uploaded successfully')
        }
      } catch (_error) {
        toast.error('Error parsing file')
      }
    }
    reader.readAsArrayBuffer(file)
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }



  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-w-2xl lg:max-w-xl'>
          <DialogHeader>
            <div className='flex items-center justify-between'>
              <DialogTitle className='text-xl font-semibold'>Xuất, sửa thực đơn</DialogTitle>
            </div>
          </DialogHeader>

          <div className='space-y-6'>
            <div className='space-y-4'>
              <h3 className='text-lg font-medium'>Bước 1. Chỉnh bộ lọc để xuất file</h3>
              <div className='flex gap-4'>
                <Combobox
                  options={cityOptions}
                  value={selectedCity}
                  onValueChange={setSelectedCity}
                  placeholder='Tất cả thành phố'
                  searchPlaceholder='Tìm thành phố...'
                  className='flex-1'
                />

                <Combobox
                  options={itemTypeOptions}
                  value={selectedItemType}
                  onValueChange={setSelectedItemType}
                  placeholder='Tất cả nhóm món'
                  searchPlaceholder='Tìm nhóm món...'
                  className='flex-1'
                />

                <Combobox
                  options={statusOptions}
                  value={selectedStatus}
                  onValueChange={setSelectedStatus}
                  placeholder='Tất cả trạng thái'
                  searchPlaceholder='Tìm trạng thái...'
                  className='flex-1'
                />
              </div>
            </div>

            <div className='space-y-4'>
              <h3 className='text-lg font-medium'>Bước 2. Tải file dữ liệu</h3>
              <div className='flex items-center justify-between'>
                <span className='text-muted-foreground text-sm'>Tải xuống</span>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleDownloadTemplate}
                  disabled={isPending}
                  className='flex items-center gap-2'
                >
                  <DownloadIcon className='h-4 w-4' />
                  {isPending && 'Đang tải...'}
                </Button>
              </div>
            </div>

            <div className='space-y-4'>
              <h3 className='text-lg font-medium'>Bước 3. Thêm cấu hình vào file</h3>
              <div className='space-y-2'>
                <p className='text-muted-foreground text-sm'>Không sửa các cột :</p>
                <p className='font-mono text-sm text-blue-600'>ID, Mã món, Thành phố, Đơn vị, Tên nhóm, Tên loại.</p>
              </div>
            </div>

            <div className='space-y-4'>
              <h3 className='text-lg font-medium'>Bước 4. Tải file lên</h3>
              <div className='flex items-center justify-between'>
                <span className='text-muted-foreground text-sm'>Sau khi đã điền đầy đủ bạn có thể tải file lên</span>
                <Button variant='outline' size='sm' onClick={handleUploadClick} className='flex items-center gap-2'>
                  Tải file lên
                  <UploadIcon className='h-4 w-4' />
                </Button>
                <input
                  ref={fileInputRef}
                  type='file'
                  accept='.xlsx,.xls'
                  onChange={handleFileUpload}
                  style={{ display: 'none' }}
                />
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <ExcelPreviewExportDialog
        open={showPreviewDialog}
        onOpenChange={setShowPreviewDialog}
        data={uploadedData}
      />
    </>
  )
}
