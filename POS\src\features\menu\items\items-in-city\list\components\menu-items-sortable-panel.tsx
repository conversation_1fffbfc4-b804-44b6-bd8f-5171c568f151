import { DndContext, closestCenter, DragEndEvent } from '@dnd-kit/core'
import { SortableContext, rectSortingStrategy } from '@dnd-kit/sortable'

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'

import type { ItemsInCity } from '../../data'
import { SortableMenuItem } from './sortable-menu-item'

type ItemTypeLite = { id: string; item_type_name: string }

interface MenuItemsSortablePanelProps {
  selectedItemTypeUid: string
  itemTypes: Array<ItemTypeLite>
  sortedItems: ItemsInCity[]
  sensors: any
  onDragEnd: (event: DragEndEvent) => void
}

export function MenuItemsSortablePanel({
  selectedItemTypeUid,
  itemTypes,
  sortedItems,
  sensors,
  onDragEnd
}: MenuItemsSortablePanelProps) {
  const title = selectedItemTypeUid
    ? itemTypes.find(type => type.id === selectedItemTypeUid)?.item_type_name || 'Uncategory'
    : 'Uncategory'

  return (
    <div className='flex h-full flex-col rounded-lg border'>
      <div className='flex-shrink-0 border-b bg-gray-50 p-3'>
        <h3 className='text-sm font-medium'>{title}</h3>
      </div>
      <div className='min-h-0 flex-1'>
        <ScrollArea className='h-[50vh] w-full'>
          <div className='p-4'>
            {!selectedItemTypeUid ? (
              <div className='py-8 text-center text-gray-500'>
                <p>Vui lòng chọn nhóm món từ danh sách bên trái</p>
              </div>
            ) : sortedItems.length === 0 ? (
              <div className='py-8 text-center text-gray-500'>
                <p>Không có món ăn nào trong nhóm này</p>
              </div>
            ) : (
              <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={onDragEnd}>
                <SortableContext items={sortedItems.map(item => item.id as string)} strategy={rectSortingStrategy}>
                  <div className='grid grid-cols-4 gap-4'>
                    {sortedItems.map(item => (
                      <SortableMenuItem key={item.id} item={item} />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
            )}
          </div>
          <ScrollBar orientation='vertical' />
        </ScrollArea>
      </div>
    </div>
  )
}
