import{h as o,i,j as e,B as s}from"./index-DZ2N7iEN.js";function r(){const t=o(),{history:n}=i();return e.jsx("div",{className:"h-svh",children:e.jsxs("div",{className:"m-auto flex h-full w-full flex-col items-center justify-center gap-2",children:[e.jsx("h1",{className:"text-[7rem] leading-tight font-bold",children:"403"}),e.jsx("span",{className:"font-medium",children:"Access Forbidden"}),e.jsxs("p",{className:"text-muted-foreground text-center",children:["You don't have necessary permission ",e.jsx("br",{}),"to view this resource."]}),e.jsxs("div",{className:"mt-6 flex gap-4",children:[e.jsx(s,{variant:"outline",onClick:()=>n.go(-1),children:"Go Back"}),e.jsx(s,{onClick:()=>t({to:"/"}),children:"Back to Home"})]})]})})}const c=r;export{c as component};
