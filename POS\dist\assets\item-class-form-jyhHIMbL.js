import{r as m,R as $,j as e,B as o,h as A,a4 as L}from"./index-D0Grd55b.js";import{g as P}from"./error-utils-rXhAXMyo.js";import"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import{c as Q,a as U,d as z}from"./use-item-classes-s7fFgXey.js";import{u as H}from"./use-items-DCy2fVIC.js";import{u as K}from"./use-removed-items-7Ty-LinJ.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import"./date-range-picker-CruKYeHR.js";import{L as b}from"./form-Bk1C9kLO.js";import{I as k}from"./input-C-0UnKOB.js";import{C as D}from"./checkbox-C_eqgJmW.js";import{C as M,a as T,b as E}from"./collapsible-MezBM5sx.js";import{D as O,a as V,e as X}from"./dialog-C8IVKkOo.js";import{C as F}from"./chevron-right-D2H_xC9V.js";import{C as G}from"./select-DBO-8fSu.js";import{X as q}from"./calendar-5lpy20z0.js";function J({open:r,onOpenChange:d,items:l,selectedItems:h,onItemsSelected:N}){const[x,f]=m.useState(""),[n,I]=m.useState(!1),[p,w]=m.useState(!1),[s,i]=m.useState(h);console.log("ItemSelectionModal - selectedItems:",h),console.log("ItemSelectionModal - open:",r);const j=m.useMemo(()=>{if(!x.trim())return l;const t=x.toLowerCase();return l.filter(c=>c.item_name.toLowerCase().includes(t)||c.item_id.toLowerCase().includes(t))},[l,x]),u=j.filter(t=>s.includes(t.item_id)),g=j.filter(t=>!s.includes(t.item_id)),C=t=>{const c=s.includes(t)?s.filter(y=>y!==t):[...s,t];i(c)},_=()=>{N(s),d(!1)},v=()=>{i(h),d(!1)};return $.useEffect(()=>{r&&(i(h),f(""))},[r,h]),e.jsx(O,{open:r,onOpenChange:d,children:e.jsxs(V,{className:"max-w-md",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(k,{placeholder:"Tìm kiếm món",value:x,onChange:t=>f(t.target.value),className:"w-full"})}),e.jsxs(M,{open:!n,onOpenChange:I,children:[e.jsx(T,{asChild:!0,children:e.jsxs(o,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",u.length,")"]}),n?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})}),e.jsxs(E,{className:"space-y-2",children:[u.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(D,{checked:!0,onCheckedChange:()=>C(t.item_id)}),e.jsx("span",{className:"text-sm",children:t.item_name})]},t.id)),u.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn món nào"})]})]}),e.jsxs(M,{open:!p,onOpenChange:w,children:[e.jsx(T,{asChild:!0,children:e.jsxs(o,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",g.length,")"]}),p?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})}),e.jsxs(E,{className:"space-y-2",children:[g.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(D,{checked:!1,onCheckedChange:()=>C(t.item_id)}),e.jsx("span",{className:"text-sm",children:t.item_name})]},t.id)),g.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có món nào"})]})]})]}),e.jsxs(X,{children:[e.jsx(o,{variant:"outline",onClick:v,children:"Hủy"}),e.jsx(o,{onClick:_,children:"Lưu"})]})]})})}function pe({id:r}){const d=A(),l=!!r,{mutate:h,isPending:N}=Q(),{mutate:x,isPending:f}=U(),{data:n,isLoading:I}=z(r||"",l),{data:p=[]}=K(),{data:w=[]}=H({params:{skip_limit:!0,list_city_uid:p.map(a=>a.id).join(",")},enabled:p.length>0}),[s,i]=m.useState({name:"",code:"",autoGenerateCode:!0,selectedItems:[]}),[j,u]=m.useState(!1);m.useEffect(()=>{l&&n&&i({name:n.item_class_name,code:n.item_class_id,autoGenerateCode:!1,selectedItems:n.list_item||[]})},[l,n]);const g=()=>{d({to:"/menu/item-class"})},C=async()=>{if(v)if(l&&n){const a={...n,item_class_name:s.name,item_class_id:s.code,list_item:s.selectedItems};x(a,{onSuccess:()=>{d({to:"/menu/item-class"})}})}else{const a={item_class_name:s.name,item_class_id:s.autoGenerateCode?void 0:s.code,list_item:s.selectedItems};h(a,{onSuccess:()=>{d({to:"/menu/item-class"})}})}},_=async()=>{if(!(!l||!n))try{const a={...n,active:n.active===1?0:1};await x(a,{onSuccess:()=>{const S=a.active===1?"kích hoạt":"vô hiệu hóa";L.success(`Đã ${S} loại món "${n.item_class_name}"`)}})}catch(a){const S=P(a);L.error(S)}},v=s.name.trim()!=="",t=N||f||I,c=(n==null?void 0:n.active)===1,y=()=>{u(!0)},R=a=>{i({...s,selectedItems:a}),u(!1)},B=()=>s.selectedItems.length===0?"Chọn món áp dụng":`${s.selectedItems.length} món`;return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(o,{variant:"ghost",size:"sm",onClick:g,className:"flex items-center",children:e.jsx(q,{className:"h-4 w-4"})}),e.jsxs("div",{className:"flex items-center gap-2",children:[l&&n&&e.jsx(o,{type:"button",variant:c?"destructive":"default",disabled:t,className:"min-w-[100px]",onClick:_,children:c?"Deactivate":"Activate"}),(!l||c)&&e.jsx(o,{type:"button",disabled:t||!v,className:"min-w-[100px]",onClick:C,children:t?l?"Đang cập nhật...":"Đang tạo...":"Lưu"})]})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:l?"Chỉnh sửa loại món":"Tạo loại món"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin chi tiết"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(b,{htmlFor:"item-class-name",className:"min-w-[200px] text-sm font-medium",children:["Tên loại món ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(k,{id:"item-class-name",value:s.name,onChange:a=>i({...s,name:a.target.value}),placeholder:"Nhập tên loại món",className:"flex-1",disabled:l})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{htmlFor:"item-class-code",className:"min-w-[200px] text-sm font-medium",children:"Mã loại món"}),e.jsx(k,{id:"item-class-code",value:s.code,onChange:a=>i({...s,code:a.target.value}),placeholder:"Nếu để trống, hệ thống sẽ tự động tạo một mã loại món",disabled:s.autoGenerateCode||l,className:"flex-1"}),!l&&e.jsx(D,{id:"auto-generate-code",checked:s.autoGenerateCode,onCheckedChange:a=>i({...s,autoGenerateCode:a,code:a?"":s.code})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Thêm các món vào loại món"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Các món đang thuộc loại món khác sẽ được gán lại vào loại món này."})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"min-w-[200px] text-sm font-medium",children:"Áp dụng cho món"}),e.jsx("div",{className:"flex-1",children:e.jsx(o,{type:"button",variant:"outline",onClick:y,className:"w-full justify-start text-left",children:B()})})]})]})]})})}),e.jsx(J,{open:j,onOpenChange:u,items:w,selectedItems:s.selectedItems,onItemsSelected:R})]})}export{pe as C};
