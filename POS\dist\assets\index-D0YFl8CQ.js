import{r as _,u as le,a3 as Ge,a4 as F,h as Ae,j as e,B as I,c as Me,T as qe,o as $e,p as We,q as Qe,R as Oe,l as Xe}from"./index-DZ2N7iEN.js";import{b as Je}from"./pos-api-PZMeNc3U.js";import"./vietqr-api-y35earyI.js";import{u as Ye}from"./use-customizations-BihGvaSQ.js";import"./user-B3TbfMPQ.js";import"./crm-api-DyqsDFNF.js";import{H as Ze}from"./header-DUOMiURq.js";import{M as et}from"./main-DEy6aM-s.js";import{P as tt}from"./profile-dropdown-CYtncOP3.js";import{S as st,T as at}from"./search-BE7sNBUd.js";import{u as fe,a as it,b as nt,c as lt,g as Pe,d as rt,e as ct,f as ot,h as dt,I as mt,i as ht,j as ut,k as xt,C as pt,B as ft}from"./customization-dialog-Da7huSuz.js";import{E as gt}from"./exceljs.min-CLCFqwcY.js";import{h as _t,x as Ce,G as ke,H as yt,J as jt,K as wt,i as bt,A as Nt,a as vt,C as Ct,B as kt}from"./react-icons.esm-Ck8h5xF8.js";import{D as St,a as It,b as Tt,c as Q}from"./dropdown-menu-Ci4yPenc.js";import{D as G}from"./data-table-column-header-4ZB1Mdgz.js";import{B as Be}from"./badge-5IdLxoVq.js";import{S as Dt}from"./status-badge-BgxZUcyg.js";import"./date-range-picker-extpnOqj.js";import"./form-CUvXDg29.js";import{C as Re}from"./checkbox-Bqls14Dj.js";import{S as de}from"./settings-BWjFFru9.js";import{I as Ft}from"./IconCopy-C-PT5cPn.js";import{I as Et}from"./IconTrash-C4E5Mz81.js";import{u as ze,g as Mt,a as Ot,b as Pt,d as Bt,e as Ke,f as pe}from"./index-AHlYCYkx.js";import{S as ge,a as W}from"./scroll-area-CoEnZUVR.js";import{T as _e,a as ye,b as q,c as J,d as je,e as $}from"./table-DQgmazAN.js";import{C as Le}from"./confirm-dialog-DAqmAcjM.js";import{a as Rt,C as Vt}from"./chevron-right-D-SI6PAy.js";import{i as At,u as re}from"./use-item-types-BdROFFdK.js";import{u as Y}from"./use-removed-items-DMGo-T5T.js";import{I as zt}from"./input-DpObCffE.js";import{S as me,a as he,b as ue,c as xe,d as X}from"./select-B60YVMMU.js";import{M as Kt}from"./multi-select-B_hvbjyS.js";import{T as Se}from"./trash-2-BhgXHZOc.js";import{I as Lt}from"./IconFilter-D2eFCzWx.js";import{X as Ht}from"./calendar-CoHe9sRq.js";import{S as N}from"./skeleton-B7FDKZaJ.js";import{read as Ie,utils as Te}from"./xlsx-DkH2s96g.js";import{u as we}from"./use-item-classes-JUakwOO-.js";import{u as be}from"./use-units-CwHCNZjQ.js";import{D as Z,a as ee,b as te,c as se}from"./dialog-DDb7K8l1.js";import{C as ve}from"./combobox-B_tJR0V_.js";import{u as De}from"./useQuery-CCx3k6lE.js";import{u as Ut}from"./useMutation-zADCXE7W.js";import{i as Gt}from"./item-api-CZJRGV6g.js";import{s as qt}from"./sources-api-DJQwcNwK.js";import{Q as ne}from"./query-keys-3lmd-xp6.js";import{D as $t}from"./download-BL_XCcwW.js";import{U as Wt}from"./upload-BpTKdC5P.js";import"./separator-Bnr4MN7f.js";import"./avatar-C7msROEs.js";import"./search-context-bUKMT4ET.js";import"./command-By9h7q-C.js";import"./search-Cr9zvKK2.js";import"./createLucideIcon-2r9cCEY3.js";import"./createReactComponent-CQMxkbLi.js";import"./IconChevronRight-Cvwf7k8w.js";import"./IconSearch-Do03nWQy.js";import"./use-dialog-state-CIsfF4Gm.js";import"./modal-C3wc7cQn.js";import"./zod-Dyorah_P.js";import"./index-Csh0LmDQ.js";import"./index-CC24pdSB.js";import"./index-DeeqTHK3.js";import"./check-CP51yA4o.js";import"./popover-BK8t3srL.js";import"./isSameMonth-C8JQo-AN.js";import"./index-DIwSqRjm.js";import"./alert-dialog-3tViT-1a.js";import"./circle-x-DCSjVXFs.js";import"./chevrons-up-down-Cl92_NdW.js";import"./utils-km2FGkQ4.js";import"./sources-CfiQ7039.js";const Fe=[{sourceId:"10000045",name:"ZALO"},{sourceId:"10000049",name:"FACEBOOK"},{sourceId:"10000134",name:"SO"},{sourceId:"10000162",name:"CRM"},{sourceId:"10000165",name:"VNPAY"},{sourceId:"10000168",name:"GOJEK (GOVIET)"},{sourceId:"10000169",name:"ShopeeFood"},{sourceId:"10000171",name:"MANG VỀ"},{sourceId:"10000172",name:"TẠI CHỖ"},{sourceId:"10000176",name:"CALL CENTER"},{sourceId:"10000216",name:"O2O"},{sourceId:"10000253",name:"BEFOOD"}],Qt=()=>{const l=new gt.Workbook;return l.creator="POS System",l.lastModifiedBy="POS System",l.created=new Date,l.modified=new Date,l},Xt=()=>{const l=["item_uid","item_id","item_name","Nhóm món","Giá gốc","Vat (%)"],t=Fe.map(s=>`${s.name} [${s.sourceId}]`);return[...l,...t]},Jt=(l,t)=>{const i=l.map(o=>{const h=t.find(g=>g.id===o.item_type_uid),c=(h==null?void 0:h.item_type_name)||"Uncategory";return{item:o,itemTypeName:c}}).reduce((o,{item:h,itemTypeName:c})=>(o[c]||(o[c]=[]),o[c].push({item:h,itemTypeName:c}),o),{}),a=Object.keys(i).sort(),r=[];return a.forEach(o=>{const h=i[o];h.sort((c,g)=>c.item.item_name.localeCompare(g.item.item_name)),h.forEach(({item:c,itemTypeName:g})=>{const y=[c.id,c.item_id,c.item_name,g,c.ots_price||0,(c.ots_tax||0)*100];Fe.forEach(S=>{var p,w;const v=(w=(p=c.extra_data)==null?void 0:p.price_by_source)==null?void 0:w.find(x=>x.source_id===S.sourceId);y.push((v==null?void 0:v.price)||"")}),r.push(y)})}),r},Yt=(l,t,s)=>{const i=l.addWorksheet("Sheet"),a=Xt(),r=Jt(t,s);return i.addRow(a).eachCell(c=>{c.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},c.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},c.alignment={horizontal:"center",vertical:"middle"},c.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),r.forEach(c=>{i.addRow(c).eachCell(y=>{y.font={size:10},y.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),[40,15,25,15,12,10,...Fe.map(()=>15)].forEach((c,g)=>{i.getColumn(g+1).width=c}),i},Zt=async(l,t,s)=>{try{const i=Qt();Yt(i,l,t);const a=await i.xlsx.writeBuffer(),r=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),o=new Date().toISOString().slice(0,19).replace(/:/g,"-"),h=`import-price-by-source_${s}_${o}.xlsx`,c=window.URL.createObjectURL(r),g=document.createElement("a");return g.href=c,g.download=h,document.body.appendChild(g),g.click(),document.body.removeChild(g),window.URL.revokeObjectURL(c),Promise.resolve()}catch(i){return console.error("Error creating price by source Excel file:",i),Promise.reject(i)}},es=["item_uid","item_id","item_name","Nhóm món","Giá gốc","Vat (%)","ZALO [10000045]","FACEBOOK [10000049]","SO [10000134]","CRM [10000162]","VNPAY [10000165]","GOJEK (GOVIET) [10000168]","ShopeeFood [10000169]","MANG VỀ [10000171]","TẠI CHỖ [10000172]","CALL CENTER [10000176]","O2O [10000216]","BEFOOD [10000253]"],ts=async l=>new Promise((t,s)=>{const i=new FileReader;i.onload=a=>{var r;try{const o=(r=a.target)==null?void 0:r.result;if(!o){s(new Error("Không thể đọc file"));return}const h=Ie(o,{type:"array"}),c=h.SheetNames[0];if(!c){s(new Error("File Excel không có sheet nào"));return}const g=h.Sheets[c],y=Te.sheet_to_json(g,{header:1});if(y.length<2){s(new Error("File Excel không có dữ liệu"));return}const S=y[0],v=es.filter(w=>!S.includes(w));v.length>0&&console.warn("Missing headers:",v);const p=[];for(let w=1;w<y.length;w++){const x=y[w];if(!x||x.length===0)continue;const j={item_uid:"",item_id:"",item_name:"",item_type_name:"",ots_price:0,ots_tax:0};S.forEach((T,P)=>{const f=x[P];switch(T){case"item_uid":j.item_uid=String(f||"");break;case"item_id":j.item_id=String(f||"");break;case"item_name":j.item_name=String(f||"");break;case"Nhóm món":j.item_type_name=String(f||"");break;case"Giá gốc":j.ots_price=parseFloat(String(f||"0"))||0;break;case"Vat (%)":j.ots_tax=parseFloat(String(f||"0"))||0;break;case"ZALO [10000045]":case"FACEBOOK [10000049]":case"SO [10000134]":case"CRM [10000162]":case"VNPAY [10000165]":case"GOJEK (GOVIET) [10000168]":case"ShopeeFood [10000169]":case"MANG VỀ [10000171]":case"TẠI CHỖ [10000172]":case"CALL CENTER [10000176]":case"O2O [10000216]":case"BEFOOD [10000253]":if(f!=null&&f!==""){const n=parseFloat(String(f));isNaN(n)||(j[T]=n)}break;default:f!=null&&(j[T]=f);break}}),j.item_uid&&j.item_id&&j.item_name&&p.push(j)}console.log("📊 Parsed Excel data:",{totalRows:y.length-1,validItems:p.length,headers:S,sampleItem:p[0]}),t(p)}catch(o){console.error("Error parsing Excel file:",o),s(new Error("Có lỗi xảy ra khi đọc file Excel"))}},i.onerror=()=>{s(new Error("Có lỗi xảy ra khi đọc file"))},i.readAsArrayBuffer(l)}),ss=l=>{if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","application/octet-stream"].includes(l.type)&&!l.name.match(/\.(xlsx|xls)$/i))return{isValid:!1,error:"File phải có định dạng Excel (.xlsx hoặc .xls)"};const s=10*1024*1024;return l.size>s?{isValid:!1,error:"File không được vượt quá 10MB"}:{isValid:!0}};function as(){const[l,t]=_.useState(!1),[s,i]=_.useState(!1),[a,r]=_.useState(null),[o,h]=_.useState(!1),[c,g]=_.useState([]),[y,S]=_.useState("all"),[v,p]=_.useState("all"),[w,x]=_.useState([]),[j,T]=_.useState("all");return{isCustomizationDialogOpen:l,isBuffetItem:s,selectedMenuItem:a,isBuffetConfigModalOpen:o,selectedBuffetMenuItem:c,selectedItemTypeUid:y,selectedCityUid:v,selectedDaysOfWeek:w,selectedStatus:j,setIsCustomizationDialogOpen:t,setIsBuffetItem:i,setSelectedMenuItem:r,setIsBuffetConfigModalOpen:h,setSelectedBuffetMenuItem:g,setSelectedItemTypeUid:S,setSelectedCityUid:p,setSelectedDaysOfWeek:x,setSelectedStatus:T}}const is=(l,t=!0)=>{const{company:s,brands:i}=le(r=>r.auth),a=i==null?void 0:i[0];return De({queryKey:[ne.ITEMS_LIST,"price-by-source",l],queryFn:async()=>{const r={company_uid:(s==null?void 0:s.id)||"",brand_uid:(a==null?void 0:a.id)||"",city_uid:l,skip_limit:!0,active:1};return(await Gt.getItems(r)).data||[]},enabled:t&&!!(s!=null&&s.id&&(a!=null&&a.id)&&l),staleTime:5*60*1e3,gcTime:10*60*1e3})},ns=(l,t=!0)=>{const{company:s,brands:i}=le(r=>r.auth),a=i==null?void 0:i[0];return De({queryKey:[ne.SOURCES,"price-by-source",l],queryFn:async()=>{const r={company_uid:(s==null?void 0:s.id)||"",brand_uid:(a==null?void 0:a.id)||"",city_uid:l,skip_limit:!0};return await qt.getSources(r)||[]},enabled:t&&!!(s!=null&&s.id&&(a!=null&&a.id)&&l),staleTime:5*60*1e3,gcTime:10*60*1e3})},ls=(l,t=!0)=>{const{company:s,brands:i}=le(r=>r.auth),a=i==null?void 0:i[0];return De({queryKey:[ne.ITEM_TYPES,"price-by-source",l],queryFn:async()=>{const r={company_uid:(s==null?void 0:s.id)||"",brand_uid:(a==null?void 0:a.id)||"",city_uid:l,skip_limit:!0,active:1};return(await At.getItemTypes(r)).data||[]},enabled:t&&!!(s!=null&&s.id&&(a!=null&&a.id)&&l),staleTime:5*60*1e3,gcTime:10*60*1e3})},rs=(l,t=!0)=>{const s=is(l,t),i=ns(l,t),a=ls(l,t);return{items:s.data||[],sources:i.data||[],itemTypes:a.data||[],isLoading:s.isLoading||i.isLoading||a.isLoading,isError:s.isError||i.isError||a.isError,error:s.error||i.error||a.error,refetch:()=>{s.refetch(),i.refetch(),a.refetch()}}},cs=(l,t,s)=>l.map(i=>{const a=t.find(h=>h.id===i.item_uid);if(!a)throw new Error(`Original item not found for ${i.item_uid}`);const r=[],o={};return s.forEach(h=>{const c=`${h.sourceName} [${h.sourceId}]`;o[c]=h.sourceId}),Object.entries(o).forEach(([h,c])=>{const g=i[h];if(g!=null&&g!==""){const y=typeof g=="string"?parseFloat(g):g;!isNaN(y)&&y>0&&r.push({source_id:c,price:y})}}),{...a,ots_price:i.ots_price,ots_tax:i.ots_tax/100,ta_price:i.ots_price,ta_tax:i.ots_tax/100,extra_data:{...a.extra_data,price_by_source:r}}}),os=()=>{const l=Ge(),{mutate:t,isPending:s}=Ut({mutationFn:async i=>{const a=cs(i.previewItems,i.originalItems,i.sources),r=await Je.put("/mdata/v1/items",a);return r.data.data||r.data},onSuccess:()=>{l.invalidateQueries({queryKey:[ne.ITEMS_LIST],refetchType:"none"}),setTimeout(()=>{l.refetchQueries({queryKey:[ne.ITEMS_LIST]})},100),F.success("Đã cập nhật cấu hình giá theo nguồn thành công!")},onError:i=>{F.error(`Có lỗi xảy ra khi lưu cấu hình: ${i.message}`)}});return{bulkUpdatePriceBySource:t,isUpdating:s}};function ds(){const{setOpen:l}=fe(),t=Ae(),s=()=>{t({to:"/menu/items/items-in-city/detail"})},i=()=>{l("export-dialog")},a=()=>{l("import")},r=()=>{l("price-by-source-config")},o=()=>{},h=()=>{},c=()=>{};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(St,{children:[e.jsx(It,{asChild:!0,children:e.jsxs(I,{variant:"outline",size:"sm",children:["Tiện ích",e.jsx(_t,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(Tt,{align:"end",className:"w-56",children:[e.jsxs(Q,{onClick:i,children:[e.jsx(Ce,{className:"mr-2 h-4 w-4"}),"Xuất, sửa thực đơn"]}),e.jsxs(Q,{onClick:a,children:[e.jsx(ke,{className:"mr-2 h-4 w-4"}),"Thêm món từ file"]}),e.jsxs(Q,{onClick:r,children:[e.jsx(yt,{className:"mr-2 h-4 w-4"}),"Cấu hình giá theo nguồn"]}),e.jsxs(Q,{onClick:o,children:[e.jsx(jt,{className:"mr-2 h-4 w-4"}),"Sắp xếp thực đơn"]}),e.jsxs(Q,{onClick:h,children:[e.jsx(wt,{className:"mr-2 h-4 w-4"}),"Sao chép thực đơn"]}),e.jsxs(Q,{onClick:c,children:[e.jsx(bt,{className:"mr-2 h-4 w-4"}),"Cấu hình khung thời gian"]})]})]}),e.jsx(I,{variant:"default",size:"sm",onClick:s,children:"Tạo món"})]})})}function Ve({column:l,title:t,className:s,defaultSort:i="desc"}){if(!l.getCanSort())return e.jsx("div",{className:Me(s),children:t});const a=()=>{const r=l.getIsSorted();r?r==="desc"?l.toggleSorting(!1):l.toggleSorting(!0):l.toggleSorting(i==="desc")};return e.jsx("div",{className:Me("flex items-center space-x-2",s),children:e.jsxs(I,{variant:"ghost",size:"sm",className:"-ml-3 h-8 hover:bg-accent",onClick:a,children:[e.jsx("span",{children:t}),l.getIsSorted()==="desc"?e.jsx(Nt,{className:"ml-2 h-4 w-4"}):l.getIsSorted()==="asc"?e.jsx(vt,{className:"ml-2 h-4 w-4"}):e.jsx(Ct,{className:"ml-2 h-4 w-4"})]})})}const ms=({onBuffetConfigClick:l})=>[{id:"select",header:({table:t})=>e.jsx(Re,{checked:t.getIsAllPageRowsSelected(),onCheckedChange:s=>t.toggleAllPageRowsSelected(!!s),"aria-label":"Select all"}),cell:({row:t})=>e.jsx(Re,{checked:t.getIsSelected(),onCheckedChange:s=>t.toggleSelected(!!s),"aria-label":"Select row",onClick:s=>s.stopPropagation()}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:t})=>e.jsx("div",{className:"w-[50px]",children:t.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"code",header:({column:t})=>e.jsx(G,{column:t,title:"Mã món"}),cell:({row:t})=>e.jsx("div",{className:"text-sm font-medium",children:t.getValue("code")}),enableSorting:!1,enableHiding:!0},{accessorKey:"name",header:({column:t})=>e.jsx(G,{column:t,title:"Tên món"}),cell:({row:t})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm font-medium",children:t.getValue("name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"price",header:({column:t})=>e.jsx(G,{column:t,title:"Giá"}),cell:({row:t})=>{const s=t.getValue("price");return e.jsx("div",{className:"text-sm font-medium",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(s)})},enableSorting:!1,enableHiding:!0},{accessorKey:"vatPercent",header:({column:t})=>e.jsx(G,{column:t,title:"VAT (%)"}),cell:({row:t})=>{const s=t.getValue("vatPercent");return e.jsx("div",{className:"text-right text-sm",children:s*100})},enableSorting:!1,enableHiding:!0},{accessorKey:"itemType",header:({column:t})=>e.jsx(G,{column:t,title:"Nhóm món"}),cell:({row:t})=>e.jsx(Be,{variant:"outline",className:"text-xs",children:t.getValue("itemType")}),enableSorting:!1,enableHiding:!0},{accessorKey:"itemClass",header:({column:t})=>e.jsx(G,{column:t,title:"Loại món"}),cell:({row:t})=>t.getValue("itemClass")&&e.jsx(Be,{variant:"outline",className:"text-center text-xs",children:t.getValue("itemClass")}),enableSorting:!1,enableHiding:!0},{accessorKey:"unit",header:({column:t})=>e.jsx(G,{column:t,title:"Đơn vị tính"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("unit")}),enableSorting:!1,enableHiding:!0},{accessorKey:"sideItems",header:({column:t})=>e.jsx(Ve,{column:t,title:"Món ăn kèm",defaultSort:"desc"}),cell:({row:t})=>{const s=t.getValue("sideItems");if(!s)return e.jsx("div",{children:"Món chính"});const i=s==="Món ăn kèm"?"Món ăn kèm":s;return e.jsx(qe,{children:e.jsxs($e,{children:[e.jsx(We,{asChild:!0,children:e.jsx("div",{className:"max-w-[120px] cursor-help truncate text-sm",children:i})}),e.jsx(Qe,{children:e.jsx("p",{className:"max-w-[300px]",children:i})})]})})},enableSorting:!0,enableHiding:!0},{accessorKey:"city",header:({column:t})=>e.jsx(G,{column:t,title:"Thành phố"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("city")}),enableSorting:!1,enableHiding:!0},{accessorKey:"buffetConfig",header:({column:t})=>e.jsx(G,{column:t,title:"Cấu hình buffet"}),cell:({row:t})=>{var a;const s=t.original;return((a=s.extra_data)==null?void 0:a.is_buffet_item)===1?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Đã cấu hình"}),e.jsx(I,{variant:"outline",size:"sm",onClick:()=>l(s),className:"h-6 px-2 text-xs",children:e.jsx(de,{className:"h-3 w-3"})})]}):e.jsxs(I,{variant:"outline",size:"sm",onClick:()=>l(s),className:"h-7 px-2 text-xs",children:[e.jsx(de,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{accessorKey:"customization",header:({column:t})=>e.jsx(G,{column:t,title:"Customization"}),cell:({row:t,table:s})=>{var h;const i=t.original,a=s.options.meta,r=i.customization_uid,o=(h=a==null?void 0:a.customizations)==null?void 0:h.find(c=>c.id===r);return o?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:o.name}),e.jsx(I,{variant:"outline",size:"sm",onClick:()=>{var c;return(c=a==null?void 0:a.onCustomizationClick)==null?void 0:c.call(a,i)},className:"h-6 px-2 text-xs",children:e.jsx(de,{className:"h-3 w-3"})})]}):e.jsxs(I,{variant:"outline",size:"sm",onClick:()=>{var c;return(c=a==null?void 0:a.onCustomizationClick)==null?void 0:c.call(a,i)},className:"h-7 px-2 text-xs",children:[e.jsx(de,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{id:"copy",header:"Sao chép tạo món mới",cell:({row:t,table:s})=>{const i=t.original,a=s.options.meta;return e.jsxs(I,{variant:"ghost",size:"sm",className:"ml-14 h-8 w-8",onClick:r=>{var o;r.stopPropagation(),(o=a==null?void 0:a.onCopyClick)==null||o.call(a,i)},children:[e.jsx(Ft,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép thiết bị ",i.item_name]})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"isActive",header:({column:t})=>e.jsx(Ve,{column:t,title:"Thao tác",defaultSort:"desc"}),enableSorting:!0,cell:({row:t,table:s})=>{const i=t.original,a=t.getValue("isActive"),r=s.options.meta;return e.jsx("div",{onClick:o=>{var h;o.stopPropagation(),(h=r==null?void 0:r.onToggleStatus)==null||h.call(r,i)},className:"cursor-pointer",children:e.jsx(Dt,{isActive:a,activeText:"Active",inactiveText:"Deactive"})})},enableHiding:!0},{id:"actions",cell:({row:t,table:s})=>{const i=t.original,a=s.options.meta;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs(I,{variant:"ghost",size:"sm",onClick:r=>{var o;r.stopPropagation(),(o=a==null?void 0:a.onDeleteClick)==null||o.call(a,i)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(Et,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa món ",i.item_name]})]})})},enableSorting:!1,enableHiding:!1,size:80}],hs=ms;function us({currentPage:l,onPageChange:t,hasNextPage:s}){const i=()=>{l>1&&t(l-1)},a=()=>{s&&t(l+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(I,{variant:"outline",size:"sm",onClick:i,disabled:l===1,className:"flex items-center gap-2",children:[e.jsx(Rt,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:l}),e.jsxs(I,{variant:"outline",size:"sm",onClick:a,disabled:!s,className:"flex items-center gap-2",children:["Sau",e.jsx(Vt,{className:"h-4 w-4"})]})]})}const xs=[{label:"Thứ 2",value:"2"},{label:"Thứ 3",value:"3"},{label:"Thứ 4",value:"4"},{label:"Thứ 5",value:"5"},{label:"Thứ 6",value:"6"},{label:"Thứ 7",value:"7"},{label:"Chủ Nhật",value:"1"}],ps=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}];function fs({table:l,selectedItemTypeUid:t="all",onItemTypeChange:s,selectedCityUid:i="all",onCityChange:a,selectedDaysOfWeek:r=[],onDaysOfWeekChange:o,selectedStatus:h="all",onStatusChange:c,onDeleteSelected:g}){var f;const[y,S]=_.useState(!1),{data:v=[]}=re(),{data:p=[]}=Y(),w=p.filter(n=>n.active===1),x=w.map(n=>({label:n.city_name,value:n.id})),j=w.map(n=>n.id).join(",");_.useEffect(()=>{i==="all"&&j&&a&&a(j)},[i,j,a]);const T=l.getState().columnFilters.length>0,P=l.getFilteredSelectedRowModel().rows.length;return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[P>0&&e.jsxs(I,{variant:"destructive",size:"sm",onClick:g,className:"h-9",children:[e.jsx(Se,{}),"Xóa món (",P,")"]}),e.jsx(zt,{placeholder:"Tìm kiếm món ăn...",value:((f=l.getColumn("name"))==null?void 0:f.getFilterValue())??"",onChange:n=>{var C;return(C=l.getColumn("name"))==null?void 0:C.setFilterValue(n.target.value)},className:"h-9 w-[150px] lg:w-[250px]"}),e.jsxs(me,{value:i,onValueChange:n=>{a&&a(n)},children:[e.jsx(he,{className:"h-10 w-[180px]",children:e.jsx(ue,{placeholder:"Chọn thành phố"})}),e.jsxs(xe,{children:[e.jsx(X,{value:j,children:"Tất cả thành phố"}),x.map(n=>e.jsx(X,{value:n.value,children:n.label},n.value))]})]}),e.jsxs(me,{value:h,onValueChange:c,children:[e.jsx(he,{className:"h-10 w-[180px]",children:e.jsx(ue,{placeholder:"Chọn Trạng thái"})}),e.jsx(xe,{children:ps.map(n=>e.jsx(X,{value:n.value,children:n.label},n.value))})]}),e.jsxs(I,{variant:"outline",size:"sm",onClick:()=>S(!y),className:"h-9",children:[e.jsx(Lt,{className:"h-4 w-4"}),"Nâng cao"]}),T&&e.jsxs(I,{variant:"ghost",onClick:()=>l.resetColumnFilters(),className:"h-10 px-2 lg:px-3",children:["Reset",e.jsx(Ht,{className:"ml-2 h-4 w-4"})]})]})}),y&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(me,{value:t,onValueChange:s,children:[e.jsx(he,{className:"h-10 w-[180px]",children:e.jsx(ue,{placeholder:"Chọn loại món"})}),e.jsxs(xe,{children:[e.jsx(X,{value:"all",children:"Tất cả nhóm món"}),v.filter(n=>n.active===1).map(n=>({label:n.item_type_name,value:n.id})).map(n=>e.jsx(X,{value:n.value,children:n.label},n.value))]})]}),e.jsx(Kt,{options:xs,value:r,onValueChange:o||(()=>{}),placeholder:"Chọn ngày trong tuần",className:"min-h-9 w-[300px]",maxCount:1})]})]})}function gs({columns:l,data:t,onCustomizationClick:s,onCopyClick:i,onToggleStatus:a,onRowClick:r,onDeleteClick:o,customizations:h,selectedItemTypeUid:c,onItemTypeChange:g,selectedCityUid:y,onCityChange:S,selectedDaysOfWeek:v,onDaysOfWeekChange:p,selectedStatus:w,onStatusChange:x,hasNextPageOverride:j,currentPage:T,onPageChange:P}){var H;const[f,n]=_.useState({}),[C,m]=_.useState({}),[E,V]=_.useState([]),[K,b]=_.useState([]),[u,d]=_.useState(!1),{deleteMultipleItemsAsync:D}=it(),A=()=>{d(!0)},M=async()=>{try{const z=B.getFilteredSelectedRowModel().rows.map(U=>U.original.id);await D(z),d(!1),B.resetRowSelection()}catch{}},O=(k,z)=>{const U=z.target;U.closest('input[type="checkbox"]')||U.closest("button")||U.closest('[role="button"]')||U.closest(".badge")||U.tagName==="BUTTON"||r==null||r(k)},B=ze({data:t,columns:l,state:{sorting:K,columnVisibility:C,rowSelection:f,columnFilters:E},enableRowSelection:!0,onRowSelectionChange:n,onSortingChange:b,onColumnFiltersChange:V,onColumnVisibilityChange:m,getCoreRowModel:Ke(),getFilteredRowModel:Bt(),getSortedRowModel:Pt(),getFacetedRowModel:Ot(),getFacetedUniqueValues:Mt(),meta:{onCustomizationClick:s,onCopyClick:i,onToggleStatus:a,onDeleteClick:o,customizations:h}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(fs,{table:B,selectedItemTypeUid:c,onItemTypeChange:g,selectedCityUid:y,onCityChange:S,selectedDaysOfWeek:v,onDaysOfWeekChange:p,selectedStatus:w,onStatusChange:x,onDeleteSelected:A}),e.jsxs(ge,{className:"rounded-md border",children:[e.jsxs(_e,{className:"relative",children:[e.jsx(ye,{children:B.getHeaderGroups().map(k=>e.jsx(q,{children:k.headers.map(z=>e.jsx(J,{colSpan:z.colSpan,children:z.isPlaceholder?null:pe(z.column.columnDef.header,z.getContext())},z.id))},k.id))}),e.jsx(je,{children:(H=B.getRowModel().rows)!=null&&H.length?B.getRowModel().rows.map(k=>e.jsx(q,{"data-state":k.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:z=>O(k.original,z),children:k.getVisibleCells().map(z=>e.jsx($,{children:pe(z.column.columnDef.cell,z.getContext())},z.id))},k.id)):e.jsx(q,{children:e.jsx($,{colSpan:l.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx(W,{orientation:"horizontal"})]}),e.jsx(us,{currentPage:T??1,onPageChange:k=>P&&P(k),hasNextPage:!!j}),e.jsx(Le,{open:u,onOpenChange:d,title:`Bạn có chắc muốn xóa ${B.getFilteredSelectedRowModel().rows.length} món đã chọn`,desc:"Hành động này không thể hoàn tác.",confirmText:"Xóa",cancelBtnText:"Hủy",className:"top-[30%] translate-y-[-50%]",handleConfirm:M,destructive:!0})]})}function _s(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(N,{className:"h-8 w-[250px]"}),e.jsx(N,{className:"h-8 w-[100px]"}),e.jsx(N,{className:"h-8 w-[100px]"}),e.jsx(N,{className:"h-8 w-[100px]"})]}),e.jsx(N,{className:"h-8 w-[100px]"})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsx("div",{className:"border-b p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(N,{className:"h-4 w-8"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-32"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-16"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-16"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-16"})]})}),Array.from({length:10}).map((l,t)=>e.jsx("div",{className:"border-b p-4 last:border-b-0",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(N,{className:"h-4 w-8"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-32"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-16"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-16"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-16"})]})},t))]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(N,{className:"h-8 w-[200px]"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(N,{className:"h-8 w-[100px]"}),e.jsx(N,{className:"h-8 w-8"}),e.jsx(N,{className:"h-8 w-8"})]})]})]})}function ys({open:l,onOpenChange:t,data:s,onSave:i}){var b;const[a,r]=_.useState(s),[o,h]=_.useState([]),[c,g]=_.useState(!1),{company:y,brands:S}=le(u=>u.auth),v=S==null?void 0:S[0],{bulkCreateItemsInCity:p,isBulkCreating:w}=nt(),{data:x=[]}=re({skip_limit:!0}),{data:j=[]}=we({skip_limit:!0}),{data:T=[]}=be(),{data:P=[]}=Y();Oe.useEffect(()=>{r(s)},[s]),_.useEffect(()=>{if(s&&s.length>0){const u=s[0]||[],D=s.slice(1).map(A=>{const M={};return u.forEach((O,B)=>{M[String(O)]=A[B]||""}),M});h(D)}},[s]);const f=u=>{if(typeof u=="string"){const d=u.toLowerCase().trim();return d==="có"||d==="yes"||d==="1"||d==="true"?1:0}return u?1:0},n=async()=>{if(i){i(a),F.success("Data saved successfully"),t(!1);return}if(!(y!=null&&y.id)||!(v!=null&&v.id)){F.error("Thiếu thông tin công ty hoặc thương hiệu");return}if(o.length===0){F.error("Không có dữ liệu để import");return}g(!0);try{const u=o.map(d=>{const D=P.find(k=>k.city_name===d["Thành phố"]);if(!D)throw new Error(`Không tìm thấy thành phố: ${d["Thành phố"]}`);const A=T.find(k=>k.unit_id===d["Đơn vị"]),M=T.find(k=>k.unit_id==="MON"),O=x.find(k=>k.item_type_id===d.Nhóm||k.item_type_name===d.Nhóm),B=x.find(k=>k.item_type_name==="LOẠI KHÁC"),H=j.find(k=>k.item_class_id===d["Loại món"]||k.item_class_name===d["Loại món"]);return{company_uid:y.id,brand_uid:v.id,city_uid:D.id,item_id:d["Mã món"],unit_uid:(A==null?void 0:A.id)||(M==null?void 0:M.id)||"",ots_price:Number(d.Giá)||0,ta_price:Number(d.Giá)||0,ots_tax:(Number(d["VAT (%)"])||0)/100,ta_tax:(Number(d["VAT (%)"])||0)/100,item_name:d.Tên,item_id_barcode:d["Mã barcode"]||"",is_eat_with:f(d["Món ăn kèm"]),item_type_uid:(O==null?void 0:O.id)||(B==null?void 0:B.id)||"",item_class_uid:(H==null?void 0:H.id)||null,description:d["Mô tả"]||"",item_id_mapping:String(d.SKU||""),time_cooking:(Number(d["Thời gian chế biến (phút)"])||0)*6e4,time_sale_date_week:Number(d.Ngày)||0,time_sale_hour_day:Number(d.Giờ)||0,sort:Number(d["Thứ tự"])||1,image_path_thumb:"",image_path:d["Hình ảnh"]||"",extra_data:{no_update_quantity_toping:f(d["Không cập nhật số lượng món ăn kèm"]),enable_edit_price:f(d["Cho phép sửa giá khi bán"]),is_virtual_item:f(d["Cấu hình món ảo"]),is_item_service:f(d["Cấu hình món dịch vụ"]),is_buffet_item:f(d["Cấu hình món ăn là vé buffet"])}}});await p(u,{onSuccess:()=>{g(!1),t(!1)},onError:d=>{console.error("Error creating items:",d),g(!1)}})}catch(u){console.error("Error creating items:",u),F.error(u instanceof Error?u.message:"Có lỗi xảy ra khi tạo món ăn")}},C=()=>{t(!1)},m=Oe.useCallback(u=>{const d=a.filter((D,A)=>A!==u);r(d)},[a]),{tableData:E,columns:V}=_.useMemo(()=>{if(!a||a.length===0)return{tableData:[],columns:[]};const u=a[0]||[],d=a.slice(1),D=[{id:"actions",header:"-",cell:({row:M})=>e.jsx(I,{variant:"ghost",size:"sm",onClick:()=>m(M.original._originalIndex),className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:e.jsx(kt,{className:"h-4 w-4"})}),enableSorting:!1,enableHiding:!1,size:50,meta:{className:"w-12 text-center sticky left-0 bg-background z-20 border-r"}},...u.map((M,O)=>({id:`col_${O}`,accessorKey:`col_${O}`,header:String(M),cell:({row:B})=>e.jsx("div",{className:"min-w-[150px] whitespace-nowrap",children:B.getValue(`col_${O}`)}),enableSorting:!1,enableHiding:!1,meta:{className:"min-w-[150px] px-4 whitespace-nowrap"}}))];return{tableData:d.map((M,O)=>{const B={_originalIndex:O+1};return M.forEach((H,k)=>{B[`col_${k}`]=H}),B}),columns:D}},[a,m]),K=ze({data:E,columns:V,getCoreRowModel:Ke()});return!a||a.length===0?null:e.jsx(Z,{open:l,onOpenChange:t,children:e.jsxs(ee,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(te,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(se,{className:"text-xl font-semibold",children:"Thêm món từ file"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(ge,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(_e,{children:[e.jsx(ye,{className:"sticky top-0 z-10 bg-white",children:K.getHeaderGroups().map(u=>e.jsx(q,{children:u.headers.map(d=>{var D;return e.jsx(J,{className:((D=d.column.columnDef.meta)==null?void 0:D.className)||"",children:d.isPlaceholder?null:pe(d.column.columnDef.header,d.getContext())},d.id)})},u.id))}),e.jsx(je,{children:(b=K.getRowModel().rows)!=null&&b.length?K.getRowModel().rows.map(u=>e.jsx(q,{className:"hover:bg-muted/50",children:u.getVisibleCells().map(d=>{var D;return e.jsx($,{className:((D=d.column.columnDef.meta)==null?void 0:D.className)||"",children:pe(d.column.columnDef.cell,d.getContext())},d.id)})},u.id)):e.jsx(q,{children:e.jsx($,{colSpan:V.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx(W,{orientation:"horizontal"}),e.jsx(W,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(I,{variant:"outline",onClick:C,children:"Đóng"}),e.jsx(I,{onClick:n,disabled:c||w,className:"bg-green-600 hover:bg-green-700",children:c||w?"Đang lưu...":"Lưu"})]})]})]})})}function js(){const{open:l,setOpen:t}=fe(),[s,i]=_.useState(!1),[a,r]=_.useState([]),o=_.useRef(null),{data:h=[]}=re(),{data:c=[]}=we(),{data:g=[]}=be(),{data:y=[]}=Y(),{downloadImportTemplateAsync:S,isPending:v}=lt(),p=async()=>{try{await S({itemTypes:h,itemClasses:c,units:g,cities:y})}catch{F.error("Lỗi khi tải template")}},w=()=>{var j;(j=o.current)==null||j.click()},x=j=>{var f;const T=(f=j.target.files)==null?void 0:f[0];if(!T)return;const P=new FileReader;P.onload=n=>{var C;try{const m=new Uint8Array((C=n.target)==null?void 0:C.result),E=Ie(m,{type:"array"}),V=E.SheetNames[0],K=E.Sheets[V],b=Te.sheet_to_json(K,{defval:"",raw:!1});if(b.length===0){F.error("File không có dữ liệu");return}const u=new Set,d=b.map(D=>{const A=(D["Mã món"]??"").toString().trim();A&&u.add(A);let M=A;if(!M){let O=Pe();for(;u.has(O);)O=Pe();u.add(O),M=O}return{...D,"Mã món":M}});if(d.length>0){const D=Object.keys(d[0]),A=[D,...d.map(M=>D.map(O=>M[O]||""))];r(A)}else r([]);t(null),i(!0),o.current&&(o.current.value="")}catch{F.error("Lỗi khi đọc file. Vui lòng kiểm tra định dạng file.")}},P.readAsArrayBuffer(T)};return e.jsxs(e.Fragment,{children:[e.jsx(Z,{open:l==="import",onOpenChange:j=>t(j?"import":null),children:e.jsxs(ee,{className:"max-w-2xl",children:[e.jsx(te,{children:e.jsx(se,{children:"Thêm món"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Tải file mẫu"}),e.jsx(I,{variant:"outline",size:"sm",onClick:p,disabled:v,className:"flex items-center gap-2",children:v?"Đang tải...":e.jsxs(e.Fragment,{children:["Tải xuống",e.jsx(Ce,{className:"h-4 w-4"})]})})]})}),e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Không được để trống các cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Tên, Thành phố"}),"."]}),e.jsxs("p",{children:["Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã nhóm, mã loại, mã đơn vị đã có vào cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Nhóm, Loại món"}),"."]}),e.jsxs("p",{children:["Mã đơn vị món, mã thành phố có thể xem trong sheet"," ",e.jsx("span",{className:"font-mono text-blue-600",children:"Guide"})," của file mẫu."]})]})]}),e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"})]}),e.jsxs(I,{variant:"outline",size:"sm",onClick:w,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(ke,{className:"h-4 w-4"})]})]})})]})]})}),e.jsx("input",{ref:o,type:"file",accept:".xlsx,.xls",onChange:x,style:{display:"none"}}),e.jsx(ys,{open:s,onOpenChange:i,data:a})]})}function ws({open:l,onOpenChange:t,data:s}){const[i,a]=_.useState(s),[r,o]=_.useState(!1),{user:h,company:c}=le(f=>f.auth),{selectedBrand:g}=Xe(),{mutate:y,isPending:S}=rt(),{data:v=[]}=re({skip_limit:!0}),{data:p=[]}=we({skip_limit:!0}),{data:w=[]}=be(),{data:x=[]}=Y();_.useEffect(()=>{a(s)},[s]);const j=f=>{a(n=>n.filter((C,m)=>m!==f))},T=async()=>{if(!c||!g){F.error("Thiếu thông tin cần thiết để cập nhật");return}o(!0);const f=i.map(n=>{const C=w.find(u=>u.unit_id===n.unit_id),m=x.find(u=>u.city_name===n.city_name),E=v.find(u=>u.item_type_id===n.item_type_id||u.item_type_name===n.item_type_name),V=p.find(u=>u.item_class_id===n.item_class_id||u.item_class_name===n.item_class_name),K=w.find(u=>u.unit_id==="MON"),b=v.find(u=>u.item_type_name==="LOẠI KHÁC");return{item_id:n.item_id,item_name:n.item_name,description:n.description||"",ots_price:n.ots_price||0,ots_tax:(n.ots_tax||0)/100,ta_price:n.ots_price||0,ta_tax:(n.ots_tax||0)/100,time_sale_hour_day:String(n.time_sale_hour_day??0),time_sale_date_week:String(n.time_sale_date_week??0),allow_take_away:1,is_eat_with:n.is_eat_with||0,image_path:n.image_path||"",image_path_thumb:n.image_path?`${n.image_path}?width=185`:"",item_color:"",list_order:n.list_order||0,is_service:n.is_item_service||0,is_material:0,active:n.active||1,user_id:"",is_foreign:0,quantity_default:0,price_change:n.price_change||0,currency_type_id:"",point:0,is_gift:0,is_fc:0,show_on_web:0,show_price_on_web:0,cost_price:0,is_print_label:n.is_print_label||0,quantity_limit:0,is_kit:0,time_cooking:(n.time_cooking||0)*6e4,item_id_barcode:n.item_id_barcode||"",process_index:0,is_allow_discount:n.is_allow_discount||0,quantity_per_day:0,item_id_eat_with:"",is_parent:0,is_sub:0,item_id_mapping:String(n.sku||""),effective_date:0,expire_date:0,sort:n.list_order||1,sort_online:1e3,extra_data:{cross_price:n.cross_price||[],formula_qrcode:n.inqr_formula||"",is_buffet_item:n.is_buffet_item||0,up_size_buffet:[],is_item_service:n.is_item_service||0,is_virtual_item:n.is_virtual_item||0,price_by_source:n.price_by_source||[],enable_edit_price:n.price_change||0,exclude_items_buffet:n.exclude_items_buffet||[],no_update_quantity_toping:n.no_update_quantity_toping||0},revision:0,unit_uid:(C==null?void 0:C.id)||(K==null?void 0:K.id)||"",unit_secondary_uid:null,item_type_uid:(E==null?void 0:E.id)||(b==null?void 0:b.id)||"",item_class_uid:(V==null?void 0:V.id)||void 0,source_uid:null,brand_uid:g.id,city_uid:(m==null?void 0:m.id)||"",company_uid:c.id,customization_uid:n.customization_uid||"",is_fabi:1,deleted:!1,created_by:(h==null?void 0:h.email)||"",updated_by:(h==null?void 0:h.email)||"",deleted_by:null,created_at:n.created_at||Math.floor(Date.now()/1e3),updated_at:Math.floor(Date.now()/1e3),deleted_at:null,cities:m?[{id:m.id,city_id:m.city_id||"",fb_city_id:m.fb_city_id||"",city_name:m.city_name,image_path:m.image_path,description:m.description||"",active:m.active||1,extra_data:m.extra_data,revision:m.revision||0,sort:m.sort||0,created_by:m.created_by,updated_by:m.updated_by,deleted_by:m.deleted_by,created_at:m.created_at||0,updated_at:m.updated_at||0,deleted_at:m.deleted_at,items_cities:{item_uid:n.id,city_uid:m.id}}]:[],id:n.id}});y(f,{onSuccess:()=>{o(!1),t(!1)},onError:n=>{console.error("Error updating items:",n),F.error(`Có lỗi xảy ra khi cập nhật món ăn: ${n}`),o(!1)}})},P=[{key:"item_id",label:"Mã món",width:"120px"},{key:"city_name",label:"Thành phố",width:"120px"},{key:"item_name",label:"Tên",width:"200px"},{key:"ots_price",label:"Giá",width:"100px"},{key:"active",label:"Trạng thái",width:"100px"},{key:"item_id_barcode",label:"Mã barcode",width:"120px"},{key:"is_eat_with",label:"Món ăn kèm",width:"120px"},{key:"no_update_quantity_toping",label:"Không cập nhật số lượng",width:"180px"},{key:"unit_name",label:"Đơn vị",width:"100px"},{key:"item_type_id",label:"Nhóm",width:"120px"},{key:"item_type_name",label:"Tên nhóm",width:"150px"},{key:"item_class_id",label:"Loại món",width:"120px"},{key:"item_class_name",label:"Tên loại",width:"150px"},{key:"description",label:"Mô tả",width:"200px"},{key:"sku",label:"SKU",width:"100px"},{key:"ots_tax",label:"VAT (%)",width:"80px"},{key:"time_cooking",label:"Thời gian chế biến (phút)",width:"180px"},{key:"price_change",label:"Cho phép sửa giá khi bán",width:"180px"},{key:"is_virtual_item",label:"Cấu hình món ảo",width:"150px"},{key:"is_item_service",label:"Cấu hình món dịch vụ",width:"180px"},{key:"is_buffet_item",label:"Cấu hình món ăn là vé buffet",width:"200px"},{key:"time_sale_hour_day",label:"Giờ",width:"80px"},{key:"time_sale_date_week",label:"Ngày",width:"80px"},{key:"list_order",label:"Thứ tự",width:"80px"},{key:"image_path",label:"Hình ảnh",width:"120px"},{key:"inqr_formula",label:"Công thức inQR cho máy pha trà",width:"220px"}];return e.jsx(Z,{open:l,onOpenChange:t,children:e.jsxs(ee,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(te,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(se,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(ge,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(_e,{children:[e.jsx(ye,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(q,{children:[e.jsx(J,{className:"w-12"}),P.map(f=>e.jsx(J,{style:{width:f.width},children:f.label},f.key))]})}),e.jsx(je,{children:i.map((f,n)=>e.jsxs(q,{children:[e.jsx($,{children:e.jsx(I,{variant:"ghost",size:"icon",onClick:()=>j(n),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(Se,{className:"h-4 w-4"})})}),P.map(C=>e.jsx($,{style:{width:C.width},children:(()=>{var E;const m=f[C.key];return C.key==="ots_price"?e.jsxs("span",{className:"text-right",children:[((E=Number(m))==null?void 0:E.toLocaleString("vi-VN"))||0," ₫"]}):C.key==="active"?e.jsx("span",{children:m}):C.key==="item_id"||C.key==="item_id_barcode"?e.jsx("span",{className:"font-mono text-sm",children:m||""}):C.key==="item_name"?e.jsx("span",{className:"font-medium",children:m||""}):["is_eat_with","no_update_quantity_toping","price_change","is_virtual_item","is_item_service","is_buffet_item"].includes(C.key)?e.jsx("span",{className:"text-center",children:m}):e.jsx("span",{children:m||""})})()},C.key))]},n))})]}),e.jsx(W,{orientation:"horizontal"}),e.jsx(W,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(I,{variant:"outline",onClick:()=>t(!1),children:"Đóng"}),e.jsx(I,{onClick:T,disabled:r||S,children:r||S?"Đang lưu...":"Lưu"})]})]})]})})}function bs({open:l,onOpenChange:t}){const[s,i]=_.useState("all"),[a,r]=_.useState("all"),[o,h]=_.useState("all"),[c,g]=_.useState([]),[y,S]=_.useState(!1),v=_.useRef(null),{data:p=[]}=re(),{data:w=[]}=we(),{data:x=[]}=be(),{data:j=[]}=Y(),{fetchItemsDataAsync:T,isPending:P}=ct(),f=[{label:"Tất cả nhóm món",value:"all"},...p.filter(b=>b.active===1).map(b=>({label:b.item_type_name,value:b.id}))],n=[{label:"Tất cả thành phố",value:"all"},...j.filter(b=>b.active===1).map(b=>({label:b.city_name,value:b.id}))],C=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}],m=async()=>{try{const b=await T({city_uid:s!=="all"?s:void 0,item_type_uid:a!=="all"?a:void 0,active:o!=="all"?o:void 0});await ot({itemTypes:p,itemClasses:w,units:x},b),F.success("Tải file thành công!")}catch{F.error("Lỗi khi tải file")}},E=b=>({ID:"id","Mã món":"item_id","Thành phố":"city_name",Tên:"item_name",Giá:"ots_price","Trạng thái":"active","Mã barcode":"item_id_barcode","Món ăn kèm":"is_eat_with","Không cập nhật số lượng món ăn kèm":"no_update_quantity_toping","Đơn vị":"unit_name",Nhóm:"item_type_id","Tên nhóm":"item_type_name","Loại món":"item_class_id","Tên loại":"item_class_name","Mô tả":"description",SKU:"sku","VAT (%)":"ots_tax","Thời gian chế biến (phút)":"time_cooking","Cho phép sửa giá khi bán":"price_change","Cấu hình món ảo":"is_virtual_item","Cấu hình món dịch vụ":"is_item_service","Cấu hình món ăn là vé buffet":"is_buffet_item",Giờ:"time_sale_hour_day",Ngày:"time_sale_date_week","Thứ tự":"list_order","Hình ảnh":"image_path","Công thức inQR cho máy pha trà":"inqr_formula"})[b]||b.toLowerCase().replace(/\s+/g,"_"),V=b=>{var D;const u=(D=b.target.files)==null?void 0:D[0];if(!u)return;const d=new FileReader;d.onload=A=>{var M;try{const O=new Uint8Array((M=A.target)==null?void 0:M.result),B=Ie(O,{type:"array"}),H=B.SheetNames[0],k=B.Sheets[H],z=Te.sheet_to_json(k,{header:1});if(z.length>0){const U=z,R=U[0]||[],ce=U.slice(1).map((He,Ne)=>{const ie={id:`temp_${Ne}`};return R.forEach((Ee,Ue)=>{const L=E(String(Ee)),oe=He[Ue];Ne===0&&console.log(`Header: "${Ee}" -> Key: "${L}", Value: "${oe}"`),L==="ots_price"||L==="ots_tax"||L==="time_cooking"||L==="time_sale_hour_day"||L==="time_sale_date_week"||L==="list_order"||L==="active"||L==="is_eat_with"||L==="no_update_quantity_toping"||L==="price_change"||L==="is_virtual_item"||L==="is_item_service"||L==="is_buffet_item"?ie[L]=Number(oe)||0:ie[L]=oe||""}),Ne===0&&console.log("First item after processing:",ie),ie});g(ce),S(!0),F.success("File uploaded successfully")}}catch{F.error("Error parsing file")}},d.readAsArrayBuffer(u)},K=()=>{var b;(b=v.current)==null||b.click()};return e.jsxs(e.Fragment,{children:[e.jsx(Z,{open:l,onOpenChange:t,children:e.jsxs(ee,{className:"max-w-2xl lg:max-w-xl",children:[e.jsx(te,{children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(se,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(ve,{options:n,value:s,onValueChange:i,placeholder:"Tất cả thành phố",searchPlaceholder:"Tìm thành phố...",className:"flex-1"}),e.jsx(ve,{options:f,value:a,onValueChange:r,placeholder:"Tất cả nhóm món",searchPlaceholder:"Tìm nhóm món...",className:"flex-1"}),e.jsx(ve,{options:C,value:o,onValueChange:h,placeholder:"Tất cả trạng thái",searchPlaceholder:"Tìm trạng thái...",className:"flex-1"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải xuống"}),e.jsxs(I,{variant:"outline",size:"sm",onClick:m,disabled:P,className:"flex items-center gap-2",children:[e.jsx(Ce,{className:"h-4 w-4"}),P&&"Đang tải..."]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không sửa các cột :"}),e.jsx("p",{className:"font-mono text-sm text-blue-600",children:"ID, Mã món, Thành phố, Đơn vị, Tên nhóm, Tên loại."})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsxs(I,{variant:"outline",size:"sm",onClick:K,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(ke,{className:"h-4 w-4"})]}),e.jsx("input",{ref:v,type:"file",accept:".xlsx,.xls",onChange:V,style:{display:"none"}})]})]})]})]})}),e.jsx(ws,{open:y,onOpenChange:S,data:c})]})}function Ns({open:l,onOpenChange:t,data:s,originalItems:i,sources:a}){const[r,o]=_.useState(s),{bulkUpdatePriceBySource:h,isUpdating:c}=os();_.useEffect(()=>{o(s)},[s]);const g=p=>{o(w=>w.filter((x,j)=>j!==p))},y=async()=>{if(r.length===0){F.error("Không có dữ liệu để lưu");return}try{await h({previewItems:r,originalItems:i,sources:a}),t(!1)}catch(p){console.error("Error saving price by source configuration:",p)}},S=[{key:"item_uid",label:"item_uid",width:"280px"},{key:"item_id",label:"item_id",width:"120px"},{key:"item_name",label:"item_name",width:"200px"},{key:"ots_price",label:"Giá gốc",width:"100px"},{key:"ots_tax",label:"Vat",width:"80px"},{key:"ZALO [10000045]",label:"ZALO [10000045]",width:"120px"},{key:"FACEBOOK [10000049]",label:"FACEBOOK [10000049]",width:"140px"},{key:"SO [10000134]",label:"SO [10000134]",width:"120px"},{key:"CRM [10000162]",label:"CRM [10000162]",width:"120px"},{key:"VNPAY [10000165]",label:"VNPAY [10000165]",width:"120px"},{key:"GOJEK (GOVIET) [10000168]",label:"GOJEK (GOVIET) [10000168]",width:"180px"},{key:"ShopeeFood [10000169]",label:"ShopeeFood [10000169]",width:"160px"},{key:"MANG VỀ [10000171]",label:"MANG VỀ [10000171]",width:"140px"},{key:"TẠI CHỖ [10000172]",label:"TẠI CHỖ [10000172]",width:"140px"},{key:"CALL CENTER [10000176]",label:"CALL CENTER [10000176]",width:"160px"},{key:"O2O [10000216]",label:"O2O [10000216]",width:"120px"},{key:"BEFOOD [10000253]",label:"BEFOOD [10000253]",width:"140px"}],v=p=>{if(p==null||p==="")return"";const w=typeof p=="string"?parseFloat(p):p;return isNaN(w)?"":w.toLocaleString("vi-VN")};return e.jsx(Z,{open:l,onOpenChange:t,children:e.jsxs(ee,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(te,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(se,{className:"text-xl font-semibold",children:"Thêm cấu hình giá món theo nguồn đơn"})}),e.jsxs("div",{className:"flex h-full flex-col space-y-4 overflow-hidden",children:[e.jsxs("div",{className:"flex-shrink-0 text-sm text-gray-600",children:["Tổng số món: ",e.jsx("span",{className:"font-medium",children:r.length})]}),e.jsx("div",{className:"min-h-0 flex-1",children:e.jsxs(ge,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(_e,{children:[e.jsx(ye,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(q,{children:[e.jsx(J,{className:"w-12"}),S.map(p=>e.jsx(J,{style:{width:p.width},children:p.label},p.key))]})}),e.jsx(je,{children:r.map((p,w)=>e.jsxs(q,{children:[e.jsx($,{children:e.jsx(I,{variant:"ghost",size:"icon",onClick:()=>g(w),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(Se,{className:"h-4 w-4"})})}),S.map(x=>e.jsxs($,{style:{width:x.width},children:[x.key==="item_uid"&&e.jsx("span",{className:"font-mono text-xs text-gray-600",children:p[x.key]}),x.key==="item_id"&&e.jsx("span",{className:"font-mono text-sm",children:p[x.key]}),x.key==="item_name"&&e.jsx("span",{className:"font-medium",children:p[x.key]}),x.key==="item_type_name"&&e.jsx("span",{className:"text-sm",children:p[x.key]}),x.key==="ots_price"&&e.jsx("span",{className:"text-right font-medium",children:v(p[x.key])}),x.key==="ots_tax"&&e.jsx("span",{className:"text-center",children:p[x.key]}),x.key.includes("[")&&e.jsx("span",{className:"text-right font-medium text-blue-600",children:p[x.key]?`${v(p[x.key])}`:""}),!x.key.includes("[")&&x.key!=="item_uid"&&x.key!=="item_id"&&x.key!=="item_name"&&x.key!=="item_type_name"&&x.key!=="ots_price"&&x.key!=="ots_tax"&&e.jsx("span",{children:p[x.key]||""})]},x.key))]},w))})]}),e.jsx(W,{orientation:"horizontal"}),e.jsx(W,{orientation:"vertical"})]})}),e.jsxs("div",{className:"flex flex-shrink-0 items-center justify-between border-t pt-4",children:[e.jsx(I,{variant:"outline",onClick:()=>t(!1),children:"Đóng"}),e.jsx(I,{onClick:y,disabled:c||r.length===0,children:c?"Đang lưu...":"Lưu"})]})]})]})})}function vs({open:l,onOpenChange:t,cities:s}){var C;const[i,a]=_.useState(""),[r,o]=_.useState(null),[h,c]=_.useState(!1),[g,y]=_.useState(!1),[S,v]=_.useState([]),[p,w]=_.useState(!1),{items:x,sources:j,itemTypes:T,isLoading:P}=rs(i,!!i),f=async()=>{if(!i){F.error("Vui lòng chọn thành phố trước");return}if(P){F.error("Đang tải dữ liệu, vui lòng chờ...");return}if(!x.length){F.error("Không có món nào trong thành phố này");return}try{c(!0);const m=s.find(V=>V.id===i),E=(m==null?void 0:m.city_name)||"Unknown";await Zt(x,T,E),F.success("Đã tải xuống file template thành công")}catch(m){console.error("Error downloading template:",m),F.error("Có lỗi xảy ra khi tải xuống file template")}finally{c(!1)}},n=()=>{if(!i){F.error("Vui lòng chọn thành phố trước");return}const m=document.createElement("input");m.type="file",m.accept=".xlsx,.xls",m.onchange=async E=>{var b;const V=(b=E.target.files)==null?void 0:b[0];if(!V)return;const K=ss(V);if(!K.isValid){F.error(K.error);return}try{y(!0),o(V);const u=await ts(V);if(u.length===0){F.error("File không có dữ liệu hợp lệ");return}v(u),w(!0),t(!1),F.success(`Đã xử lý file thành công: ${u.length} món`)}catch(u){console.error("Error processing file:",u),F.error("Có lỗi xảy ra khi xử lý file")}finally{y(!1)}},m.click()};return e.jsxs(Z,{open:l,onOpenChange:t,children:[e.jsxs(ee,{className:"max-w-2xl",children:[e.jsx(te,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(se,{className:"text-xl font-semibold",children:"Thêm cấu hình giá món theo nguồn đơn"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 1. Chọn thành phố"}),e.jsxs(me,{value:i,onValueChange:a,children:[e.jsx(he,{className:"w-full",children:e.jsx(ue,{placeholder:"Chọn thành phố"})}),e.jsx(xe,{children:s.map(m=>e.jsx(X,{value:m.id,children:m.city_name},m.id))})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 2. Tải file dữ liệu để lấy món và nguồn đã có"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"text-sm text-gray-600",children:"Tải xuống"})}),e.jsxs(I,{variant:"outline",onClick:f,disabled:h||!i||P,className:"flex items-center gap-2",children:[e.jsx($t,{className:"h-4 w-4"}),h?"Đang tải...":P?"Đang tải dữ liệu...":"Tải xuống"]})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Nhập giá theo nguồn tương ứng với từng món vào cột ",e.jsx("strong",{children:"price"}),"."]}),e.jsx("p",{children:"Bỏ trống hoặc xoá dòng với những nguồn không có cấu hình giá."}),e.jsx("p",{children:e.jsx("strong",{className:"text-red-600",children:"Không sửa các cột item_uid, item_name."})})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),r&&e.jsxs("span",{className:"mt-1 text-xs text-gray-500",children:["File đã chọn: ",r.name]})]}),e.jsxs(I,{onClick:n,disabled:!i||g,className:"flex items-center gap-2",children:[e.jsx(Wt,{className:"h-4 w-4"}),g?"Đang xử lý...":"Tải file lên"]})]})]})]})]}),e.jsx(Ns,{open:p,onOpenChange:w,data:S,originalItems:x,sources:j,storeName:(C=s.find(m=>m.id===i))==null?void 0:C.city_name})]})}function Cs(){const{open:l,setOpen:t,currentRow:s,setCurrentRow:i}=fe(),{deleteItemAsync:a}=dt(),{data:r=[]}=Y();return e.jsxs(e.Fragment,{children:[e.jsx(bs,{open:l==="export-dialog",onOpenChange:()=>t(null)}),e.jsx(js,{}),e.jsx(vs,{open:l==="price-by-source-config",onOpenChange:()=>t(null),cities:r}),s&&e.jsx(e.Fragment,{children:e.jsx(Le,{destructive:!0,open:l==="delete",onOpenChange:o=>{o||(t(null),setTimeout(()=>{i(null)},500))},handleConfirm:async()=>{t(null),setTimeout(()=>{i(null)},500),await a(s.id||"")},className:"max-w-md",title:"Bạn có muốn xoá ?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")})]})}function ks(){const l=Ae(),[t,s]=_.useState(1),{setOpen:i,setCurrentRow:a}=fe(),{updateStatusAsync:r}=ht(),{updateItemAsync:o}=ut(),{isCustomizationDialogOpen:h,isBuffetItem:c,isBuffetConfigModalOpen:g,setIsCustomizationDialogOpen:y,setIsBuffetItem:S,selectedMenuItem:v,setSelectedMenuItem:p,setIsBuffetConfigModalOpen:w,selectedBuffetMenuItem:x,setSelectedBuffetMenuItem:j,selectedItemTypeUid:T,setSelectedItemTypeUid:P,selectedCityUid:f,setSelectedCityUid:n,selectedDaysOfWeek:C,setSelectedDaysOfWeek:m,selectedStatus:E,setSelectedStatus:V}=as(),K=_.useMemo(()=>({...T!=="all"&&{item_type_uid:T},...f!=="all"&&{city_uid:f},...C.length>0&&{time_sale_date_week:C.join(",")},...E!=="all"&&{active:parseInt(E,10)},page:t}),[T,f,C,E,t]);_.useEffect(()=>{s(1)},[T,f,C,E]);const{data:b=[],isLoading:u,error:d,hasNextPage:D}=xt({params:K}),{data:A=[]}=Ye({skip_limit:!0,list_city_uid:f!=="all"?[f]:void 0}),M=R=>{p(R),y(!0)},O=R=>{var ae,ce;p(R),j(((ae=R==null?void 0:R.extra_data)==null?void 0:ae.exclude_items_buffet)||[]),S(((ce=R==null?void 0:R.extra_data)==null?void 0:ce.is_buffet_item)===1),w(!0)},B=R=>{l({to:"/menu/items/items-in-city/detail",search:{id:R.id||""}})},H=R=>{a(R),i("delete")},k=R=>{l({to:"/menu/items/items-in-city/detail/$id",params:{id:R.id||""}})},z=async R=>{const ae=R.active?0:1;await r({id:R.id||"",active:ae})},U=hs({onBuffetConfigClick:O});return d?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:d&&`Món ăn: ${(d==null?void 0:d.message)||"Lỗi không xác định"}`})]})}):e.jsxs(e.Fragment,{children:[e.jsx(Ze,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(st,{}),e.jsx(at,{}),e.jsx(tt,{})]})}),e.jsxs(et,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Món ăn tại thành phố"})}),e.jsx(ds,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[u&&e.jsx(_s,{}),!u&&e.jsx(gs,{columns:U,data:b,onCustomizationClick:M,onCopyClick:B,onToggleStatus:z,onRowClick:k,onDeleteClick:H,customizations:A,selectedItemTypeUid:T,onItemTypeChange:P,selectedCityUid:f,onCityChange:n,selectedDaysOfWeek:C,onDaysOfWeekChange:m,selectedStatus:E,onStatusChange:V,hasNextPageOverride:D,currentPage:t,onPageChange:s})]})]}),e.jsx(Cs,{}),h&&v&&e.jsx(pt,{open:h,onOpenChange:y,item:v,customizations:A}),g&&x&&e.jsx(ft,{itemsBuffet:x,open:g,onOpenChange:w,onItemsChange:async R=>{await o({...v,extra_data:{is_buffet_item:c?1:0,exclude_items_buffet:R}})},items:b,hide:!1,enable:c,onEnableChange:S})]})}function Ss(){return e.jsx(mt,{children:e.jsx(ks,{})})}const $a=Ss;export{$a as component};
