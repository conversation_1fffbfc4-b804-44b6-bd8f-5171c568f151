import{j as e,r as D,h as M,R as N,B as T}from"./index-DZ2N7iEN.js";import{u as L,a as B,o as K,b as H,c as $}from"./use-order-sources-V64JNK6l.js";import{H as U}from"./header-DUOMiURq.js";import{M as q}from"./main-DEy6aM-s.js";import{P as G}from"./profile-dropdown-CYtncOP3.js";import{S as z,T as Q}from"./search-BE7sNBUd.js";import"./pos-api-PZMeNc3U.js";import{u as V}from"./use-stores-DvOEKdtr.js";import"./vietqr-api-y35earyI.js";import"./user-B3TbfMPQ.js";import"./crm-api-DyqsDFNF.js";import"./date-range-picker-extpnOqj.js";import"./form-CUvXDg29.js";import{I as W}from"./input-DpObCffE.js";import{S as X,a as Y,b as Z,c as J,d as O}from"./select-B60YVMMU.js";import{D as R,a as ee,b as te,c as se}from"./dropdown-menu-Ci4yPenc.js";import{S as w}from"./skeleton-B7FDKZaJ.js";import{P as ne}from"./modal-C3wc7cQn.js";import{S as re}from"./search-Cr9zvKK2.js";import{E as _}from"./ellipsis-BF2o96mX.js";import{P as ae}from"./plus-UCXJIK3L.js";import{P as oe}from"./pagination-1m7XlFsy.js";import{b,e as j,a as ie,c as S,T as k,d as P}from"./table-DQgmazAN.js";import{C as le}from"./index-DkdBtE-t.js";import{u as ce}from"./useQuery-CCx3k6lE.js";import{u as de}from"./use-pos-data-DvaAesJs.js";import{D as me,a as he,b as xe,c as ue}from"./dialog-DDb7K8l1.js";import{T as pe}from"./trash-2-BhgXHZOc.js";import{T as ge}from"./table-skeleton-DNeZ3KTb.js";import"./useMutation-zADCXE7W.js";import"./utils-km2FGkQ4.js";import"./separator-Bnr4MN7f.js";import"./avatar-C7msROEs.js";import"./search-context-bUKMT4ET.js";import"./command-By9h7q-C.js";import"./calendar-CoHe9sRq.js";import"./createLucideIcon-2r9cCEY3.js";import"./index-CC24pdSB.js";import"./isSameMonth-C8JQo-AN.js";import"./createReactComponent-CQMxkbLi.js";import"./scroll-area-CoEnZUVR.js";import"./index-Csh0LmDQ.js";import"./IconChevronRight-Cvwf7k8w.js";import"./IconSearch-Do03nWQy.js";import"./stores-api-EIS-KrB6.js";import"./query-keys-3lmd-xp6.js";import"./chevron-right-D-SI6PAy.js";import"./react-icons.esm-Ck8h5xF8.js";import"./popover-BK8t3srL.js";import"./index-DIwSqRjm.js";import"./check-CP51yA4o.js";import"./index-DeeqTHK3.js";import"./use-auth-qNRX4JC8.js";const je=["ZALO","FACEBOOK","CRM"],fe=8,Se=4,E="text/plain",C={title:"Sắp xếp nguồn hàng",confirmText:"Lưu",maxWidth:"sm:max-w-4xl",description:"Thứ tự hiển thị các nguồn đơn sẽ được áp dụng tại thiết bị bán hàng"},I={loadError:"Có lỗi xảy ra khi tải danh sách nguồn đơn hàng",noSources:"Không có nguồn đơn hàng nào để sắp xếp"},p={description:"text-muted-foreground text-sm",errorText:"text-sm text-red-600",emptyState:"py-8 text-center",grid:`grid grid-cols-${Se} gap-3`,draggableItem:"flex aspect-square cursor-move items-center justify-center bg-slate-300 p-2 transition-colors select-none hover:bg-slate-400",itemText:"text-center text-sm font-medium",skeletonContainer:"aspect-square bg-slate-300 p-2"};function Ne(){return e.jsx("div",{className:p.grid,children:Array.from({length:fe}).map((t,s)=>e.jsx("div",{className:p.skeletonContainer,children:e.jsx(w,{className:"h-4 w-full"})},s))})}function De(){return e.jsx("div",{className:p.emptyState,children:e.jsx("p",{className:p.errorText,children:I.loadError})})}function ve(){return e.jsx("div",{className:p.emptyState,children:e.jsx("p",{className:p.description,children:I.noSources})})}function Ce({source:t,index:s,onDragStart:n,onDragOver:i,onDrop:c}){return e.jsx("div",{draggable:!0,onDragStart:a=>n(a,s),onDragOver:i,onDrop:a=>c(a,s),className:p.draggableItem,children:e.jsx("div",{className:p.itemText,children:t.source_name})},t.id)}function Te(t){return D.useMemo(()=>{if(!(t!=null&&t.data))return[];const s=n=>n.deleted_at===null&&!je.includes(n.source_name);return t.data.filter(s).sort((n,i)=>n.sort-i.sort)},[t==null?void 0:t.data])}function be(t){const[s,n]=D.useState([]);return D.useEffect(()=>{n(t)},[t]),{sortedSources:s,handleDragStart:(r,d)=>{r.dataTransfer.setData(E,d.toString())},handleDragOver:r=>{r.preventDefault()},handleDrop:(r,d)=>{r.preventDefault();const o=parseInt(r.dataTransfer.getData(E));if(o===d)return;const m=[...s],h=m[o];m.splice(o,1),m.splice(d,0,h),n(m)}}}function ye({open:t,onOpenChange:s}){const{data:n,isLoading:i,error:c}=L(),{updateSort:a,isUpdating:r}=B(),d=Te(n),{sortedSources:o,handleDragStart:m,handleDragOver:h,handleDrop:l}=be(d),x=i||r,f=o.length===0,u=()=>{const v=o.map((y,F)=>({source_id:y.source_id,sort:F}));a(v,{onSuccess:()=>s(!1)})},g=()=>{s(!1)},A=()=>i?e.jsx(Ne,{}):c?e.jsx(De,{}):f?e.jsx(ve,{}):e.jsx("div",{className:p.grid,children:o.map((v,y)=>e.jsx(Ce,{source:v,index:y,onDragStart:m,onDragOver:h,onDrop:l},v.id))});return e.jsxs(ne,{open:t,onOpenChange:s,title:C.title,onCancel:g,onConfirm:u,confirmText:C.confirmText,hideButtons:!1,confirmDisabled:x,isLoading:r,disableCancelButton:!0,maxWidth:C.maxWidth,children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:p.description,children:C.description})}),e.jsx("div",{children:A()})]})}function we({searchInput:t,storeFilter:s,onSearchInputChange:n,onSearchKeyPress:i,onStoreFilterChange:c}){const a=M(),{data:r=[]}=V(),[d,o]=N.useState(!1),m=()=>{a({to:"/setting/source/detail"})},h=()=>{o(!0)};return e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx("div",{className:"flex flex-col",children:e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:"Danh sách nguồn"})}),e.jsxs("div",{className:"relative max-w-sm flex-1",children:[e.jsx(re,{className:"text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2"}),e.jsx(W,{placeholder:"Tìm kiếm nhà hàng (nhấn Enter để tìm)",value:t,onChange:n,onKeyDown:i,className:"pl-9"})]}),e.jsxs(X,{value:s,onValueChange:c,children:[e.jsx(Y,{className:"w-[200px]",children:e.jsx(Z,{placeholder:"Tất cả các điểm"})}),e.jsxs(J,{children:[e.jsx(O,{value:"all",children:"Tất cả các điểm"}),r.map(l=>e.jsx(O,{value:l.id,children:l.name},l.id))]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(R,{children:[e.jsx(ee,{asChild:!0,children:e.jsxs(T,{variant:"outline",children:[e.jsx(_,{className:"mr-2 h-4 w-4"}),"Tiện ích"]})}),e.jsx(te,{align:"end",children:e.jsx(se,{onClick:h,children:"Sắp xếp nguồn đơn"})})]}),e.jsxs(T,{onClick:m,children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Thêm nguồn hàng"]})]}),e.jsx(ye,{open:d,onOpenChange:o})]})}function Oe(){return e.jsx(b,{children:e.jsx(j,{colSpan:5,className:"h-24 text-center",children:e.jsx("div",{className:"text-muted-foreground",children:"Không tìm thấy nguồn đơn hàng nào"})})})}function Ee(){return e.jsx(ie,{children:e.jsxs(b,{children:[e.jsx(S,{className:"w-[60px] text-center",children:"#"}),e.jsx(S,{className:"min-w-[150px]",children:"Mã nguồn đơn"}),e.jsx(S,{className:"min-w-[200px]",children:"Tên"}),e.jsx(S,{className:"min-w-[150px]",children:"Áp dụng tại"}),e.jsx(S,{className:"w-[50px]"})]})})}function Me({open:t,onOpenChange:s,orderSourceId:n,orderSourceName:i}){var h;const{getCityById:c}=de(),{data:a,isLoading:r,error:d}=ce({queryKey:["order-source-detail",n],queryFn:async()=>await K.getOrderSourceDetail(n),enabled:t&&!!n,staleTime:5*60*1e3}),o=((h=a==null?void 0:a.data)==null?void 0:h.stores)||[],m=l=>{const x=c(l);return(x==null?void 0:x.city_name)||l};return e.jsx(me,{open:t,onOpenChange:s,children:e.jsxs(he,{className:"max-w-2xl",children:[e.jsx(xe,{children:e.jsx(ue,{children:"Danh sách cửa hàng áp dụng"})}),e.jsx("div",{className:"mt-4",children:r?e.jsx("div",{className:"space-y-3",children:Array.from({length:3}).map((l,x)=>e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(w,{className:"h-4 w-[200px]"}),e.jsx(w,{className:"h-4 w-[150px]"})]},x))}):d?e.jsx("p",{className:"text-sm text-red-600",children:"Có lỗi xảy ra khi tải danh sách cửa hàng"}):o.length===0?e.jsx("p",{className:"text-muted-foreground py-8 text-center text-sm",children:"Không có cửa hàng nào áp dụng nguồn đơn hàng này"}):e.jsx("div",{className:"rounded-md border",children:e.jsx(k,{children:e.jsx(P,{children:o.map(l=>e.jsxs(b,{children:[e.jsx(j,{className:"font-medium",children:l.store_name}),e.jsx(j,{className:"font-mono text-sm",children:m(l.city_uid)})]},l.id))})})})})]})})}function _e({source:t,index:s}){const n=M(),[i,c]=D.useState(!1),[a,r]=D.useState(!1),{deleteOrderSource:d,isDeleting:o}=H(),m=()=>{d({id:t.id},{onSuccess:()=>{c(!1)}})},h=u=>u===null||u===0?"0 cửa hàng":`${u} cửa hàng`,l=u=>{u.stopPropagation(),r(!0)},x=()=>{n({to:`/setting/source/detail/${t.id}`})},f=u=>{u.stopPropagation(),c(!0)};return e.jsxs(e.Fragment,{children:[e.jsxs(b,{className:"hover:bg-muted/50 cursor-pointer",onClick:x,children:[e.jsx(j,{className:"text-center font-medium",children:s}),e.jsx(j,{className:"font-medium",children:t.source_id}),e.jsx(j,{children:t.source_name}),e.jsx(j,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{className:"text-muted-foreground hover:text-primary cursor-pointer text-sm underline-offset-4 hover:underline",onClick:l,disabled:!t.stores||t.stores===0,children:h(t.stores)}),t.stores&&t.stores>0&&e.jsx(T,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:l,children:e.jsx(_,{className:"text-muted-foreground h-4 w-4"})})]})}),e.jsx(j,{children:e.jsx(T,{variant:"ghost",size:"sm",className:"text-muted-foreground hover:text-destructive h-8 w-8 p-0",onClick:f,children:e.jsx(pe,{className:"h-4 w-4"})})})]}),e.jsx(le,{open:i,onOpenChange:c,title:"Xác nhận xóa",content:`Bạn có chắc chắn muốn xóa nguồn đơn hàng "${t.source_name}"?`,confirmText:"Xóa",cancelText:"Hủy",onConfirm:m,isLoading:o}),e.jsx(Me,{open:a,onOpenChange:r,orderSourceId:t.id,orderSourceName:t.source_name})]})}function ke({orderSources:t,isLoading:s,error:n,currentPage:i,onPageChange:c}){return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"rounded-md border",children:e.jsxs(k,{children:[e.jsx(Ee,{}),e.jsxs(P,{children:[s&&e.jsx(ge,{}),!s&&n&&e.jsx("tr",{children:e.jsx("td",{colSpan:5,className:"h-24 text-center",children:e.jsx("div",{className:"text-muted-foreground",children:"Có lỗi xảy ra khi tải dữ liệu"})})}),!s&&!n&&t.length===0&&e.jsx(Oe,{}),!s&&!n&&t.length>0&&t.map((a,r)=>e.jsx(_e,{source:a,index:r+1+(i-1)*10},a.id))]})]})}),t.length>0&&e.jsx(oe,{currentPage:i,totalPages:Math.ceil(t.length/10),pageSize:10,totalItems:t.length,onPageChange:c})]})}function Pe(){const[t,s]=N.useState(""),[n,i]=N.useState(""),[c,a]=N.useState("all"),[r,d]=N.useState(1),{data:o,isLoading:m,error:h}=$(r,n,c),l=(o==null?void 0:o.data)||[],x=g=>{g.key==="Enter"&&(i(t.trim()),d(1))},f=g=>{s(g.target.value),g.target.value.trim()===""&&(i(""),d(1))},u=g=>{a(g),d(1)};return e.jsxs(e.Fragment,{children:[e.jsx(U,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(z,{}),e.jsx(Q,{}),e.jsx(G,{})]})}),e.jsx(q,{children:e.jsxs("div",{className:"container mx-auto space-y-6 py-6",children:[e.jsx(we,{searchInput:t,storeFilter:c,onSearchInputChange:f,onSearchKeyPress:x,onStoreFilterChange:u}),e.jsx(ke,{orderSources:l,isLoading:m,error:h,currentPage:r,onPageChange:d})]})})]})}const Pt=Pe;export{Pt as component};
