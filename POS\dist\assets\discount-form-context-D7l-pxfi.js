import{b as r}from"./pos-api-PZMeNc3U.js";import{j as g,r as _}from"./index-DZ2N7iEN.js";const i=new Map,d=new Map,h=5*60*1e3,D={getDiscountPayments:async t=>{const o=`${t.company_uid}-${t.brand_uid}-${t.page||1}-${t.list_store_uid||"all"}-${t.active??"all"}`,e=i.get(o);if(e&&Date.now()-e.timestamp<h)return e.data;const a=d.get(o);if(a)return a;const s=(async()=>{var y,c,u;try{const n=new URLSearchParams;n.set("company_uid",t.company_uid),n.set("brand_uid",t.brand_uid),t.page&&n.set("page",t.page.toString()),t.list_store_uid&&n.set("list_store_uid",t.list_store_uid),t.active!==void 0&&n.set("active",t.active.toString()),t.limit&&n.set("limit",t.limit.toString());const l=await r.get(`/mdata/v1/discount-payment?${n.toString()}`);if(!l.data||typeof l.data!="object")throw new Error("Invalid response format from discount payment API");const p=l.data;return i.set(o,{data:p,timestamp:Date.now()}),p}catch(n){throw console.error("Error fetching discount payments:",n),((y=n.response)==null?void 0:y.status)===429?new Error("Too many requests - please wait a moment before trying again."):((c=n.response)==null?void 0:c.status)===401?new Error("Unauthorized - please check your authentication."):((u=n.response)==null?void 0:u.status)===403?new Error("Forbidden - you do not have permission to access this resource."):n}finally{d.delete(o)}})();return d.set(o,s),s},updateDiscountPayment:async t=>{try{await r.put("/mdata/v1/discount-payment",t),i.clear()}catch(o){throw console.error("Error updating discount payment:",o),o}},deleteDiscountPayment:async t=>{try{const o=new URLSearchParams;o.set("company_uid",t.company_uid),o.set("brand_uid",t.brand_uid),o.set("id",t.id),await r.delete(`/mdata/v1/discount-payment?${o.toString()}`),i.clear()}catch(o){throw console.error("Error deleting discount payment:",o),o}},getDiscountPaymentPrograms:async t=>{var o;try{const e=new URLSearchParams;return e.set("skip_limit","true"),e.set("company_uid",t.company_uid),e.set("brand_uid",t.brand_uid),e.set("store_uid",t.store_uid),((o=(await r.get(`/mdata/v1/discount-payment?${e.toString()}`)).data)==null?void 0:o.data)||[]}catch(e){throw console.error("Error fetching discount payment programs:",e),e}},cloneDiscountPayments:async t=>{try{const o={company_uid:t.company_uid,brand_uid:t.brand_uid,list_discount_payment_uid:t.list_discount_payment_uid,store_uid_root:t.store_uid_root,store_uid_target:t.store_uid_target};return(await r.post("/mdata/v1/discount-payment/clone",o)).data}catch(o){throw console.error("Error cloning discount payments:",o),o}},getPaymentDiscountPromotions:async t=>{var o;try{const a=`/mdata/v1/promotions?${new URLSearchParams({skip_limit:"true",company_uid:t.companyUid,brand_uid:t.brandUid,store_uid:t.storeUid,partner_auto_gen:"0",active:"1"}).toString()}`;console.log("🔥 Payment Discount Promotions API Call:",a),console.log("🔥 Params:",t);const s=await r.get(a);console.log("🔥 Payment Discount Promotions Response:",s.data);const c=(Array.isArray((o=s.data)==null?void 0:o.data)?s.data.data:[]).flatMap(u=>Array.isArray(u.list_data)?u.list_data.map(n=>({promotion_uid:n.id,promotion_name:n.promotion_name})):[]);return console.log("🔥 Parsed Payment Discount Promotions:",c),c}catch(e){return console.error("Error fetching payment discount promotions:",e),[]}},getDiscountPaymentById:async t=>{var o;try{const e=new URLSearchParams;return e.set("company_uid",t.company_uid),e.set("brand_uid",t.brand_uid),e.set("id",t.id),((o=(await r.get(`/mdata/v1/discount-payment?${e.toString()}`)).data)==null?void 0:o.data)||null}catch(e){throw console.error("Error fetching discount payment by ID:",e),e}},createPaymentDiscount:async t=>{try{await r.post("/mdata/v1/discount-payment",t),i.clear()}catch(o){throw console.error("Error creating payment discount:",o),o}},clearCache:()=>{i.clear(),d.clear()}},P=_.createContext(void 0);function v({children:t,value:o}){return g.jsx(P.Provider,{value:o,children:t})}function m(){const t=_.useContext(P);if(t===void 0)throw new Error("usePaymentDiscountFormContext must be used within a PaymentDiscountFormProvider");return t}function b(){const{formData:t,updateFormData:o}=m();return{formData:t,updateFormData:o}}function E(){const{handleBack:t,handleSave:o}=m();return{handleBack:t,handleSave:o}}function S(){const{isFormValid:t,isLoading:o,isEditMode:e}=m();return{isFormValid:t,isLoading:o,isEditMode:e}}function q(){const{promotions:t,isLoadingPromotions:o}=m();return{promotions:t,isLoadingPromotions:o}}export{v as P,S as a,b,q as c,D as d,E as u};
