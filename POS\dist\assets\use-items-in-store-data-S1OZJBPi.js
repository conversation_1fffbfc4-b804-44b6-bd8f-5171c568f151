import{u as y}from"./useQuery-Ck3BpOfq.js";import"./user-CN3Oxfhq.js";import{u as x,l as N}from"./index-D0Grd55b.js";import{Q as g}from"./query-keys-3lmd-xp6.js";import"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import{u as E}from"./use-item-types-CuyGAtwR.js";import{u as P}from"./use-item-classes-s7fFgXey.js";import{u as b}from"./use-units-ByB1meVo.js";import{u as v}from"./use-removed-items-7Ty-LinJ.js";import"./crm-api-Bz_HnEz3.js";import{i as f}from"./items-in-store-api-BAXUzzVL.js";const C=(r={})=>{const{params:e={},enabled:_=!0}=r,{company:s}=x(u=>u.auth),{selectedBrand:n}=N(),o={company_uid:(s==null?void 0:s.id)||"",brand_uid:(n==null?void 0:n.id)||"",page:1,reverse:1,...e,apply_with_store:e.apply_with_store},d=!!(s!=null&&s.id&&(n!=null&&n.id)),i=y({queryKey:[g.ITEMS_IN_STORE_LIST,JSON.stringify(o),typeof e.apply_with_store>"u"?"no-apply-with-store":String(e.apply_with_store)],queryFn:async()=>(await f.getItemsInStore(o)).data||[],enabled:_&&d,staleTime:5*60*1e3,refetchInterval:10*60*1e3}),a={...o,page:(o.page||1)+1},t=y({queryKey:[g.ITEMS_IN_STORE_LIST,"next",JSON.stringify(a)],queryFn:async()=>(await f.getItemsInStore(a)).data||[],enabled:_&&d&&(i.data?i.data.length>0:!1),staleTime:2*60*1e3,gcTime:5*60*1e3}),p=o.limit||50,c=(t.data?t.data.length>0:!1)||(i.data?i.data.length===p:!1);return{data:i.data,isLoading:i.isLoading,error:i.error,refetch:i.refetch,isFetching:i.isFetching,nextPageData:t.data||[],hasNextPage:c}},U=(r,e=!0)=>y({queryKey:[g.ITEMS_IN_STORE_DETAIL,r],queryFn:()=>f.getItemById({id:r}),enabled:e&&!!r,staleTime:5*60*1e3}),Y=(r={})=>{var i;const e=C(r),{data:_=[]}=E({skip_limit:!0}),{data:s=[]}=P({skip_limit:!0}),{data:n=[]}=b(),{data:o=[]}=v();return{data:((i=e.data)==null?void 0:i.map(a=>{var I,S;const t=a,p=((I=o.find(m=>m.id===t.city_uid))==null?void 0:I.city_name)||"",c=t.item_type_uid?_.find(m=>m.id===t.item_type_uid):null,u=(c==null?void 0:c.item_type_name)||"LOẠI KHÁC",h=t.item_class_uid?s.find(m=>m.id===t.item_class_uid):null,D=(h==null?void 0:h.item_class_name)||"",l=t.unit_uid?n.find(m=>m.id===t.unit_uid):null,T=(l==null?void 0:l.unit_name)||"",w=t.is_eat_with===1||t.item_id_eat_with&&t.item_id_eat_with!==""?t.item_id_eat_with||"Món ăn kèm":"";return{...a,code:t.item_id||"",name:a.item_name,price:a.ots_price,vatPercent:a.ots_tax,cookingTime:a.time_cooking,categoryGroup:u,itemType:u,itemClass:D,unit:T,sideItems:w||void 0,city:p,buffetConfig:((S=t.extra_data)==null?void 0:S.is_buffet_item)===1?"Đã cấu hình":"Chưa cấu hình",customization:t.customization_uid||void 0,applyWithStore:t.apply_with_store,isActive:!!a.active,createdAt:typeof a.created_at=="number"?new Date(a.created_at*1e3):new Date(new Date(a.created_at).getTime())}}))||[],isLoading:e.isLoading,error:e.error,refetch:e.refetch,isFetching:e.isFetching,nextPageData:e.nextPageData,hasNextPage:e.hasNextPage}};export{C as a,U as b,Y as u};
