import{r as C,j as e,z as l,B as E}from"./index-D0Grd55b.js";import{M as U}from"./main-Czv3HpP4.js";import{C as z,a as q,d as X}from"./card-Z5f_tC2-.js";import{s as Q,t as J,w as W,x as Y}from"./date-range-picker-CruKYeHR.js";import{a as m,b as j,c as w,d as v,e as P,L as N,u as Z,F as T}from"./form-Bk1C9kLO.js";import{T as ee,a as ne,b as I,c as y,d as te,e as _}from"./table-CLNYB6yq.js";import{u as ae,a as $,b as O,c as oe}from"./use-membership-type-CUbAO9OF.js";import{c as G}from"./createLucideIcon-DNzDbUBG.js";import{s as re}from"./zod-G2vIgQkk.js";import{u as se}from"./use-pos-company-data-0WFdkaVk.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import"./pos-api-B09qRspF.js";import{C as B}from"./checkbox-C_eqgJmW.js";import{S as de}from"./switch-C4OYPF8n.js";import{I as h}from"./input-C-0UnKOB.js";import{R as M,a as x}from"./radio-group-CpEBFN0-.js";import{S as ie,a as ce,b as le,c as ue,d as ge}from"./select-DBO-8fSu.js";import"./calendar-5lpy20z0.js";import"./index-CW7Xpojs.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-D2H_xC9V.js";import"./react-icons.esm-BOnXQgjl.js";import"./popover-CMTiAV3j.js";import"./useQuery-Ck3BpOfq.js";import"./utils-km2FGkQ4.js";import"./useMutation-ATsU-ht7.js";import"./query-keys-DQo7uRnN.js";import"./index-C-UyCxtf.js";import"./check-TFQPNqMS.js";import"./index-DxQNaO1C.js";import"./index-CI2TkimM.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xe=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]],he=G("circle-plus",xe);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]],je=G("pencil",me),K=C.createContext(void 0);function ve({children:o}){var A;const d=ae(),n=$(),i=O(),[c,a]=C.useState(!1),[r,s]=C.useState(!1),[k,S]=C.useState(null),V=async(D,p)=>{var f;const R=(((f=d.data)==null?void 0:f.list_membership_type)||[]).find(u=>u.type_id===D);if(!R)return;const t={...R,active:p===1?0:1,updated_at:new Date().toISOString().replace("T"," ").slice(0,19)};await n.mutateAsync(t)},H={data:((A=d.data)==null?void 0:A.list_membership_type)||[],isLoading:d.isLoading,error:d.error,refetch:d.refetch,toggleActivation:V,createDialogOpen:c,setCreateDialogOpen:a,editDialogOpen:r,setEditDialogOpen:s,selectedMembershipType:k,setSelectedMembershipType:S,updateMembershipTypeMutation:n,createMembershipTypeMutation:i};return e.jsx(K.Provider,{value:H,children:o})}function F(){const o=C.useContext(K);if(o===void 0)throw new Error("useMembershipTypeContext must be used within a MembershipTypeProvider");return o}const Ne=o=>`${o.toLocaleString("vi-VN")} VND`,fe=o=>`${(o*100).toFixed(6)}%`,ye=(o,d)=>{if(d===1)return"";const n=typeof o=="string"?parseFloat(o)||0:o;return n===0?"Thành viên đăng ký lần đầu":`Tiền tích lũy vượt ${Ne(n)}`},_e=o=>{if(o.upgrade_amount&&o.downgrade_amount&&o.downgrade_to_level){const d=o.downgrade_to_level,n=`Định mức chi tiêu xét lại hạng: ${o.downgrade_amount}`,i=`Nếu không đạt: Hạ về hạng ${d}`;return`${n}
${i}`}return""},pe=(o,d)=>{let i=(c=>{const a=new Date(c);return`Ngày tạo: ${a.toLocaleDateString("vi-VN")} ${a.toLocaleTimeString("vi-VN",{hour:"2-digit",minute:"2-digit",second:"2-digit"})}`})(o);return d&&d!==o&&(i+=`
Ngày cập nhật: ${d}`),i},we=o=>o===1?{text:"Đang hoạt động",className:"bg-green-100 text-green-800"}:{text:"Đã hủy",className:"bg-red-100 text-red-800"};function Ce(){const{data:o,isLoading:d,error:n,toggleActivation:i,setEditDialogOpen:c,setSelectedMembershipType:a}=F();return d?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-muted-foreground",children:"Đang tải..."})}):n?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-red-500",children:"Có lỗi xảy ra khi tải dữ liệu"})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"rounded-md border",children:e.jsxs(ee,{children:[e.jsx(ne,{children:e.jsxs(I,{children:[e.jsx(y,{children:"ID"}),e.jsx(y,{children:"Hạng thành viên"}),e.jsx(y,{children:"Tích điểm"}),e.jsx(y,{children:"Điều kiện đạt hạng"}),e.jsx(y,{children:"Xét lại hạng theo chu kỳ"}),e.jsx(y,{children:"Thời gian"}),e.jsx(y,{children:"Trạng thái"}),e.jsx(y,{})]})}),e.jsx(te,{children:o.map(r=>{const s=we((r==null?void 0:r.active)||0);return e.jsxs(I,{className:"hover:bg-muted/50 group",children:[e.jsx(_,{children:r.type_id}),e.jsx(_,{children:r.type_name}),e.jsx(_,{children:fe((r==null?void 0:r.point_rate)||0)}),e.jsx(_,{children:ye((r==null?void 0:r.upgrade_amount)||0,r==null?void 0:r.is_no_change)}),e.jsx(_,{className:"whitespace-pre-line",children:_e(r)}),e.jsx(_,{children:e.jsx("div",{className:"text-xs whitespace-pre-line text-gray-600",children:pe((r==null?void 0:r.created_at)||"",(r==null?void 0:r.updated_at)||"")})}),e.jsx(_,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${s.className}`,children:s.text}),e.jsx("button",{className:"ml-2 text-xs text-blue-500 opacity-0 transition-opacity group-hover:opacity-100",onClick:()=>i((r==null?void 0:r.type_id)||"",(r==null?void 0:r.active)||0),children:(r==null?void 0:r.active)===1?"Hủy":"Kích hoạt"})]})}),e.jsx(_,{children:e.jsx(je,{className:"h-5 w-5 cursor-pointer text-gray-400 hover:text-gray-600",onClick:()=>{a(r),c(!0)}})})]},r.id)})})]})}),o.length===0&&e.jsx("div",{className:"text-muted-foreground py-8 text-center",children:"Không có dữ liệu"})]})}function Me({form:o,editMode:d}){const{watch:n}=o,i=n("pointRate"),c=i&&i>100;return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"HẠNG THÀNH VIÊN"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[e.jsx(m,{control:o.control,name:"typeId",render:({field:a})=>e.jsxs(j,{children:[e.jsx(w,{children:"ID *"}),e.jsx(v,{children:e.jsx(h,{placeholder:"Nhập ID hạng thành viên",disabled:d,...a})}),e.jsx(P,{})]})}),e.jsx(m,{control:o.control,name:"typeName",render:({field:a})=>e.jsxs(j,{children:[e.jsx(w,{children:"Hạng thành viên *"}),e.jsx(v,{children:e.jsx(h,{placeholder:"Nhập tên hạng thành viên",...a})}),e.jsx(P,{})]})}),e.jsx(m,{control:o.control,name:"pointRate",render:({field:a})=>e.jsxs(j,{children:[e.jsx(w,{children:"Tích điểm *"}),e.jsx(v,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(h,{placeholder:"0.01",type:"number",value:a.value||"",onChange:r=>a.onChange(r.target.value?Number(r.target.value):void 0),className:`w-32 ${c?"border-red-500 focus:border-red-500 focus:ring-red-500":""}`}),e.jsx("span",{className:"rounded bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"% giá trị hóa đơn"}),e.jsx("a",{href:"/crm/marketing/create-evoucher",target:"_blank",rel:"noopener noreferrer",className:"ring-offset-background focus-visible:ring-ring inline-flex h-9 items-center justify-center rounded-md border border-orange-200 bg-orange-50 px-3 text-sm font-medium text-orange-600 transition-colors hover:bg-orange-100 focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50",children:"Tạo voucher đổi điểm"})]})}),e.jsx("div",{className:"text-sm",children:e.jsx("a",{href:"#",className:"text-blue-600 hover:underline",children:"Cài đặt làm tròn điểm tích lũy tại đây"})}),e.jsx(P,{})]})}),e.jsx(m,{control:o.control,name:"isNoChange",render:({field:a})=>e.jsxs(j,{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(v,{children:e.jsx(B,{checked:a.value,onCheckedChange:a.onChange})}),e.jsx(w,{children:"Hạng không đổi"})]}),e.jsx("div",{className:"rounded bg-gray-50 p-3 text-sm text-gray-600",children:"Hạng không đổi sẽ không ảnh hưởng bởi các điều kiện hạng thành viên thông thường, không xét lại theo chu kỳ"})]})})]})]})}function Ae({form:o}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"ĐIỀU KIỆN ĐẠT HẠNG"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(m,{control:o.control,name:"upgradeAmount",render:({field:d})=>e.jsxs(j,{children:[e.jsx(w,{children:"Tiền tích lũy vượt mức *"}),e.jsx(v,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(h,{placeholder:"0",...d}),e.jsx("span",{className:"text-sm text-gray-500",children:"VNĐ"})]})}),e.jsx(P,{})]})}),e.jsx(m,{control:o.control,name:"moneyHandlingType",render:({field:d})=>e.jsx(j,{className:"space-y-3",children:e.jsx(v,{children:e.jsxs(M,{onValueChange:d.onChange,defaultValue:d.value,className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"maintain",id:"maintain"}),e.jsx("label",{htmlFor:"maintain",className:"cursor-pointer text-sm text-gray-700",children:"Duy trì tiền tích lũy"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"deduct",id:"deduct"}),e.jsx(m,{control:o.control,name:"upgradeMinusPointAmount",render:({field:n})=>e.jsx(j,{children:e.jsx(v,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"Trừ tiền tích lũy"}),e.jsx(h,{placeholder:"0",className:"w-24 rounded-none border-r-0 border-l-0",...n}),e.jsx("div",{className:"rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"VNĐ"})]})})})})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"reset",id:"reset"}),e.jsx(m,{control:o.control,name:"upgradeResetPointAmount",render:({field:n})=>e.jsx(j,{children:e.jsx(v,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"Reset tiền tích lũy về"}),e.jsx(h,{placeholder:"0",className:"w-24 rounded-none border-r-0 border-l-0",...n}),e.jsx("div",{className:"rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"VNĐ"})]})})})})]})]})})})})]})]})}function be({form:o}){const{register:d,watch:n,setValue:i}=o,{data:c}=F(),a=n("rankChangeType"),r=n("moneyChangeType");return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(N,{htmlFor:"downgradeAmount",children:"Định mức chi tiêu xét lại hạng *"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(h,{...d("downgradeAmount"),type:"text",id:"downgradeAmount",placeholder:"0",className:"flex-1"}),e.jsx("span",{className:"text-sm text-gray-500",children:"VNĐ"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-700",children:"Nếu không đạt định mức chi tiêu:"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(N,{children:"Thay đổi hạng:"}),e.jsxs(M,{value:a,onValueChange:s=>i("rankChangeType",s),className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"maintain",id:"rank-maintain"}),e.jsx(N,{htmlFor:"rank-maintain",className:"cursor-pointer",children:"Duy trì hạng"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"downgrade",id:"rank-downgrade"}),e.jsx(N,{htmlFor:"rank-downgrade",className:"cursor-pointer",children:"Hạ hạng về"}),e.jsxs(ie,{value:n("downgradeToLevel"),onValueChange:s=>i("downgradeToLevel",s),children:[e.jsx(ce,{className:"ml-2 w-32",children:e.jsx(le,{placeholder:"Chọn hạng mới"})}),e.jsx(ue,{children:c==null?void 0:c.map(s=>e.jsx(ge,{value:s.type_id||"",children:s.type_name},s.id))})]})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(N,{children:"Thay đổi tiền tích lũy:"}),e.jsxs(M,{value:r,onValueChange:s=>i("moneyChangeType",s),className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"maintain",id:"money-maintain"}),e.jsx(N,{htmlFor:"money-maintain",className:"cursor-pointer",children:"Duy trì tiền tích lũy"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"deduct",id:"money-deduct"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"Trừ tiền tích lũy"}),e.jsx(h,{...d("downgradeMinusPointAmount",{setValueAs:s=>s===""?void 0:Number(s)}),type:"number",placeholder:"0",className:"w-24 rounded-none border border-r-0 border-l-0"}),e.jsx("div",{className:"rounded-r-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"VNĐ"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"reset",id:"money-reset"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"Reset tiền tích lũy về"}),e.jsx(h,{...d("downgradeResetPointAmount",{setValueAs:s=>s===""?void 0:Number(s)}),type:"number",placeholder:"0",className:"w-24 rounded-none border border-r-0 border-l-0"}),e.jsx("div",{className:"rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"VNĐ"})]})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(N,{children:"Thay đổi điểm tích lũy:"}),e.jsxs(M,{value:n("pointChangeType"),onValueChange:s=>i("pointChangeType",s),className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"maintain",id:"points-maintain"}),e.jsx(N,{htmlFor:"points-maintain",className:"cursor-pointer",children:"Duy trì điểm tích lũy hiện tại"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"deduct",id:"points-deduct"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"Trừ điểm tích lũy"}),e.jsx(h,{...d("downgradeMinusPoint",{setValueAs:s=>s===""?void 0:Number(s)}),type:"number",placeholder:"0",className:"w-24 rounded-none border border-r-0 border-l-0"}),e.jsx("div",{className:"rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"Điểm"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"reset",id:"points-reset"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"Reset điểm tích lũy về"}),e.jsx(h,{...d("downgradeResetPoint",{setValueAs:s=>s===""?void 0:Number(s)}),type:"number",placeholder:"0",className:"w-24 rounded-none border border-r-0 border-l-0"}),e.jsx("div",{className:"rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"Điểm"})]})]})]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-700",children:"Nếu vượt quá định mức chi tiêu:"}),e.jsx("div",{className:"space-y-2",children:e.jsxs(M,{value:n("exceededMoneyChangeType"),onValueChange:s=>i("exceededMoneyChangeType",s),className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"maintain",id:"exceeded-money-maintain"}),e.jsx(N,{htmlFor:"exceeded-money-maintain",className:"cursor-pointer",children:"Duy trì tiền tích lũy"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"deduct",id:"exceeded-money-deduct"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"Trừ tiền tích lũy"}),e.jsx(h,{...d("unchangeMinusPointAmount",{setValueAs:s=>s===""?void 0:Number(s)}),type:"number",placeholder:"0",className:"w-24 rounded-none border border-r-0 border-l-0"}),e.jsx("div",{className:"rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"VNĐ"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{value:"reset",id:"exceeded-money-reset"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"rounded-l-md border border-r-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"Reset tiền tích lũy về"}),e.jsx(h,{...d("unchangeResetPointAmount",{setValueAs:s=>s===""?void 0:Number(s)}),type:"number",placeholder:"0",className:"w-24 rounded-none border border-r-0 border-l-0"}),e.jsx("div",{className:"rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700",children:"VNĐ"})]})]})]})})]})]})})}const Re=l.object({typeId:l.string().optional(),typeName:l.string().optional(),pointRate:l.number().optional(),isNoChange:l.boolean().optional(),upgradeAmount:l.string().optional(),moneyHandlingType:l.enum(["maintain","deduct","reset"]).optional(),upgradeMinusPointAmount:l.string().optional(),upgradeResetPointAmount:l.string().optional(),isRankReviewEnabled:l.boolean().optional(),downgradeAmount:l.string().optional(),rankChangeType:l.enum(["maintain","downgrade"]).optional(),downgradeToLevel:l.string().optional(),moneyChangeType:l.enum(["maintain","deduct","reset"]).optional(),downgradeMinusPointAmount:l.number().optional(),downgradeResetPointAmount:l.number().optional(),downgradeMinusPoint:l.number().optional(),downgradeResetPoint:l.number().optional(),unchangeMinusPointAmount:l.number().optional(),unchangeResetPointAmount:l.number().optional(),pointChangeType:l.enum(["maintain","deduct","reset"]).optional(),exceededMoneyChangeType:l.enum(["maintain","deduct","reset"]).optional(),isActive:l.boolean().optional()}),L={typeId:"",typeName:"",pointRate:0,isNoChange:!1,upgradeAmount:"",moneyHandlingType:"maintain",upgradeMinusPointAmount:"",upgradeResetPointAmount:"",isRankReviewEnabled:!1,downgradeAmount:"",rankChangeType:"maintain",downgradeToLevel:"",moneyChangeType:"maintain",downgradeMinusPointAmount:0,downgradeResetPointAmount:0,downgradeMinusPoint:0,downgradeResetPoint:0,unchangeMinusPointAmount:0,unchangeResetPointAmount:0,pointChangeType:"maintain",exceededMoneyChangeType:"maintain",isActive:!0},Pe=({open:o,onOpenChange:d,membershipType:n})=>{const i=!!n,c=Z({resolver:re(Re),mode:"onChange",defaultValues:L}),{setValue:a,watch:r,formState:{isSubmitting:s,errors:k}}=c,S=r("isRankReviewEnabled"),V=r("isNoChange"),H=Object.keys(k).length>0,A=$(),D=O(),p=se(),b=oe();C.useEffect(()=>{var t,f,u,g;i&&n?(a("typeId",n.type_id),a("typeName",n.type_name),a("pointRate",((n==null?void 0:n.point_rate)||0)*100),a("isNoChange",(n==null?void 0:n.is_no_change)===1),a("upgradeAmount",((t=n==null?void 0:n.upgrade_amount)==null?void 0:t.toString())||"0"),a("upgradeMinusPointAmount",((f=n==null?void 0:n.upgrade_minus_point_amount)==null?void 0:f.toString())||"0"),a("upgradeResetPointAmount",((u=n==null?void 0:n.upgrade_reset_point_amount)==null?void 0:u.toString())||"0"),a("downgradeAmount",((g=n==null?void 0:n.downgrade_amount)==null?void 0:g.toString())||""),a("downgradeToLevel",(n==null?void 0:n.downgrade_to_level)||""),a("downgradeMinusPointAmount",(n==null?void 0:n.downgrade_minus_point_amount)||0),a("downgradeResetPointAmount",(n==null?void 0:n.downgrade_reset_point_amount)||0),a("downgradeMinusPoint",(n==null?void 0:n.downgrade_minus_point)||0),a("downgradeResetPoint",(n==null?void 0:n.downgrade_reset_point)||0),a("unchangeMinusPointAmount",(n==null?void 0:n.unchange_minus_point_amount)||0),a("unchangeResetPointAmount",(n==null?void 0:n.unchange_reset_point_amount)||0),a("isActive",n.active===1),(n!=null&&n.downgrade_amount||n!=null&&n.downgrade_to_level||n!=null&&n.downgrade_minus_point_amount||n!=null&&n.downgrade_reset_point_amount||n!=null&&n.downgrade_minus_point||n!=null&&n.downgrade_reset_point||n!=null&&n.unchange_minus_point_amount||n!=null&&n.unchange_reset_point_amount)&&(a("isRankReviewEnabled",!0),a("rankChangeType","downgrade")),n!=null&&n.upgrade_minus_point_amount?a("moneyHandlingType","deduct"):n!=null&&n.upgrade_reset_point_amount?a("moneyHandlingType","reset"):a("moneyHandlingType","maintain"),n!=null&&n.downgrade_minus_point_amount?a("moneyChangeType","deduct"):n!=null&&n.downgrade_reset_point_amount?a("moneyChangeType","reset"):a("moneyChangeType","maintain"),n!=null&&n.downgrade_minus_point?a("pointChangeType","deduct"):n!=null&&n.downgrade_reset_point?a("pointChangeType","reset"):a("pointChangeType","maintain"),n!=null&&n.unchange_minus_point_amount?a("exceededMoneyChangeType","deduct"):n!=null&&n.unchange_reset_point_amount?a("exceededMoneyChangeType","reset"):a("exceededMoneyChangeType","maintain")):c.reset(L)},[i,n,o,a,c.reset]);const R=async t=>{var f;if(!(p!=null&&p.company_id)||!((f=b==null?void 0:b[0])!=null&&f.brand_id)){console.error("Missing required fields");return}try{if(i&&n){const g={id:n.id,type_id:t.typeId||"",company_id:p.company_id,type_name:t.typeName||"",point_rate:(t.pointRate||0)/100,active:t.isActive?1:0,is_no_change:t.isNoChange?1:0,created_at:n.created_at,updated_at:new Date().toISOString().replace("T"," ").slice(0,19),upgrade_amount:0};t.isNoChange||(g.upgrade_amount=t.upgradeAmount||"0",t.moneyHandlingType==="deduct"&&(g.upgrade_minus_point_amount=t.upgradeMinusPointAmount||"0"),t.moneyHandlingType==="reset"&&(g.upgrade_reset_point_amount=t.upgradeResetPointAmount||"0")),t.isNoChange||(t.downgradeAmount&&(g.downgrade_amount=Number(t.downgradeAmount)),t.downgradeToLevel&&(g.downgrade_to_level=t.downgradeToLevel),t.moneyChangeType==="deduct"&&(g.downgrade_minus_point_amount=Number(t.downgradeMinusPointAmount)),t.moneyChangeType==="reset"&&(g.downgrade_reset_point_amount=Number(t.downgradeResetPointAmount)),t.pointChangeType==="deduct"&&(g.downgrade_minus_point=t.downgradeMinusPoint),t.pointChangeType==="reset"&&(g.downgrade_reset_point=t.downgradeResetPoint),t.exceededMoneyChangeType==="deduct"&&(g.unchange_minus_point_amount=Number(t.unchangeMinusPointAmount)),t.exceededMoneyChangeType==="reset"&&(g.unchange_reset_point_amount=Number(t.unchangeResetPointAmount))),await A.mutateAsync(g),d(!1);return}const u={company_id:p.company_id,type_id:t.typeId||"",type_name:t.typeName||"",point_rate:(t.pointRate||0)/100,active:t.isActive?1:0,is_no_change:t.isNoChange?1:0};t.isNoChange||(u.upgrade_amount=t.upgradeAmount||"0",t.moneyHandlingType==="deduct"&&(u.upgrade_minus_point_amount=t.upgradeMinusPointAmount||"0"),t.moneyHandlingType==="reset"&&(u.upgrade_reset_point_amount=t.upgradeResetPointAmount||"0")),t.isNoChange||(t.downgradeAmount&&(u.downgrade_amount=Number(t.downgradeAmount)),t.downgradeToLevel&&(u.downgrade_to_level=t.downgradeToLevel),t.moneyChangeType==="deduct"&&(u.downgrade_minus_point_amount=Number(t.downgradeMinusPointAmount)),t.moneyChangeType==="reset"&&(u.downgrade_reset_point_amount=Number(t.downgradeResetPointAmount)),t.pointChangeType==="deduct"&&(u.downgrade_minus_point=t.downgradeMinusPoint),t.pointChangeType==="reset"&&(u.downgrade_reset_point=t.downgradeResetPoint),t.exceededMoneyChangeType==="deduct"&&(u.unchange_minus_point_amount=Number(t.unchangeMinusPointAmount)),t.exceededMoneyChangeType==="reset"&&(u.unchange_reset_point_amount=Number(t.unchangeResetPointAmount))),await D.mutateAsync(u),d(!1)}catch(u){console.error("Error updating membership type:",u)}};return e.jsx(Q,{open:o,onOpenChange:d,children:e.jsxs(J,{className:"h-full w-[800px] overflow-y-auto p-6",side:"right",children:[e.jsx(W,{className:"mb-6",children:e.jsx(Y,{children:i?"Sửa hạng thành viên":"Tạo hạng thành viên"})}),e.jsx(T,{...c,children:e.jsxs("form",{id:"membership-type-form",onSubmit:c.handleSubmit(R),className:"space-y-8",children:[e.jsx(Me,{form:c,editMode:i}),!V&&e.jsxs(e.Fragment,{children:[e.jsx(Ae,{form:c}),e.jsx(m,{control:c.control,name:"isRankReviewEnabled",render:({field:t})=>e.jsx(j,{className:"space-y-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(v,{children:e.jsx(B,{id:"review-rank",checked:t.value,onCheckedChange:t.onChange})}),e.jsx(w,{htmlFor:"review-rank",className:"cursor-pointer text-sm font-medium",children:"XÉT LẠI HẠNG THEO CHU KỲ"}),t.value&&e.jsx("span",{className:"text-sm",children:e.jsx("a",{href:"/crm/settings#loyalty",className:"text-blue-600 hover:underline",children:"Cài đặt chu kỳ tại đây"})})]})})}),S&&e.jsx(be,{form:c})]}),e.jsx(m,{control:c.control,name:"isActive",render:({field:t})=>e.jsxs(j,{className:"flex items-center justify-between",children:[e.jsx(w,{children:"Trạng thái"}),e.jsx(v,{children:e.jsx(de,{id:"status",checked:t.value,onCheckedChange:t.onChange})})]})}),e.jsx("div",{className:"flex justify-end pt-4",children:e.jsx(E,{type:"submit",disabled:s||H,children:s?i?"Đang cập nhật...":"Đang tạo...":i?"Cập nhật":"Tạo mới"})})]})})]})})};function ke(){const{editDialogOpen:o,setEditDialogOpen:d,selectedMembershipType:n,setSelectedMembershipType:i}=F(),c=()=>{i(null),d(!0)};return e.jsx(U,{children:e.jsxs("div",{className:"container mx-auto space-y-6 py-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Hạng thành viên"})}),e.jsxs(z,{children:[e.jsx(q,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsxs(E,{variant:"default",onClick:c,children:[e.jsx(he,{className:"mr-2 h-4 w-4"}),"Tạo mới"]})}),e.jsx(X,{children:e.jsx(Ce,{})})]}),e.jsx(Pe,{open:o,onOpenChange:d,membershipType:n||void 0})]})})}function Se(){return e.jsx(ve,{children:e.jsx(ke,{})})}const gn=Se;export{gn as component};
