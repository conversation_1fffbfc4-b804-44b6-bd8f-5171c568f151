import{r as v,h as ne,u as ae,a4 as w,j as e,B as b,c as G,T as Z,o as M,p as E,q as O,aA as re}from"./index-D0Grd55b.js";import"./date-range-picker-CruKYeHR.js";import{L as j}from"./form-Bk1C9kLO.js";import{D as ie,g as le,a as ce,b as de,c as oe}from"./dialog-C8IVKkOo.js";import{I as P}from"./input-C-0UnKOB.js";import{C as A}from"./checkbox-C_eqgJmW.js";import{P as he,a as me,b as ue}from"./popover-CMTiAV3j.js";import"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import{u as xe}from"./use-stores-ma88RbZs.js";import{c as ee}from"./use-order-sources-D7MJORwg.js";import{C as W,S as q,a as $,b as D,c as K,d as T}from"./select-DBO-8fSu.js";import{S as pe}from"./search-C4dfJjaw.js";import{a as te}from"./use-payment-methods-DDrQsH6X.js";import{C as R}from"./circle-help-CEmvpE3g.js";import{d as ge,e as ve,g as fe,f as je}from"./error-handler-DsIUL1Kn.js";import{X as ye}from"./calendar-5lpy20z0.js";const B=[{value:"monday",label:"Thứ 2",numericValue:4},{value:"tuesday",label:"Thứ 3",numericValue:8},{value:"wednesday",label:"Thứ 4",numericValue:16},{value:"thursday",label:"Thứ 5",numericValue:32},{value:"friday",label:"Thứ 6",numericValue:64},{value:"saturday",label:"Thứ 7",numericValue:128},{value:"sunday",label:"Chủ nhật",numericValue:256}],X=t=>t.reduce((s,i)=>{const a=B.find(r=>r.value===i);return s+((a==null?void 0:a.numericValue)||0)},0),Ne=t=>{const s=[],i=[...B].sort((r,n)=>n.numericValue-r.numericValue);let a=t;for(const r of i)a>=r.numericValue&&(s.push(r.value),a-=r.numericValue);return s.sort((r,n)=>{const h=B.findIndex(x=>x.value===r),m=B.findIndex(x=>x.value===n);return h-m})},H=[{value:"0h",numericValue:1},{value:"1h",numericValue:2},{value:"2h",numericValue:4},{value:"3h",numericValue:8},{value:"4h",numericValue:16},{value:"5h",numericValue:32},{value:"6h",numericValue:64},{value:"7h",numericValue:128},{value:"8h",numericValue:256},{value:"9h",numericValue:512},{value:"10h",numericValue:1024},{value:"11h",numericValue:2048},{value:"12h",numericValue:4096},{value:"13h",numericValue:8192},{value:"14h",numericValue:16384},{value:"15h",numericValue:32768},{value:"16h",numericValue:65536},{value:"17h",numericValue:131072},{value:"18h",numericValue:262144},{value:"19h",numericValue:524288},{value:"20h",numericValue:1048576},{value:"21h",numericValue:2097152},{value:"22h",numericValue:4194304},{value:"23h",numericValue:8388608}],Y=t=>t.reduce((s,i)=>{const a=H.find(r=>r.value===i);return s+((a==null?void 0:a.numericValue)||0)},0),_e=t=>{const s=[],i=[...H].sort((r,n)=>n.numericValue-r.numericValue);let a=t;for(const r of i)a>=r.numericValue&&(s.push(r.value),a-=r.numericValue);return s.sort((r,n)=>{const h=H.findIndex(x=>x.value===r),m=H.findIndex(x=>x.value===n);return h-m})},J={sourceName:[],description:"",commissionRate:0,excludeShipping:!1,paymentType:"",deductTaxRate:0,requireTransactionNumber:!1,useOnlineOrder:!1,paymentMethodId:"",voucherRunPartner:"",notShowPartnerBill:"0",requirePartnerInvoice:!1,selectedPaymentMethodId:"",marketingPartnerCost:0,voucherCostType:"",marketingStartDate:"",marketingEndDate:"",marketingDays:[],marketingHours:[],storeId:""},be=()=>{const[t,s]=v.useState(J),i=n=>{s(h=>{const m={...h,...n};return n.paymentMethodId!==void 0&&(n.paymentMethodId===""?(m.requirePartnerInvoice=!1,m.selectedPaymentMethodId=""):n.paymentMethodId==="prepaid"&&(m.selectedPaymentMethodId="")),n.storeId!==void 0&&n.storeId!==h.storeId&&(m.selectedPaymentMethodId=""),m})},a=()=>{s(J)},r=t.sourceName.length>0&&t.storeId!==""&&t.paymentMethodId!==""&&(t.paymentMethodId!=="postpaid"||t.selectedPaymentMethodId!=="");return{formData:t,updateFormData:i,resetForm:a,isFormValid:r}},ke=({formData:t,isFormValid:s,channelId:i,onSuccess:a})=>{const r=ne(),{company:n,brands:h}=ae(o=>o.auth),m=h==null?void 0:h[0],x=!!i,p=ge(),c=ve(),{data:_}=te({storeUid:x?void 0:t.storeId||void 0}),{data:l}=ee(1,void 0,t.storeId),u=async o=>{var C,I;if(!(n!=null&&n.id)||!(m!=null&&m.id)){w.error("Thiếu thông tin công ty hoặc thương hiệu");return}if(!o.sourceName.length){w.error("Vui lòng chọn ít nhất một nguồn đơn hàng");return}let S,V;if(x)S=o.paymentMethodId,V=o.selectedPaymentMethodId||o.paymentMethodId;else{const g=_==null?void 0:_.find(N=>N.id===o.selectedPaymentMethodId);S=o.paymentMethodId==="postpaid"?(g==null?void 0:g.code)||o.selectedPaymentMethodId:o.paymentMethodId,V=o.paymentMethodId==="postpaid"?(g==null?void 0:g.name)||(g==null?void 0:g.code)||S:o.paymentMethodId==="prepaid"?"PREPAID":S}const f=o.sourceName.map((g,N)=>{var F;const y=(F=l==null?void 0:l.data)==null?void 0:F.find(se=>se.id===g);return x&&i?{source_id:(y==null?void 0:y.source_id)||g,source_name:(y==null?void 0:y.source_name)||`Source_${N+1}`,description:null,active:1,sort:N===0?4:1e3,is_fabi:1,source_type:[],id:i,company_uid:n.id,brand_uid:m.id,store_uid:o.storeId,partner_config:1,extra_data:{require_tran_no:o.requirePartnerInvoice?1:0,commission:o.commissionRate/100,deduct_tax_rate:o.deductTaxRate/100,payment_method_id:S,payment_type:o.paymentMethodId.toUpperCase(),marketing_partner_cost_type:o.voucherCostType==="PERCENTAGE"?"PERCENT":"AMOUNT",marketing_partner_cost:o.marketingPartnerCost,voucher_run_partner:o.voucherRunPartner||"",not_show_partner_bill:parseInt(o.notShowPartnerBill),use_order_online:o.useOnlineOrder?1:0,exclude_ship:o.excludeShipping?1:0,marketing_partner_cost_from_date:o.marketingStartDate?new Date(o.marketingStartDate).getTime():Date.now(),marketing_partner_cost_to_date:o.marketingEndDate?new Date(o.marketingEndDate).getTime():Date.now(),payment_method_name:V,marketing_partner_cost_hour_day:Y(o.marketingHours)||138752,marketing_partner_cost_date_week:X(o.marketingDays)||224}}:{source_id:(y==null?void 0:y.source_id)||g,source_name:(y==null?void 0:y.source_name)||`Source_${N+1}`,description:null,active:1,sort:N===0?4:1e3,is_fabi:1,source_type:[],company_uid:n.id,brand_uid:m.id,store_uid:o.storeId,partner_config:1,extra_data:{require_tran_no:o.requirePartnerInvoice?1:0,commission:o.commissionRate/100,deduct_tax_rate:o.deductTaxRate/100,payment_method_id:S,payment_type:o.paymentMethodId.toUpperCase(),marketing_partner_cost_type:o.voucherCostType==="PERCENTAGE"?"PERCENT":"AMOUNT",marketing_partner_cost:o.marketingPartnerCost,voucher_run_partner:o.voucherRunPartner||"",not_show_partner_bill:parseInt(o.notShowPartnerBill),use_order_online:o.useOnlineOrder?1:0,exclude_ship:o.excludeShipping?1:0,marketing_partner_cost_from_date:o.marketingStartDate?new Date(o.marketingStartDate).getTime():Date.now(),marketing_partner_cost_to_date:o.marketingEndDate?new Date(o.marketingEndDate).getTime():Date.now(),marketing_partner_cost_date_week:X(o.marketingDays)||224,marketing_partner_cost_hour_day:Y(o.marketingHours)||138752,payment_method_name:V}}});try{if(x){const g=f[0];await p.mutateAsync(g),w.success("Cập nhật thành công kênh bán hàng")}else await p.mutateAsync(f),w.success(`Tạo thành công ${f.length} kênh bán hàng`);r({to:"/sale-channel/channel"}),a==null||a()}catch(g){const N=g;if(((C=N==null?void 0:N.response)==null?void 0:C.status)===200||((I=N==null?void 0:N.response)==null?void 0:I.status)===201){const z=x?"Cập nhật thành công kênh bán hàng":`Tạo thành công ${f.length} kênh bán hàng`;w.success(z),r({to:"/sale-channel/channel"}),a==null||a();return}const y=fe(g);w.error(y)}};return{handleBack:()=>{r({to:"/sale-channel/channel"})},handleSave:()=>{if(!s){w.error("Vui lòng điền đầy đủ thông tin bắt buộc");return}u(t)},createChannelMutation:x?c:p}};function Se(){const[t,s]=v.useState(""),[i,a]=v.useState(!1),{data:r,isLoading:n}=xe(),h=r?r.filter(p=>p.name.toLowerCase().includes(t.toLowerCase())):[];return{stores:r,filteredStores:h,searchTerm:t,setSearchTerm:s,isPopoverOpen:i,setIsPopoverOpen:a,isLoading:n,getSelectedStore:p=>r==null?void 0:r.find(c=>c.id===p),handleStoreSelect:(p,c)=>{c(p),a(!1),s("")}}}function Ce(t,s=[]){const[i,a]=v.useState(""),[r,n]=v.useState(!1),[h,m]=v.useState(s),[x,p]=v.useState(!0),{data:c,isLoading:_}=ee(1,void 0,t);v.useEffect(()=>{m(s)},[s]);const l=c!=null&&c.data?c.data.slice(1).filter(f=>f.source_name.toLowerCase().includes(i.toLowerCase())):[],u=l.filter(f=>h.includes(f.id)),d=l.filter(f=>!h.includes(f.id));return{searchTerm:i,setSearchTerm:a,isDialogOpen:r,setIsDialogOpen:n,selectedSources:h,showSelectedSources:x,setShowSelectedSources:p,isLoading:_,filteredSources:l,selectedSourcesData:u,unselectedSourcesData:d,getSelectedSourceNames:()=>{var f,C,I;return((I=(C=(f=c==null?void 0:c.data)==null?void 0:f.filter(g=>h.includes(g.id)))==null?void 0:C.map(g=>g.source_name))==null?void 0:I.join(", "))||""},handleSourceToggle:f=>{const C=h.includes(f)?h.filter(I=>I!==f):[...h,f];m(C)},handleDialogConfirm:f=>{f(h),n(!1)},handleDialogCancel:()=>{m(s),n(!1)},resetSources:()=>{m([])}}}const Ie=({channelId:t,onDataLoaded:s})=>{const{data:i,isLoading:a,error:r}=je(t),[n,h]=v.useState(!1),[m,x]=v.useState(!1),p=v.useRef(s);if(p.current=s,i&&p.current&&!n){const c=Q(i);p.current(c),h(!0),x(!0)}return v.useEffect(()=>{if(i&&p.current&&!n){const c=Q(i);p.current(c),h(!0),x(!0)}},[i,t,n]),v.useEffect(()=>{h(!1),x(!1)},[t]),v.useEffect(()=>{h(!1),x(!1)},[]),{channelData:i,isLoading:a,error:r,isDataLoaded:m&&!!i}},Q=t=>{var a,r;const s=t.extra_data;return{sourceName:t.source_name?[t.source_name]:[],description:t.description||"",storeId:t.store_uid,commissionRate:(s.commission||0)*100,excludeShipping:s.exclude_ship===1,paymentType:s.payment_type||"",deductTaxRate:(s.deduct_tax_rate||0)*100,requireTransactionNumber:s.require_tran_no===1,useOnlineOrder:s.use_order_online===1,paymentMethodId:((a=s.payment_type)==null?void 0:a.toLowerCase())||"prepaid",selectedPaymentMethodId:s.payment_method_name||s.payment_method_id||"",requirePartnerInvoice:s.require_tran_no===1,voucherRunPartner:s.voucher_run_partner||"",notShowPartnerBill:((r=s.not_show_partner_bill)==null?void 0:r.toString())||"0",marketingPartnerCost:s.marketing_partner_cost||0,voucherCostType:s.marketing_partner_cost_type||"PERCENTAGE",marketingStartDate:s.marketing_partner_cost_from_date?new Date(s.marketing_partner_cost_from_date).toISOString().split("T")[0]:"",marketingEndDate:s.marketing_partner_cost_to_date?new Date(s.marketing_partner_cost_to_date).toISOString().split("T")[0]:"",marketingDays:Ne(s.marketing_partner_cost_date_week||224),marketingHours:_e(s.marketing_partner_cost_hour_day||138752)}};function we({formData:t,onFormDataChange:s,mode:i,isLoading:a=!1}){const r=Se(),n=Ce(t.storeId,t.sourceName||[]),h=r.getSelectedStore(t.storeId),m=n.getSelectedSourceNames()||null,x=c=>{s({storeId:c,sourceName:[]}),n.resetSources(),r.handleStoreSelect(c,()=>{})},p=()=>{n.handleDialogConfirm(c=>{s({sourceName:c})})};return e.jsxs("div",{className:"rounded-lg border p-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Thông tin cơ bản"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(j,{className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(he,{open:r.isPopoverOpen,onOpenChange:r.setIsPopoverOpen,children:[e.jsx(me,{asChild:!0,children:e.jsxs(b,{variant:"outline",role:"combobox","aria-expanded":r.isPopoverOpen,className:"flex-1 justify-between",disabled:a||r.isLoading||i==="edit",children:[e.jsxs("span",{className:"truncate",children:[h?h.name:"Chọn cửa hàng",i==="edit"&&" (Không thể thay đổi)"]}),e.jsx(W,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),e.jsx(ue,{className:"w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"flex items-center border-b px-3 py-2",children:[e.jsx(pe,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),e.jsx(P,{placeholder:"Tìm kiếm cửa hàng...",value:r.searchTerm,onChange:c=>r.setSearchTerm(c.target.value),className:"border-0 p-0 focus-visible:ring-0"})]}),e.jsx("div",{className:"max-h-60 overflow-auto",children:r.isLoading?e.jsx("div",{className:"py-6 text-center text-sm text-gray-500",children:"Đang tải..."}):r.filteredStores.length===0?e.jsx("div",{className:"py-6 text-center text-sm text-gray-500",children:"Không tìm thấy cửa hàng nào."}):e.jsx("div",{className:"py-1",children:r.filteredStores.map(c=>e.jsx("div",{className:G("cursor-pointer px-3 py-2 text-sm hover:bg-gray-100",t.storeId===c.id&&"bg-gray-100"),onClick:()=>x(c.id),children:c.name},c.id))})})]})})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(j,{className:"min-w-[200px] text-sm font-medium",children:["Nguồn ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(ie,{open:n.isDialogOpen,onOpenChange:n.setIsDialogOpen,children:[e.jsx(le,{asChild:!0,children:e.jsx(b,{variant:"outline",className:"flex-1 justify-between",disabled:a||n.isLoading||!t.storeId||i==="edit",children:e.jsxs("span",{className:"truncate",children:[m||"Chọn nguồn",i==="edit"&&" (Không thể thay đổi)"]})})}),e.jsxs(ce,{className:"max-w-md",children:[e.jsx(de,{children:e.jsx(oe,{children:"Chọn nguồn"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Tìm kiếm",value:n.searchTerm,onChange:c=>n.setSearchTerm(c.target.value)}),t.storeId&&!n.isLoading&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between text-sm text-green-600",onClick:()=>n.setShowSelectedSources(!n.showSelectedSources),children:[e.jsxs("span",{children:["✓ Đã chọn ",n.selectedSources.length]}),e.jsx(W,{className:G("h-4 w-4 transition-transform",!n.showSelectedSources&&"rotate-180")})]}),n.showSelectedSources&&n.selectedSourcesData.length>0&&e.jsx("div",{className:"space-y-2 border-l-2 border-green-200 pl-4",children:n.selectedSourcesData.map(c=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{id:`selected-${c.id}`,checked:!0,onCheckedChange:()=>n.handleSourceToggle(c.id)}),e.jsx("label",{htmlFor:`selected-${c.id}`,className:"cursor-pointer text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:c.source_name})]},c.id))})]}),t.storeId&&!n.isLoading&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:["Còn lại ",n.unselectedSourcesData.length]}),n.unselectedSourcesData.length>0&&e.jsx("div",{className:"max-h-40 space-y-2 overflow-auto",children:n.unselectedSourcesData.map(c=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{id:`unselected-${c.id}`,checked:!1,onCheckedChange:()=>n.handleSourceToggle(c.id)}),e.jsx("label",{htmlFor:`unselected-${c.id}`,className:"cursor-pointer text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:c.source_name})]},c.id))})]}),t.storeId?n.isLoading?e.jsx("div",{className:"py-6 text-center text-sm text-gray-500",children:"Đang tải..."}):n.filteredSources.length===0?e.jsx("div",{className:"text-center text-sm text-gray-400",children:"Không có dữ liệu"}):null:e.jsx("div",{className:"py-6 text-center text-sm text-gray-500",children:"Vui lòng chọn cửa hàng trước."}),e.jsxs("div",{className:"flex justify-between space-x-2 pt-4",children:[e.jsx(b,{variant:"outline",onClick:n.handleDialogCancel,children:"Hủy"}),e.jsx(b,{onClick:p,children:"Xong"})]})]})]})]})]})]})]})}function Te({formData:t,onFormDataChange:s,mode:i,isLoading:a=!1}){var u;const[r,n]=v.useState(t.storeId),{data:h,isLoading:m}=te({storeUid:i==="add"&&t.storeId||void 0});v.useEffect(()=>{r!==t.storeId&&i==="add"?(t.selectedPaymentMethodId&&s({selectedPaymentMethodId:""}),n(t.storeId)):r!==t.storeId&&n(t.storeId)},[t.storeId,t.selectedPaymentMethodId,s,i,r]);const x=t.paymentMethodId==="prepaid"||t.paymentMethodId==="postpaid",p=t.paymentMethodId==="postpaid",c=p&&i==="edit"&&t.selectedPaymentMethodId&&t.storeId,_=d=>{d>100?s({commissionRate:100}):s({commissionRate:d})},l=d=>{d>100?s({deductTaxRate:100}):s({deductTaxRate:d})};return e.jsx(Z,{children:e.jsxs("div",{className:"rounded-lg border p-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Cấu hình kênh bán hàng"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(j,{htmlFor:"commission-rate",className:"min-w-[200px] text-sm font-medium",children:["Phần trăm hoa hồng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(P,{id:"commission-rate",type:"number",value:t.commissionRate,onChange:d=>_(Number(d.target.value)),placeholder:"0",className:"flex-1",min:"0",max:"100",disabled:a}),e.jsx("span",{className:"text-sm text-gray-500",children:"%"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(j,{htmlFor:"deduct-tax-rate",className:"min-w-[200px] text-sm font-medium",children:["Cấu hình thuế khấu trừ ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(P,{id:"deduct-tax-rate",type:"number",value:t.deductTaxRate,onChange:d=>l(Number(d.target.value)),placeholder:"0",className:"flex-1",min:"0",max:"100",disabled:a}),e.jsx("span",{className:"text-sm text-gray-500",children:"%"})]})]}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsxs("div",{className:"flex w-[200px] items-center gap-2",children:[e.jsx(j,{className:"flex-1 text-sm leading-relaxed font-medium",children:"Sử dụng hóa đơn dành cho khách hàng khi in"}),e.jsxs(M,{children:[e.jsx(E,{asChild:!0,children:e.jsx(R,{className:"h-4 w-4 flex-shrink-0 cursor-help text-gray-400"})}),e.jsx(O,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Khi thanh toán POS sẽ không hiển thị tiền hoa hồng, tiền đối tác hỗ trợ vi vậy tổng tiền trên hóa đơn khi in sẽ khác so với tổng tiền ghi nhận trên hệ thống. Áp dụng cho trường hợp trả hóa đơn cho khách mua hàng!"})})]})]}),e.jsx("div",{className:"mt-0.5 flex-1",children:e.jsxs(q,{value:t.notShowPartnerBill,onValueChange:d=>s({notShowPartnerBill:d}),disabled:a,children:[e.jsx($,{className:"w-full",children:e.jsx(D,{placeholder:"Chọn loại hóa đơn"})}),e.jsxs(K,{children:[e.jsx(T,{value:"0",children:"Không sử dụng hóa đơn dành cho khách khi in"}),e.jsx(T,{value:"1",children:"Sử dụng hóa đơn dành cho khách khi in"}),e.jsx(T,{value:"2",children:"In hóa đơn chỉ với phần giảm giá nhà hàng"})]})]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(j,{htmlFor:"apply-online-order",className:"min-w-[200px] text-sm font-medium",children:"Áp dụng đơn online"}),e.jsx(A,{id:"apply-online-order",checked:t.useOnlineOrder,onCheckedChange:d=>s({useOnlineOrder:d}),disabled:a})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(j,{htmlFor:"exclude-shipping",className:"w-[200px] text-sm font-medium",children:"Không tính phí vận chuyển vào doanh thu (áp dụng đơn online)"}),e.jsx(A,{id:"exclude-shipping",checked:t.excludeShipping,onCheckedChange:d=>s({excludeShipping:d}),disabled:a})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(j,{className:"min-w-[200px] text-sm font-medium",children:["Hình thức thanh toán ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(q,{value:t.paymentMethodId,onValueChange:d=>s({paymentMethodId:d}),disabled:a,children:[e.jsx($,{className:"flex-1",children:e.jsx(D,{placeholder:"Chọn hình thức thanh toán"})}),e.jsxs(K,{children:[e.jsx(T,{value:"prepaid",children:"Trả trước"}),e.jsx(T,{value:"postpaid",children:"Trả sau"})]})]})]}),(p||c)&&e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(j,{className:"min-w-[200px] text-sm font-medium",children:["Phương thức thanh toán ",e.jsx("span",{className:"text-red-500",children:"*"})]}),i==="edit"?e.jsx("div",{className:"border-input bg-background flex-1 rounded-md border px-3 py-2 text-sm",children:t.selectedPaymentMethodId||"Chưa có thông tin"}):e.jsxs(q,{value:(u=t.selectedPaymentMethodId)==null?void 0:u.trim(),onValueChange:d=>s({selectedPaymentMethodId:d}),disabled:a||!t.storeId||m,children:[e.jsx($,{className:"flex-1",children:e.jsx(D,{placeholder:t.storeId?m?"Đang tải...":"Chọn phương thức thanh toán":"Vui lòng chọn cửa hàng trước"})}),e.jsx(K,{children:h==null?void 0:h.map(d=>e.jsx(T,{value:d.name.trim(),children:d.name},d.id))})]},`payment-method-${t.storeId}`)]}),x&&e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(j,{className:"min-w-[200px] text-sm font-medium",children:"Nhập số hóa đơn đối tác"}),e.jsx(A,{checked:t.requirePartnerInvoice,onCheckedChange:d=>s({requirePartnerInvoice:d}),disabled:a})]})]})]})})}const U=t=>{if(!t&&t!==0)return"";const s=typeof t=="string"?parseFloat(t):t;return isNaN(s)?"":s.toLocaleString("vi-VN")},Pe=t=>{if(!t)return 0;const s=t.replace(/,/g,""),i=parseFloat(s);return isNaN(i)?0:i};function Ve({formData:t,onFormDataChange:s,isLoading:i=!1}){const[a,r]=v.useState("amount"),[n,h]=v.useState("");v.useEffect(()=>{if(t.voucherCostType){const l=t.voucherCostType==="PERCENT"?"percentage":"amount";r(l)}},[t.voucherCostType]),v.useEffect(()=>{var l;a==="amount"&&t.marketingPartnerCost?h(U(t.marketingPartnerCost)):h(((l=t.marketingPartnerCost)==null?void 0:l.toString())||"")},[t.marketingPartnerCost,a]);const m=l=>{if(a==="amount"){const u=l.replace(/[^\d,]/g,""),d=Pe(u);h(u),s({marketingPartnerCost:d})}else{const u=l.replace(/[^\d.]/g,""),d=parseFloat(u)||0;d>100?(h("100"),s({marketingPartnerCost:100})):(h(u),s({marketingPartnerCost:d}))}},x=()=>{a==="amount"&&t.marketingPartnerCost&&h(U(t.marketingPartnerCost))},p=l=>{var u;r(l),s({voucherCostType:l==="percentage"?"PERCENTAGE":"AMOUNT"}),l==="amount"&&t.marketingPartnerCost?h(U(t.marketingPartnerCost)):h(((u=t.marketingPartnerCost)==null?void 0:u.toString())||"")},c=l=>{const u=t.marketingDays||[],d=u.includes(l)?u.filter(k=>k!==l):[...u,l];s({marketingDays:d})},_=l=>{const u=t.marketingHours||[],d=u.includes(l)?u.filter(k=>k!==l):[...u,l];s({marketingHours:d})};return e.jsx(Z,{children:e.jsxs("div",{className:"rounded-lg border p-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Cấu hình chi phí đối tác marketing"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex w-[200px] items-center gap-2",children:[e.jsxs(j,{htmlFor:"marketing-cost",className:"text-sm font-medium",children:[a==="amount"?"Số tiền/đơn":"Phần trăm/đơn"," ",e.jsx("span",{className:"text-red-500",children:"*"})]}),a==="percentage"&&e.jsxs(M,{children:[e.jsx(E,{asChild:!0,children:e.jsx(R,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(O,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Phần trăm chỉ áp dụng với chiết khấu toàn hóa đơn"})})]})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(P,{id:"marketing-cost",type:"text",value:n,onChange:l=>m(l.target.value),onBlur:x,placeholder:"0",className:"flex-1",disabled:i}),e.jsxs("div",{className:"flex rounded-md border",children:[e.jsx(b,{type:"button",variant:a==="amount"?"default":"ghost",size:"sm",onClick:()=>p("amount"),disabled:i,className:"rounded-r-none border-r px-3 py-1 text-sm",children:"đ"}),e.jsx(b,{type:"button",variant:a==="percentage"?"default":"ghost",size:"sm",onClick:()=>p("percentage"),disabled:i,className:"rounded-l-none px-3 py-1 text-sm",children:"%"})]})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex w-[200px] items-center gap-2",children:[e.jsx(j,{className:"text-sm font-medium",children:"Voucher cho chương trình"}),e.jsxs(M,{children:[e.jsx(E,{asChild:!0,children:e.jsx(R,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(O,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu có cấu hình voucher, POS sẽ yêu cầu phải nhập voucher mới được thanh toán!"})})]})]}),e.jsx(P,{value:t.voucherRunPartner,onChange:l=>s({voucherRunPartner:l.target.value}),placeholder:"Nhập voucher",className:"flex-1",disabled:i})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(j,{className:"text-sm font-medium",children:"Ngày áp dụng hỗ trợ marketing"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(j,{className:"w-[200px] rounded bg-gray-100 px-3 py-2 text-sm font-medium text-gray-600",children:"Ngày bắt đầu"}),e.jsx("input",{type:"date",value:t.marketingStartDate,onChange:l=>s({marketingStartDate:l.target.value}),className:"border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex-1 rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",style:{colorScheme:"light"},disabled:i})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(j,{className:"w-[200px] rounded bg-gray-100 px-3 py-2 text-sm font-medium text-gray-600",children:"Ngày kết thúc"}),e.jsx("input",{type:"date",value:t.marketingEndDate,onChange:l=>s({marketingEndDate:l.target.value}),className:"border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex-1 rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",style:{colorScheme:"light"},disabled:i})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(j,{className:"text-sm font-medium",children:"Khung thời gian áp dụng hỗ trợ marketing"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(j,{className:"text-sm font-medium text-gray-500",children:"Chọn ngày"}),e.jsxs(M,{children:[e.jsx(E,{asChild:!0,children:e.jsx(R,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(O,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần"})})]})]}),e.jsx("div",{className:"flex gap-2",children:B.map(l=>{var u;return e.jsx(b,{type:"button",variant:(u=t.marketingDays)!=null&&u.includes(l.value)?"default":"outline",size:"sm",onClick:()=>c(l.value),disabled:i,className:"flex-1",children:l.label},l.value)})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(j,{className:"text-sm font-medium text-gray-500",children:"Chọn giờ"}),e.jsxs(M,{children:[e.jsx(E,{asChild:!0,children:e.jsx(R,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(O,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày"})})]})]}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:H.map(l=>{var u;return e.jsx(b,{type:"button",variant:(u=t.marketingHours)!=null&&u.includes(l.value)?"default":"outline",size:"sm",onClick:()=>_(l.value),disabled:i,className:"text-xs",children:l.value},l.value)})})]})]})]})})}function Me({formData:t,onFormDataChange:s,mode:i,isLoading:a=!1}){return e.jsxs("div",{className:"space-y-8",children:[e.jsx(we,{formData:t,onFormDataChange:s,mode:i,isLoading:a}),e.jsx(Te,{formData:t,onFormDataChange:s,mode:i,isLoading:a}),e.jsx(Ve,{formData:t,onFormDataChange:s,mode:i,isLoading:a})]})}function Qe(){let t;try{const d=re({from:"/_authenticated/sale-channel/channel/detail/$uuid"});t=d==null?void 0:d.uuid}catch{t=void 0}if(!t){const d=window.location.pathname.split("/"),k=d.findIndex(L=>L==="detail");k!==-1&&d[k+1]&&(t=d[k+1])}const s=!!t,{formData:i,updateFormData:a,isFormValid:r,resetForm:n}=be(),h=v.useCallback(d=>{a(d)},[a]),{isLoading:m,isDataLoaded:x}=Ie({channelId:t,onDataLoaded:h}),{handleBack:p,handleSave:c,createChannelMutation:_}=ke({formData:i,isFormValid:r,channelId:t,onSuccess:n}),l=_.isPending||m,u=!s||s&&x;return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(b,{variant:"ghost",size:"sm",onClick:p,className:"flex items-center",children:e.jsx(ye,{className:"h-4 w-4"})}),e.jsx(b,{type:"button",disabled:l||!r,className:"min-w-[100px]",onClick:c,children:_.isPending?s?"Đang cập nhật...":"Đang tạo...":"Lưu"})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:s?"Chỉnh sửa kênh bán hàng":"Tạo kênh bán hàng mới"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:u?e.jsx(Me,{formData:i,onFormDataChange:a,mode:s?"edit":"add",isLoading:l}):e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-blue-600"}),e.jsx("p",{className:"text-gray-600",children:"Đang tải dữ liệu kênh bán hàng..."})]})})})]})}export{Qe as C};
