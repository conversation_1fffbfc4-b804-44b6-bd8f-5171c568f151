import{v as m,r as i,j as e,B as c,c as a}from"./index-D0Grd55b.js";import{D as d,a as h,b as u,c as o}from"./dropdown-menu-BvqrmFsX.js";import{a as x,b as p,u as f}from"./search-context-CjM0jrYw.js";import{c as j}from"./createReactComponent-eJgt86Cn.js";import"./date-range-picker-CruKYeHR.js";import"./form-Bk1C9kLO.js";import{I as k}from"./IconSearch-ClUZlEwa.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var r=j("outline","check","IconCheck",[["path",{d:"M5 12l5 5l10 -10",key:"svg-0"}]]);function S(){const{theme:s,setTheme:t}=m();return i.useEffect(()=>{const n=s==="dark"?"#020817":"#fff",l=document.querySelector("meta[name='theme-color']");l&&l.setAttribute("content",n)},[s]),e.jsxs(d,{modal:!1,children:[e.jsx(h,{asChild:!0,children:e.jsxs(c,{variant:"ghost",size:"icon",className:"scale-95 rounded-full",children:[e.jsx(x,{className:"size-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90"}),e.jsx(p,{className:"absolute size-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0"}),e.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),e.jsxs(u,{align:"end",children:[e.jsxs(o,{onClick:()=>t("light"),children:["Light"," ",e.jsx(r,{size:14,className:a("ml-auto",s!=="light"&&"hidden")})]}),e.jsxs(o,{onClick:()=>t("dark"),children:["Dark",e.jsx(r,{size:14,className:a("ml-auto",s!=="dark"&&"hidden")})]}),e.jsxs(o,{onClick:()=>t("system"),children:["System",e.jsx(r,{size:14,className:a("ml-auto",s!=="system"&&"hidden")})]})]})]})}function T({className:s="",placeholder:t="Tìm kiếm..."}){const{setOpen:n}=f();return e.jsxs(c,{variant:"outline",className:a("bg-muted/25 text-muted-foreground hover:bg-muted/50 relative h-8 w-full flex-1 justify-start rounded-md text-sm font-normal shadow-none sm:pr-12 md:w-40 md:flex-none lg:w-56 xl:w-64",s),onClick:()=>n(!0),children:[e.jsx(k,{"aria-hidden":"true",className:"absolute top-1/2 left-1.5 -translate-y-1/2"}),e.jsx("span",{className:"ml-3",children:t}),e.jsxs("kbd",{className:"bg-muted pointer-events-none absolute top-[0.3rem] right-[0.3rem] hidden h-5 items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 select-none sm:flex",children:[e.jsx("span",{className:"text-xs",children:"⌘"}),"K"]})]})}export{T as S,S as T};
