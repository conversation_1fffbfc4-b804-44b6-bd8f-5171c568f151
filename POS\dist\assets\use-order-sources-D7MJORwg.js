import{u as O}from"./useQuery-Ck3BpOfq.js";import{u as y,a3 as p,a4 as S}from"./index-D0Grd55b.js";import{u as m}from"./useMutation-ATsU-ht7.js";import{a as d}from"./pos-api-B09qRspF.js";const g={getOrderSources:async(t,n,s=1,r,i)=>{const u=new URLSearchParams({skip_limit:"true",company_uid:t,brand_uid:n,partner_config:"0"});return u.get("skip_limit")||u.append("page",s.toString()),r&&r.trim()&&u.append("search",r.trim()),i&&i!=="all"&&u.append("store_uid",i),(await d.get(`/mdata/v1/sources?${u.toString()}`)).data},createOrderSource:async t=>(await d.post("/mdata/v1/source",t)).data.data,getOrderSourceById:async t=>(await d.get(`/mdata/v1/source?id=${t}`)).data.data,updateOrderSourceById:async t=>(await d.put("/mdata/v1/source",t)).data.data,updateOrderSource:async t=>(await d.put(`/mdata/v1/sources/${t.id}`,t)).data.data,deleteOrderSource:async t=>{const n=new URLSearchParams({company_uid:t.company_uid,brand_uid:t.brand_uid,id:t.id});await d.delete(`/mdata/v1/source?${n.toString()}`)},getOrderSourceDetail:async t=>(await d.get(`/mdata/v1/source?id=${t}`)).data,getOrderSourcesForSort:async(t,n)=>{const s=new URLSearchParams({skip_limit:"true",company_uid:t,brand_uid:n});return(await d.get(`/mdata/v1/sources?${s.toString()}`)).data},updateOrderSourcesSort:async t=>{await d.put("/mdata/v1/source/sort",t)}},h={ORDER_SOURCES_LIST:"order-sources-list"},U=(t=1,n,s)=>{const{company:r,brands:i}=y(e=>e.auth),u=i==null?void 0:i[0];return O({queryKey:[h.ORDER_SOURCES_LIST,t,n,s],queryFn:async()=>{const e=(r==null?void 0:r.id)||"",o=(u==null?void 0:u.id)||"";if(!e||!o)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");return await g.getOrderSources(e,o,t,n,s)},enabled:!!(r!=null&&r.id&&(u!=null&&u.id)&&s),staleTime:5*60*1e3})},C=()=>{const{company:t,brands:n}=y(r=>r.auth),s=n==null?void 0:n[0];return O({queryKey:[h.ORDER_SOURCES_LIST,"sort"],queryFn:async()=>{const r=(t==null?void 0:t.id)||"",i=(s==null?void 0:s.id)||"";if(!r||!i)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");return await g.getOrderSourcesForSort(r,i)},enabled:!!(t!=null&&t.id&&(s!=null&&s.id)),staleTime:5*60*1e3})},T=()=>{const t=p(),{company:n,brands:s}=y(e=>e.auth),r=s==null?void 0:s[0],{mutate:i,isPending:u}=m({mutationFn:async e=>{const o=(n==null?void 0:n.id)||"",a=(r==null?void 0:r.id)||"";if(!o||!a)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");const c={...e,company_uid:o,brand_uid:a};return await g.createOrderSource(c)},onSuccess:()=>{t.invalidateQueries({queryKey:[h.ORDER_SOURCES_LIST]}),S.success("Tạo nguồn đơn hàng thành công")},onError:e=>{var a,c;const o=((c=(a=e==null?void 0:e.response)==null?void 0:a.data)==null?void 0:c.message)||"Có lỗi xảy ra khi tạo nguồn đơn hàng";S.error(o)}});return{createOrderSource:i,isCreating:u}},q=()=>{const t=p(),{company:n,brands:s}=y(e=>e.auth),r=s==null?void 0:s[0],{mutate:i,isPending:u}=m({mutationFn:async e=>{const o=(n==null?void 0:n.id)||"",a=(r==null?void 0:r.id)||"";if(!o||!a)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");const c=e.map(_=>({source_id:_.source_id,sort:_.sort,company_uid:o,brand_uid:a}));return await g.updateOrderSourcesSort(c)},onSuccess:()=>{t.invalidateQueries({queryKey:[h.ORDER_SOURCES_LIST]}),S.success("Cập nhật thứ tự nguồn đơn hàng thành công")},onError:e=>{var a,c;const o=((c=(a=e==null?void 0:e.response)==null?void 0:a.data)==null?void 0:c.message)||"Có lỗi xảy ra khi cập nhật thứ tự";S.error(o)}});return{updateSort:i,isUpdating:u}},f=()=>{const t=p(),{company:n,brands:s}=y(e=>e.auth),r=s==null?void 0:s[0],{mutate:i,isPending:u}=m({mutationFn:async e=>{const o=(n==null?void 0:n.id)||"",a=(r==null?void 0:r.id)||"";if(!o||!a)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");return await g.deleteOrderSource({id:e.id,company_uid:o,brand_uid:a})},onSuccess:()=>{t.invalidateQueries({queryKey:[h.ORDER_SOURCES_LIST]}),S.success("Xóa nguồn đơn hàng thành công")},onError:e=>{var a,c;const o=((c=(a=e==null?void 0:e.response)==null?void 0:a.data)==null?void 0:c.message)||"Có lỗi xảy ra khi xóa nguồn đơn hàng";S.error(o)}});return{deleteOrderSource:i,isDeleting:u}},D=t=>O({queryKey:[h.ORDER_SOURCES_LIST,"detail",t],queryFn:()=>g.getOrderSourceById(t),enabled:!!t,staleTime:5*60*1e3,gcTime:10*60*1e3}),I=()=>{const t=p(),{company:n,brands:s}=y(e=>e.auth),r=s==null?void 0:s[0],{mutate:i,isPending:u}=m({mutationFn:async e=>{const o=(n==null?void 0:n.id)||"",a=(r==null?void 0:r.id)||"";if(!o||!a)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");const c={...e,company_uid:o,brand_uid:a};return await g.updateOrderSourceById(c)},onSuccess:()=>{t.invalidateQueries({queryKey:[h.ORDER_SOURCES_LIST]}),S.success("Cập nhật nguồn đơn hàng thành công")},onError:e=>{var a,c;const o=((c=(a=e==null?void 0:e.response)==null?void 0:a.data)==null?void 0:c.message)||"Có lỗi xảy ra khi cập nhật nguồn đơn hàng";S.error(o)}});return{updateOrderSource:i,isUpdating:u}};export{q as a,f as b,U as c,T as d,I as e,D as f,g as o,C as u};
