import{aU as t,j as m}from"./index-D0Grd55b.js";import"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import"./header-yApU5MZq.js";import"./main-Czv3HpP4.js";import"./search-context-CjM0jrYw.js";import"./date-range-picker-CruKYeHR.js";import"./form-Bk1C9kLO.js";import{C as i}from"./create-table-form-xgk7y9va.js";import"./separator-CiUiq7rT.js";import"./command-Bcq7GTcy.js";import"./calendar-5lpy20z0.js";import"./createLucideIcon-DNzDbUBG.js";import"./index-CW7Xpojs.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-C8IVKkOo.js";import"./search-C4dfJjaw.js";import"./createReactComponent-eJgt86Cn.js";import"./scroll-area-r2ikcXUQ.js";import"./index-CI2TkimM.js";import"./select-DBO-8fSu.js";import"./index-C-UyCxtf.js";import"./check-TFQPNqMS.js";import"./IconChevronRight-DnVUSvDn.js";import"./chevron-right-D2H_xC9V.js";import"./react-icons.esm-BOnXQgjl.js";import"./popover-CMTiAV3j.js";import"./use-areas-D9b36wbs.js";import"./useQuery-Ck3BpOfq.js";import"./utils-km2FGkQ4.js";import"./useMutation-ATsU-ht7.js";import"./images-api-DS53jPy5.js";import"./query-keys-3lmd-xp6.js";import"./use-sales-channels-BTa1ZELq.js";import"./use-tables-DSnIc-n-.js";import"./input-C-0UnKOB.js";import"./checkbox-C_eqgJmW.js";import"./collapsible-MezBM5sx.js";import"./use-items-in-store-data-S1OZJBPi.js";import"./use-item-types-CuyGAtwR.js";import"./use-item-classes-s7fFgXey.js";import"./use-units-ByB1meVo.js";import"./use-removed-items-7Ty-LinJ.js";import"./items-in-store-api-BAXUzzVL.js";import"./xlsx-DkH2s96g.js";import"./copy-Bsxd7QFC.js";import"./plus-CVkScoxf.js";import"./minus-DXBXnRdj.js";const rt=function(){const{tableId:o}=t.useParams(),{store_uid:r}=t.useSearch();return m.jsx(i,{areaId:o,storeUid:r,fromTableLayout:!0})};export{rt as component};
