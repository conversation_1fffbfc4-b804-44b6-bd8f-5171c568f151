import{aA as o,j as i}from"./index-D0Grd55b.js";import{C as m}from"./index-BPVhoQmZ.js";import"./loading-spinner-BxUdFXPi.js";import"./skeleton-zUnEohn-.js";import"./search-context-CjM0jrYw.js";import"./command-Bcq7GTcy.js";import"./calendar-5lpy20z0.js";import"./createLucideIcon-DNzDbUBG.js";import"./index-CW7Xpojs.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-C8IVKkOo.js";import"./search-C4dfJjaw.js";import"./createReactComponent-eJgt86Cn.js";import"./pos-api-B09qRspF.js";import"./scroll-area-r2ikcXUQ.js";import"./index-CI2TkimM.js";import"./select-DBO-8fSu.js";import"./index-C-UyCxtf.js";import"./check-TFQPNqMS.js";import"./IconChevronRight-DnVUSvDn.js";import"./date-range-picker-CruKYeHR.js";import"./chevron-right-D2H_xC9V.js";import"./react-icons.esm-BOnXQgjl.js";import"./popover-CMTiAV3j.js";import"./form-Bk1C9kLO.js";import"./vietqr-api-C42F6d9k.js";import"./use-items-DCy2fVIC.js";import"./useQuery-Ck3BpOfq.js";import"./utils-km2FGkQ4.js";import"./item-api-1Lw2G9JI.js";import"./query-keys-3lmd-xp6.js";import"./use-customization-by-id-CsibWRdD.js";import"./use-customizations-DtrTtBD6.js";import"./useMutation-ATsU-ht7.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import"./modal-hDollbt0.js";import"./input-C-0UnKOB.js";import"./table-CLNYB6yq.js";import"./useCanGoBack-CJNc-2Fl.js";import"./use-update-customization-CdOkSHT_.js";import"./checkbox-C_eqgJmW.js";import"./collapsible-MezBM5sx.js";import"./IconX-CXY7eyC9.js";import"./use-pos-data-DYPxqEm9.js";import"./use-auth-DtiSzKyd.js";const X=function(){const t=o({from:"/_authenticated/menu/customization/customization-in-city/detail/$customizationId"});return i.jsx(m,{customizationId:t.customizationId})};export{X as component};
