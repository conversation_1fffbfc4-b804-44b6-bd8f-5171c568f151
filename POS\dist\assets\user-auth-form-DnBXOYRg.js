import{z as o,j as s,c as p,L as h,B as i}from"./index-D0Grd55b.js";import{u as g,F as j,a as n,b as t,c as l,d as m,e as d}from"./form-Bk1C9kLO.js";import{s as b}from"./zod-G2vIgQkk.js";import{u as f}from"./use-auth-DtiSzKyd.js";import{I as w}from"./input-C-0UnKOB.js";import{P as v}from"./password-input-DVF7dySI.js";import{I as F,a as N}from"./IconBrandGithub-CCiLC8VW.js";const P=o.object({email:o.string().min(1,{message:"Please enter your email"}).email({message:"Invalid email address"}),password:o.string().min(1,{message:"Please enter your password"}).min(6,{message:"Password must be at least 7 characters long"})});function G({className:c,...u}){const a=f(),r=g({resolver:b(P),defaultValues:{email:"<EMAIL>",password:"vh3969"}});function x(e){a.mutate({email:e.email,password:e.password})}return s.jsx(j,{...r,children:s.jsxs("form",{onSubmit:r.handleSubmit(x),className:p("grid gap-3",c),...u,children:[s.jsx(n,{control:r.control,name:"email",render:({field:e})=>s.jsxs(t,{children:[s.jsx(l,{children:"Email"}),s.jsx(m,{children:s.jsx(w,{placeholder:"<EMAIL>",...e})}),s.jsx(d,{})]})}),s.jsx(n,{control:r.control,name:"password",render:({field:e})=>s.jsxs(t,{className:"relative",children:[s.jsx(l,{children:"Password"}),s.jsx(m,{children:s.jsx(v,{placeholder:"********",...e})}),s.jsx(d,{}),s.jsx(h,{to:"/forgot-password",className:"text-muted-foreground absolute -top-0.5 right-0 text-sm font-medium hover:opacity-75",children:"Forgot password?"})]})}),s.jsx(i,{className:"mt-2",disabled:a.isPending,children:a.isPending?"Signing in...":"Login"}),s.jsxs("div",{className:"relative my-2",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("span",{className:"w-full border-t"})}),s.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:s.jsx("span",{className:"bg-background text-muted-foreground px-2",children:"Or continue with"})})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[s.jsxs(i,{variant:"outline",type:"button",disabled:a.isPending,children:[s.jsx(F,{className:"h-4 w-4"})," GitHub"]}),s.jsxs(i,{variant:"outline",type:"button",disabled:a.isPending,children:[s.jsx(N,{className:"h-4 w-4"})," Facebook"]})]})]})})}export{G as U};
