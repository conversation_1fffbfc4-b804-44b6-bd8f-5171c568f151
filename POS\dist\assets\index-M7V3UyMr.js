import{aA as ge,j as e,B as f,h as fe,r as y,l as ue,b as he,a as je,c as X,T as ye,o as J,p as Z,q as B}from"./index-D0Grd55b.js";import{c as _e,d as xe,e as pe,a as ve,f as Ne,g as Ce,t as Te,h as be,i as O,j as Se,D as De}from"./discount-form-context-DgxwKszq.js";import{D as we}from"./discount-toggle-button-C-9v5Jkz.js";import{v as H}from"./date-range-picker-CruKYeHR.js";import{L as v}from"./form-Bk1C9kLO.js";import{X as ke,C as ee}from"./calendar-5lpy20z0.js";import{I as W}from"./input-C-0UnKOB.js";import{S as se,a as te,b as ae,c as ne,d as ie,C as w}from"./select-DBO-8fSu.js";import{T as Ee,a as Fe,c as le}from"./tabs-b_k7_Aeh.js";import{b as Y}from"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import{u as Ae}from"./use-sales-channels-BTa1ZELq.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import{u as Q}from"./useQuery-Ck3BpOfq.js";import{Q as G}from"./query-keys-3lmd-xp6.js";import{P as Pe}from"./modal-hDollbt0.js";import{C as T}from"./checkbox-C_eqgJmW.js";import{C as k,a as E,b as F}from"./collapsible-MezBM5sx.js";import{C as A}from"./chevron-right-D2H_xC9V.js";import{P as ce,a as re,b as oe}from"./popover-CMTiAV3j.js";import{C as de}from"./calendar-Bxala8X3.js";import{f as $}from"./isSameMonth-C8JQo-AN.js";import{C as me}from"./circle-help-CEmvpE3g.js";import{j as C}from"./date-utils-DBbLjCz0.js";function Le(){const{handleBack:a,handleSave:m}=_e(),{isEditMode:c,isLoading:h,isFormValid:l}=xe(),r=ge({strict:!1}).id,{data:s}=pe(r||""),i=ve(),o=async()=>{if(!c||!s)return;const p={id:s.id,created_at:s.created_at,created_by:s.created_by,updated_at:s.updated_at,updated_by:s.updated_by,deleted:s.deleted||!1,deleted_at:s.deleted_at||null,deleted_by:s.deleted_by||null,ta_discount:s.ta_discount,ots_discount:s.ots_discount,is_all:s.is_all,is_type:s.is_type,is_item:s.is_item,type_id:s.type_id,item_id:s.item_id,discount_type:s.discount_type,from_date:s.from_date,to_date:s.to_date,time_sale_hour_day:s.time_sale_hour_day,time_sale_date_week:s.time_sale_date_week,description:s.description,extra_data:s.extra_data,active:s.active===1?0:1,revision:s.revision||null,promotion_uid:s.promotion_uid,brand_uid:s.brand_uid||"",company_uid:s.company_uid||"",sort:s.sort||1e3,store_uid:s.store_uid,discount_clone_id:s.discount_clone_id||null,source_uid:s.source_uid||"",promotion:s.promotion,source:s.source};await i.mutateAsync(p)};return e.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[e.jsx(f,{variant:"ghost",size:"sm",onClick:a,className:"flex items-center",children:e.jsx(ke,{className:"h-4 w-4"})}),e.jsxs("h1",{className:"text-3xl font-bold",children:[c&&"Chỉnh sửa chương trình giảm giá",!c&&"Tạo chương trình giảm giá"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[c&&s&&e.jsx(we,{isActive:s.active===1,onToggle:o,disabled:h||i.isPending}),e.jsxs(f,{type:"button",disabled:h||!l,className:"min-w-[100px]",onClick:m,children:[h&&c&&"Đang cập nhật...",h&&!c&&"Đang tạo...",!h&&c&&"Cập nhật",!h&&!c&&"Lưu"]})]})]})}function Ue({discountId:a,initialStoreUid:m}={}){const c=fe(),h=!!a,{data:l,isLoading:u}=pe(a||""),[r,s]=y.useState({storeUid:m||"",channelUid:"",discountType:"PERCENT",discountPercentage:0,discountAmount:0,startDate:new Date().toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],marketingDays:[],marketingHours:[],promotionUid:"",promotionName:"",filterState:{is_all:0,is_item:0,is_type:0,type_id:"",item_id:"",is_combo:0,combo_id:""}}),i=d=>{const _=new Date(d);return new Date(_.getTime()+7*60*60*1e3).toISOString().split("T")[0]},o=d=>new Date(d+"T00:00:00+07:00"),p=d=>{var S,K,I;const _=i(d.from_date),b=i(d.to_date),z=C.convertNumbersToDayStrings(C.convertBitFlagsToDays(d.time_sale_date_week)),L=C.convertNumbersToHourStrings(C.convertBitFlagsToHours(d.time_sale_hour_day));return{storeUid:d.store_uid,channelUid:d.source_uid,discountType:d.discount_type,discountPercentage:d.discount_type==="PERCENT"?d.ta_discount*100:0,discountAmount:d.discount_type==="AMOUNT"?d.ta_discount:0,startDate:_,endDate:b,marketingDays:z,marketingHours:L,promotionUid:d.promotion_uid,promotionName:((S=d.promotion)==null?void 0:S.promotion_name)||"",filterState:{is_all:d.is_all,is_item:d.is_item,is_type:d.is_type,type_id:d.type_id,item_id:d.item_id,is_combo:(K=d.extra_data)==null?void 0:K.is_combo,combo_id:((I=d.extra_data)==null?void 0:I.combo_id)||""}}};y.useEffect(()=>{if(l&&h){const d=p(l);s(d)}},[l,h]);const{validateFormData:n}=be(),{mutate:x,isPending:t}=Ne(r.storeUid||"temp",{onSuccess:()=>{c({to:"/sale-channel/discount"})},onError:()=>{}}),{mutate:g,isPending:N}=Ce({onSuccess:()=>{c({to:"/sale-channel/discount"})},onError:()=>{}}),P=()=>{c({to:"/sale-channel/discount"})},U=()=>{const d=r.marketingDays.length>0?C.convertDayStringsToNumbers(r.marketingDays):[0,1,2,3,4,5,6],_=r.marketingHours.length>0?C.convertHourStringsToNumbers(r.marketingHours):C.getAllHours();return{discountType:r.discountType,discountValue:r.discountType==="PERCENT"?r.discountPercentage/100:r.discountAmount,fromDate:o(r.startDate),toDate:o(r.endDate),selectedDays:d,selectedHours:_,saleChannelUid:r.channelUid,promotionUid:r.promotionUid||"1576be99-992c-4085-a68c-105f7fbf0fff",filterState:r.filterState}},V=async()=>{if(M)if(h&&l){const d=U(),_=Te(d,l);g(_)}else{const d=U();if(n(d).length>0)return;x(d)}},q=d=>{s(_=>({..._,...d}))},M=r.storeUid!==""&&r.channelUid!==""&&(r.discountType==="PERCENT"?r.discountPercentage>0:r.discountAmount>0);return{formData:r,updateFormData:q,handleBack:P,handleSave:V,isFormValid:M,isLoading:t||N||u,isEditMode:h}}function Me({storeUid:a}){const{selectedBrand:m}=ue(),{company:c}=he(),{data:h=[],isLoading:l}=Ae({companyUid:c==null?void 0:c.id,brandUid:m==null?void 0:m.id,storeUid:a,partnerConfig:1,skipLimit:!0});return{salesChannels:h,isLoadingChannels:l}}function Ke(){var i;const{formData:a,updateFormData:m}=O(),{isEditMode:c}=xe(),{currentBrandStores:h}=je(),{salesChannels:l,isLoadingChannels:u}=Me({storeUid:a.storeUid}),{createPromotion:r}=Se(),s=async o=>{m({channelUid:o});const p=l.find(n=>n.id===o);p&&a.storeUid&&await r({storeUid:a.storeUid,channelUid:o,channelName:p.sourceName})};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin giảm giá"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(se,{value:a.storeUid,onValueChange:o=>{m({storeUid:o,channelUid:""})},disabled:c,children:[e.jsx(te,{className:"flex-1",children:e.jsx(ae,{placeholder:"Chọn cửa hàng"})}),e.jsx(ne,{children:h.map(o=>e.jsx(ie,{value:o.id,children:o.store_name},o.id))})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:["Kênh ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(se,{value:a.channelUid,onValueChange:s,disabled:!a.storeUid||u||c,children:[e.jsx(te,{className:"flex-1",children:e.jsx(ae,{placeholder:a.storeUid?u?"Đang tải...":"Chọn kênh":"Chọn cửa hàng trước"})}),e.jsx(ne,{children:l.map(o=>e.jsx(ie,{value:o.id,children:o.sourceName},o.id))})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:["Chương trình khuyến mãi ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(W,{value:c&&a.promotionName?a.promotionName:a.channelUid&&l.length>0?`CTKM tự động theo kênh ${((i=l.find(o=>o.id===a.channelUid))==null?void 0:i.sourceName)||""}`:"CTKM được tự động tạo theo kênh",disabled:!0,className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:[a.discountType==="PERCENT"?"Phần trăm giảm giá":"Số tiền giảm giá"," ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"flex flex-1 gap-2",children:[e.jsx(W,{type:"number",min:"0",max:a.discountType==="PERCENT"?"100":void 0,value:a.discountType==="PERCENT"?a.discountPercentage||"":a.discountAmount||"",onChange:o=>{const p=Number(o.target.value);if(a.discountType==="PERCENT"){const n=p>100?100:p;m({discountPercentage:n})}else m({discountAmount:p})},placeholder:"0",className:"flex-1"}),e.jsx(Ee,{value:a.discountType,onValueChange:o=>m({discountType:o}),className:"w-auto",children:e.jsxs(Fe,{className:"grid w-fit grid-cols-2",children:[e.jsx(le,{value:"PERCENT",children:"%"}),e.jsx(le,{value:"AMOUNT",children:"đ"})]})})]})]})]})}function Ie({itemTypes:a,selectedItems:m,searchTerm:c,onItemToggle:h}){const[l,u]=y.useState(!1),[r,s]=y.useState(!1),i=(Array.isArray(a)?a:[]).filter(n=>{const x=n.item_type_name||n.name||"";return x.toLowerCase()!=="uncategory"&&x.toLowerCase().includes(c.toLowerCase())}),o=i.filter(n=>m.includes(n.item_type_id||n.id)),p=i.filter(n=>!m.includes(n.item_type_id||n.id));return e.jsxs("div",{className:"space-y-4",children:[e.jsxs(k,{open:!l,onOpenChange:n=>u(!n),children:[e.jsx(E,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",o.length,")"]}),l&&e.jsx(A,{className:"h-4 w-4"}),!l&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(F,{className:"max-h-40 space-y-2 overflow-y-auto",children:[o.map(n=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${n.active===0?"opacity-50":""}`,children:[e.jsx(T,{checked:!0,onCheckedChange:()=>h(n.item_type_id||n.id),disabled:n.active===0}),e.jsx("span",{className:"text-sm",children:n.item_type_name||n.name||"Không có tên"})]},n.id)),o.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn nhóm nào"})]})]}),e.jsxs(k,{open:!r,onOpenChange:n=>s(!n),children:[e.jsx(E,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",p.length,")"]}),r&&e.jsx(A,{className:"h-4 w-4"}),!r&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(F,{className:"max-h-40 space-y-2 overflow-y-auto",children:[p.map(n=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${n.active===0?"opacity-50":""}`,children:[e.jsx(T,{checked:!1,onCheckedChange:()=>h(n.item_type_id||n.id),disabled:n.active===0}),e.jsx("span",{className:"text-sm",children:n.item_type_name||n.name||"Không có tên"})]},n.id)),p.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có nhóm nào"})]})]})]})}function Re({items:a,selectedItems:m,searchTerm:c,isLoading:h,onItemToggle:l}){const[u,r]=y.useState(!1),[s,i]=y.useState(!1),p=(Array.isArray(a)?a:[]).filter(t=>{const g=(t==null?void 0:t.item_name)||(t==null?void 0:t.name)||"";return t.active!==0&&g.toLowerCase().includes(c.toLowerCase())}).map(t=>({...t,name:t.item_name||t.name||"Không có tên"})),n=p.filter(t=>m.includes(t.item_id||t.id)),x=p.filter(t=>!m.includes(t.item_id||t.id));return h?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(k,{open:!u,onOpenChange:t=>r(!t),children:[e.jsx(E,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",n.length,")"]}),u&&e.jsx(A,{className:"h-4 w-4"}),!u&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(F,{className:"max-h-40 space-y-2 overflow-y-auto",children:[n.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(T,{checked:!0,onCheckedChange:()=>l(t.item_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),n.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn món ăn nào"})]})]}),e.jsxs(k,{open:!s,onOpenChange:t=>i(!t),children:[e.jsx(E,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",x.length,")"]}),s&&e.jsx(A,{className:"h-4 w-4"}),!s&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(F,{className:"max-h-40 space-y-2 overflow-y-auto",children:[x.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(T,{checked:!1,onCheckedChange:()=>l(t.item_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),x.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có món ăn nào"})]})]})]})}function He({packages:a,selectedItems:m,searchTerm:c,isLoading:h,onItemToggle:l}){const[u,r]=y.useState(!1),[s,i]=y.useState(!1),p=(Array.isArray(a)?a:[]).filter(t=>{const N=((t==null?void 0:t.package_name)||(t==null?void 0:t.name)||"").toLowerCase().includes(c.toLowerCase());return t.active!==0&&N}).map(t=>({...t,name:t.package_name||t.name||"Không có tên"})),n=p.filter(t=>m.includes(t.package_id||t.id)),x=p.filter(t=>!m.includes(t.package_id||t.id));return h?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(k,{open:!u,onOpenChange:t=>r(!t),children:[e.jsx(E,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",n.length,")"]}),u&&e.jsx(A,{className:"h-4 w-4"}),!u&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(F,{className:"max-h-40 space-y-2 overflow-y-auto",children:[n.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(T,{checked:!0,onCheckedChange:()=>l(t.package_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),n.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn combo nào"})]})]}),e.jsxs(k,{open:!s,onOpenChange:t=>i(!t),children:[e.jsx(E,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",x.length,")"]}),s&&e.jsx(A,{className:"h-4 w-4"}),!s&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(F,{className:"max-h-40 space-y-2 overflow-y-auto",children:[x.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(T,{checked:!1,onCheckedChange:()=>l(t.package_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),x.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có combo nào"})]})]})]})}function $e({open:a,onOpenChange:m,storeUid:c,onSave:h,initialApplyToAll:l=!1,initialActiveFilter:u=null,initialSelectedItems:r=[]}){const{selectedBrand:s}=ue(),{company:i}=he(),[o,p]=y.useState(""),[n,x]=y.useState(!1),[t,g]=y.useState(null),[N,P]=y.useState([]),{data:U=[],isLoading:V}=Q({queryKey:[G.ITEM_TYPES,i==null?void 0:i.id,s==null?void 0:s.id,c],queryFn:async()=>!(i!=null&&i.id)||!(s!=null&&s.id)||!c?[]:(await Y.get(`/mdata/v1/item-types?skip_limit=true&company_uid=${i.id}&brand_uid=${s.id}&store_uid=${c}`)).data.data||[],enabled:a&&!!(i!=null&&i.id)&&!!(s!=null&&s.id)&&!!c}),{data:q=[],isLoading:M}=Q({queryKey:[G.ITEMS,i==null?void 0:i.id,s==null?void 0:s.id,c],queryFn:async()=>!(i!=null&&i.id)||!(s!=null&&s.id)||!c?[]:(await Y.get(`/mdata/v1/items?skip_limit=true&company_uid=${i.id}&brand_uid=${s.id}&list_store_uid=${c}`)).data.data||[],enabled:a&&!!(i!=null&&i.id)&&!!(s!=null&&s.id)&&!!c}),{data:d=[],isLoading:_}=Q({queryKey:[G.PACKAGES,i==null?void 0:i.id,s==null?void 0:s.id,c],queryFn:async()=>!(i!=null&&i.id)||!(s!=null&&s.id)||!c?[]:(await Y.get(`/mdata/v1/packages?skip_limit=true&company_uid=${i.id}&brand_uid=${s.id}&store_uid=${c}`)).data.data||[],enabled:a&&!!(i!=null&&i.id)&&!!(s!=null&&s.id)&&!!c}),b=V||M||_;y.useEffect(()=>{a&&(p(""),x(l),g(u),P(r))},[a,l,u,r]);const z=j=>{x(j),j&&g(null)},L=j=>{x(!1),g(D=>{const R=D===j?null:j;return R!==D&&P([]),R})},S=j=>{P(D=>D.includes(j)?D.filter(R=>R!==j):[...D,j])},K=()=>{m(!1)},I=()=>{n?h(["all"],!0,null):h(N,!1,t),m(!1)};return e.jsx(Pe,{title:"Áp dụng cho",open:a,onOpenChange:m,onCancel:K,onConfirm:I,cancelText:"Hủy",confirmText:"Lưu",maxWidth:"sm:max-w-2xl",isLoading:b,confirmDisabled:!n&&N.length===0,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(T,{id:"apply-all",checked:n,onCheckedChange:z}),e.jsx(v,{htmlFor:"apply-all",className:"text-sm font-medium",children:"Áp dụng cho tất cả món và nhóm"})]}),e.jsx(W,{placeholder:"Tìm kiếm",value:o,onChange:j=>p(j.target.value),className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(v,{className:"text-sm font-medium",children:"Áp dụng cho"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(f,{type:"button",variant:t==="groups"?"default":"outline",size:"sm",onClick:()=>L("groups"),children:"Nhóm"}),e.jsx(f,{type:"button",variant:t==="items"?"default":"outline",size:"sm",onClick:()=>L("items"),children:"Món ăn"}),e.jsx(f,{type:"button",variant:t==="packages"?"default":"outline",size:"sm",onClick:()=>L("packages"),children:"Combo"})]})]}),!n&&e.jsxs("div",{className:"space-y-2",children:[t==="groups"&&e.jsx(Ie,{itemTypes:U,selectedItems:N,searchTerm:o,onItemToggle:S}),t==="items"&&e.jsx(Re,{items:q,selectedItems:N,searchTerm:o,isLoading:b,onItemToggle:S}),t==="packages"&&e.jsx(He,{packages:d,selectedItems:N,searchTerm:o,isLoading:b,onItemToggle:S})]})]})})}function Oe(){var p;const{formData:a,updateFormData:m}=O(),[c,h]=y.useState(!1),l=()=>{h(!0)},u=(n,x,t)=>{const g={...a.filterState,is_all:x?1:0,is_type:0,is_item:0,is_combo:0,type_id:"",item_id:"",combo_id:""};!x&&n.length>0&&t&&(t==="groups"?(g.is_type=1,g.type_id=n.join(",")):t==="items"?(g.is_item=1,g.item_id=n.join(",")):t==="packages"&&(g.is_combo=1,g.combo_id=n.join(","))),m({filterState:g})},r=((p=a.filterState)==null?void 0:p.is_all)===1,s=()=>{var n,x,t;return((n=a.filterState)==null?void 0:n.is_type)===1?"groups":((x=a.filterState)==null?void 0:x.is_item)===1?"items":((t=a.filterState)==null?void 0:t.is_combo)===1?"packages":null},i=()=>{var n,x,t;return((n=a.filterState)==null?void 0:n.is_type)===1&&a.filterState.type_id?a.filterState.type_id.split(",").filter(g=>g.trim()):((x=a.filterState)==null?void 0:x.is_item)===1&&a.filterState.item_id?a.filterState.item_id.split(",").filter(g=>g.trim()):((t=a.filterState)==null?void 0:t.is_combo)===1&&a.filterState.combo_id?a.filterState.combo_id.split(",").filter(g=>g.trim()):[]},o=()=>{if(r)return"Áp dụng cho tất cả";const n=i();if(n.length>0){const x=s(),t=x==="groups"?"nhóm":x==="items"?"món":"combo";return`${n.length} ${t}`}return"Thêm"};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Tuỳ chỉnh"}),e.jsx("div",{className:"mb-4 text-sm text-gray-600",children:"Áp dụng giảm giá tự động cho các món hoặc nhóm món, combo cụ thể"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:["Áp dụng cho ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(f,{type:"button",variant:"outline",onClick:l,disabled:!a.storeUid,className:"flex-1 justify-start",children:o()})]}),e.jsx($e,{open:c,onOpenChange:h,storeUid:a.storeUid,onSave:u,initialApplyToAll:r,initialActiveFilter:s(),initialSelectedItems:i()})]})}function Ve(){const{formData:a,updateFormData:m}=O(),c=new Date;c.setHours(0,0,0,0);const h=a.startDate?new Date(a.startDate):void 0,l=a.endDate?new Date(a.endDate):void 0,u=s=>{if(s){const i=$(s,"yyyy-MM-dd");m({startDate:i})}},r=s=>{if(s){const i=$(s,"yyyy-MM-dd");m({endDate:i})}};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Ngày áp dụng"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(v,{className:"min-w-[120px] text-sm font-medium",children:"Ngày bắt đầu"}),e.jsxs(ce,{children:[e.jsx(re,{asChild:!0,children:e.jsxs(f,{variant:"outline",className:X("flex-1 justify-start text-left font-normal",!h&&"text-muted-foreground"),children:[e.jsx(de,{className:"mr-2 h-4 w-4"}),h?$(h,"dd/MM/yyyy",{locale:H}):"Chọn ngày bắt đầu"]})}),e.jsx(oe,{className:"w-auto p-0",align:"start",children:e.jsx(ee,{mode:"single",selected:h,onSelect:u,disabled:s=>s>c,initialFocus:!0,locale:H})})]})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(v,{className:"min-w-[120px] text-sm font-medium",children:"Ngày kết thúc"}),e.jsxs(ce,{children:[e.jsx(re,{asChild:!0,children:e.jsxs(f,{variant:"outline",className:X("flex-1 justify-start text-left font-normal",!l&&"text-muted-foreground"),children:[e.jsx(de,{className:"mr-2 h-4 w-4"}),l?$(l,"dd/MM/yyyy",{locale:H}):"Chọn ngày kết thúc"]})}),e.jsx(oe,{className:"w-auto p-0",align:"start",children:e.jsx(ee,{mode:"single",selected:l,onSelect:r,disabled:s=>s<c,initialFocus:!0,locale:H})})]})]})]})]})}const qe=[{label:"T2",value:"0"},{label:"T3",value:"1"},{label:"T4",value:"2"},{label:"T5",value:"3"},{label:"T6",value:"4"},{label:"T7",value:"5"},{label:"CN",value:"6"}],ze=[{value:"0"},{value:"1"},{value:"2"},{value:"3"},{value:"4"},{value:"5"},{value:"6"},{value:"7"},{value:"8"},{value:"9"},{value:"10"},{value:"11"},{value:"12"},{value:"13"},{value:"14"},{value:"15"},{value:"16"},{value:"17"},{value:"18"},{value:"19"},{value:"20"},{value:"21"},{value:"22"},{value:"23"}];function Ye(){const{formData:a,updateFormData:m}=O(),c=l=>{const u=a.marketingDays||[],r=u.includes(l)?u.filter(s=>s!==l):[...u,l];m({marketingDays:r})},h=l=>{const u=a.marketingHours||[],r=u.includes(l)?u.filter(s=>s!==l):[...u,l];m({marketingHours:r})};return e.jsx(ye,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Khung thời gian áp dụng"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{className:"text-sm font-medium text-gray-500",children:"Chọn ngày"}),e.jsxs(J,{children:[e.jsx(Z,{asChild:!0,children:e.jsx(me,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(B,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần"})})]})]}),e.jsx("div",{className:"flex gap-2",children:qe.map(l=>{var u;return e.jsx(f,{type:"button",variant:(u=a.marketingDays)!=null&&u.includes(l.value)?"default":"outline",size:"sm",onClick:()=>c(l.value),className:"flex-1",children:l.label},l.value)})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{className:"text-sm font-medium text-gray-500",children:"Chọn giờ"}),e.jsxs(J,{children:[e.jsx(Z,{asChild:!0,children:e.jsx(me,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(B,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày"})})]})]}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:ze.map(l=>{var u;return e.jsxs(f,{type:"button",variant:(u=a.marketingHours)!=null&&u.includes(l.value)?"default":"outline",size:"sm",onClick:()=>h(l.value),className:"text-xs",children:[l.value,":00"]},l.value)})})]})]})})}function js({discountId:a,storeUid:m}={}){const c=Ue({discountId:a,initialStoreUid:m});return e.jsx(De,{value:c,children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Le,{}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(Ke,{}),e.jsx(Oe,{}),e.jsx(Ve,{}),e.jsx(Ye,{})]})})})]})})}export{js as D};
