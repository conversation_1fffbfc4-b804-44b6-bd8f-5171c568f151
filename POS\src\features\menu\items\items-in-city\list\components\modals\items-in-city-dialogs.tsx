import { ConfirmDialog } from '@/components/confirm-dialog'
import { useCitiesData } from '@/hooks/api'

import { useItemsInCity } from '../../../context'
import { useDeleteItemInCity } from '../../../hooks'
import { ExportDialog } from './export-dialog'
import { ImportDialog } from './import-dialog'
import { PriceBySourceConfigModal } from './price-by-source-config-modal'
import { SortMenuModal } from './sort-menu-modal'

export function ItemsInCityDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useItemsInCity()
  const { deleteItemAsync } = useDeleteItemInCity()
  const { data: citiesData = [] } = useCitiesData()

  return (
    <>
      <ExportDialog open={open === 'export-dialog'} onOpenChange={() => setOpen(null)} />
      <ImportDialog />
      <SortMenuModal open={open === 'sort-menu'} onOpenChange={() => setOpen(null)} />
      <PriceBySourceConfigModal
        open={open === 'price-by-source-config'}
        onOpenChange={() => setOpen(null)}
        cities={citiesData}
      />

      {currentRow && (
        <>
          <ConfirmDialog
            key='quantity-day-delete'
            destructive
            open={open === 'delete'}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            handleConfirm={async () => {
              setOpen(null)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
              await deleteItemAsync(currentRow.id || '')
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá ?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}
    </>
  )
}
