import{r as h,a as O,j as e,B as m}from"./index-D0Grd55b.js";import"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import{u as A}from"./use-items-DCy2fVIC.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import"./date-range-picker-CruKYeHR.js";import{L as r}from"./form-Bk1C9kLO.js";import{I as L}from"./input-C-0UnKOB.js";import{S as T,a as R,b as k,c as F,d as E}from"./select-DBO-8fSu.js";import{u as q,a as D,b as B,c as V,C as _,A as P,I as X}from"./use-modal-state-D3jU2Tn7.js";import{X as $}from"./calendar-5lpy20z0.js";function ae(){const a=q(),s=D(),n=B({onConfirm:t=>s.setMenuItems(t)}),i=V(),[p,c]=h.useState(!1),[o,j]=h.useState(new Set),{currentBrandStores:g}=O(),{data:l=[],isLoading:u}=A({params:{list_store_uid:a.selectedStoreId,skip_limit:!0,active:1},enabled:!!a.selectedStoreId}),S=()=>{s.handleCreateGroup(),i.setCreateGroupModalOpen(!0)},I=t=>{s.handleEditGroup(t),i.setCreateGroupModalOpen(!0)},x=()=>{i.handleCloseModal(),s.resetGroupForm(),n.resetSelection()},M=()=>{i.handleCloseAddItemModal(),n.resetSelection()},N=()=>{n.handleConfirmMenuItems(l),i.handleCloseAddItemModal()},f=()=>{n.setSelectedMenuItemsFromGroup(s.menuItems),i.handleAddMenuItem(a.selectedStoreId)},C=()=>{s.handleSaveGroup(l)&&x()},v=async()=>{await a.handleSave(s.customizationGroups,o,l)},z=()=>{c(!0)},y=t=>{j(new Set(t)),c(!1)},G=()=>o.size===0?"Chọn món áp dụng":`${o.size} món`,w=n.getSelectedMenuItemsList(l),b=n.getRemainingMenuItemsList(l);return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(m,{variant:"ghost",size:"sm",onClick:a.handleBack,className:"flex items-center",children:e.jsx($,{className:"h-4 w-4"})}),e.jsx(m,{type:"button",disabled:a.isSubmitting||!a.isFormValid,className:"min-w-[100px]",onClick:v,children:a.isSubmitting?"Đang tạo...":"Lưu"})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:"Tạo customization"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(r,{htmlFor:"customization-name",className:"min-w-[200px] text-sm font-medium",children:["Tên customization ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(L,{id:"customization-name",value:a.customizationName,onChange:t=>a.setCustomizationName(t.target.value),placeholder:"Nhập tên customization",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(r,{htmlFor:"store-select",className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(T,{value:a.selectedStoreId,onValueChange:a.setSelectedStoreId,children:[e.jsx(R,{className:"flex-1",children:e.jsx(k,{placeholder:"Chọn cửa hàng"})}),e.jsx(F,{children:g.map(t=>e.jsx(E,{value:t.id,children:t.store_name},t.id))})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Áp dụng customization cho món"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Chọn các món mà customization này sẽ áp dụng"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(r,{className:"min-w-[200px] text-sm font-medium",children:"Món ăn"}),e.jsx("div",{className:"flex-1",children:e.jsx(m,{type:"button",variant:"outline",onClick:z,className:"w-full justify-start text-left",disabled:u,children:u?"Đang tải...":G()})})]})]}),e.jsx("div",{className:"flex justify-center pt-4",children:e.jsx(m,{onClick:S,children:"Tạo nhóm"})}),s.customizationGroups.length>0&&e.jsxs("div",{className:"space-y-6 pt-6",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Danh sách nhóm đã tạo"}),s.customizationGroups.map(t=>e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:t.name}),e.jsxs("span",{className:"ml-2 text-sm text-gray-500",children:["(Chọn từ ",t.minRequired," đến ",t.maxAllowed," món)"]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(m,{variant:"outline",size:"sm",onClick:()=>I(t.id),children:"Sửa"}),e.jsx(m,{variant:"outline",size:"sm",onClick:()=>s.handleDeleteGroup(t.id),className:"text-red-600 hover:text-red-700",children:"Xóa"})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4",children:t.items.map(d=>e.jsxs("div",{className:"rounded-md border bg-gray-50 p-3 text-center",children:[e.jsx("p",{className:"text-sm font-medium",children:d.name}),e.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:["(",d.code,")"]}),e.jsxs("p",{className:"mt-1 text-sm font-medium text-green-600",children:[d.price.toLocaleString("vi-VN")," ₫"]})]},d.id))})]},t.id))]})]})})}),e.jsx(_,{open:i.createGroupModalOpen,onOpenChange:i.setCreateGroupModalOpen,onCancel:x,onConfirm:C,onAddMenuItem:f,isEditing:s.isEditing,customizationName:a.customizationName,groupName:s.groupName,setGroupName:s.setGroupName,minRequired:s.minRequired,setMinRequired:s.setMinRequired,maxAllowed:s.maxAllowed,setMaxAllowed:s.setMaxAllowed,menuItems:s.menuItems}),e.jsx(P,{open:i.addItemModalOpen,onOpenChange:i.setAddItemModalOpen,onCancel:M,onConfirm:N,menuItemSearchTerm:n.menuItemSearchTerm,setMenuItemSearchTerm:n.setMenuItemSearchTerm,selectedMenuSectionOpen:n.selectedMenuSectionOpen,setSelectedMenuSectionOpen:n.setSelectedMenuSectionOpen,remainingMenuSectionOpen:n.remainingMenuSectionOpen,setRemainingMenuSectionOpen:n.setRemainingMenuSectionOpen,selectedMenuItems:n.selectedMenuItems,handleMenuItemToggle:n.handleMenuItemToggle,selectedMenuItemsList:w,remainingMenuItemsList:b}),e.jsx(X,{open:p,onOpenChange:c,items:l,selectedItems:Array.from(o),onItemsSelected:y})]})}export{ae as C};
