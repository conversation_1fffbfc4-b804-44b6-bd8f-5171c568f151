import{aA as o,j as t}from"./index-D0Grd55b.js";import{I as p}from"./item-detail-form-DRVIL4YU.js";import"./form-Bk1C9kLO.js";import"./exceljs.min-BlLyHhym.js";import"./pos-api-B09qRspF.js";import{m as e}from"./customization-dialog-87EDwGFq.js";import"./user-CN3Oxfhq.js";import"./vietqr-api-C42F6d9k.js";import"./crm-api-Bz_HnEz3.js";import"./header-yApU5MZq.js";import"./main-Czv3HpP4.js";import"./search-context-CjM0jrYw.js";import"./date-range-picker-CruKYeHR.js";import"./multi-select-OhaHZZzq.js";import"./core.esm-ZfuNvihZ.js";import"./sortable.esm-B4HpXahQ.js";import"./zod-G2vIgQkk.js";import"./use-upload-image-CdfDSGPy.js";import"./images-api-DS53jPy5.js";import"./use-item-types-CuyGAtwR.js";import"./useQuery-Ck3BpOfq.js";import"./utils-km2FGkQ4.js";import"./useMutation-ATsU-ht7.js";import"./query-keys-3lmd-xp6.js";import"./use-item-classes-s7fFgXey.js";import"./use-units-ByB1meVo.js";import"./use-items-DCy2fVIC.js";import"./item-api-1Lw2G9JI.js";import"./use-removed-items-7Ty-LinJ.js";import"./use-customizations-DtrTtBD6.js";import"./use-customization-by-id-CsibWRdD.js";import"./use-sources-CEkoU4p4.js";import"./sources-api-DecL-FPl.js";import"./sources-CfiQ7039.js";import"./calendar-5lpy20z0.js";import"./createLucideIcon-DNzDbUBG.js";import"./index-CW7Xpojs.js";import"./isSameMonth-C8JQo-AN.js";import"./checkbox-C_eqgJmW.js";import"./index-C-UyCxtf.js";import"./check-TFQPNqMS.js";import"./input-C-0UnKOB.js";import"./textarea-CTnz5I3-.js";import"./combobox-NkstXop_.js";import"./command-Bcq7GTcy.js";import"./dialog-C8IVKkOo.js";import"./search-C4dfJjaw.js";import"./popover-CMTiAV3j.js";import"./chevrons-up-down-BCCoC0zx.js";import"./upload-9hlzyje0.js";import"./collapsible-MezBM5sx.js";import"./confirm-dialog-BctmuKmX.js";import"./alert-dialog-B23_tn7-.js";import"./date-picker-CnBJ_htZ.js";import"./calendar-Bxala8X3.js";import"./circle-help-CEmvpE3g.js";import"./select-DBO-8fSu.js";import"./index-CI2TkimM.js";import"./chevron-right-D2H_xC9V.js";import"./use-dialog-state-Bi2BGkq6.js";import"./modal-hDollbt0.js";import"./separator-CiUiq7rT.js";import"./createReactComponent-eJgt86Cn.js";import"./scroll-area-r2ikcXUQ.js";import"./IconChevronRight-DnVUSvDn.js";import"./react-icons.esm-BOnXQgjl.js";import"./badge-DoEZpO1Y.js";import"./circle-x-Izs54kGD.js";const It=function(){const{id:r}=o({strict:!1}),{data:i,isLoading:m}=e(r,!!r);return m?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):i!=null&&i.data?t.jsx(p,{currentRow:i.data}):t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Không tìm thấy món ăn"})})})};export{It as component};
