import{u as x}from"./useQuery-CCx3k6lE.js";import{u as b,a3 as g,a4 as l}from"./index-DZ2N7iEN.js";import{u as m}from"./useMutation-zADCXE7W.js";import{a as _}from"./pos-api-PZMeNc3U.js";import{Q as p}from"./query-keys-3lmd-xp6.js";const S=()=>{const e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";let a="AREA-";for(let n=0;n<4;n++)a+=e.charAt(Math.floor(Math.random()*e.length));return a},y={getTablesList:async e=>{const n=`/pos/v1/table?${new URLSearchParams({company_uid:e.company_uid,brand_uid:e.brand_uid,store_uid:e.store_uid,...e.skip_limit?{skip_limit:"true"}:{page:(e.page||1).toString()},...e.limit&&!e.skip_limit&&{limit:e.limit.toString()}}).toString()}`;return(await _.get(n)).data},updateTable:async e=>{var t,r,i;const a={id:e.id,table_name:e.table_name,store_uid:e.store_uid,company_uid:e.company_uid,brand_uid:e.brand_uid,source_id:e.source_id,area_uid:e.area_uid,sort:e.sort,extra_data:{order_list:((t=e.extra_data)==null?void 0:t.order_list)||[],color:((r=e.extra_data)==null?void 0:r.color)||"",font_size:((i=e.extra_data)==null?void 0:i.font_size)||"15"},description:e.description},n=await _.post("/pos/v1/table",[a],{headers:{Accept:"application/json, text/plain, */*","Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return n.data.data||n.data},getTableById:async(e,a,n,t)=>{const r=new URLSearchParams({company_uid:a,brand_uid:n,store_uid:t,id:e}),i=await _.get(`/pos/v1/table?${r.toString()}`);return i.data.data||i.data},deleteTables:async e=>(await _.delete("/pos/v1/table",{data:e})).data,createTable:async e=>{const a=await _.post("/pos/v1/table",e);return a.data.data||a.data},bulkImportTables:async e=>{const a=await _.post("/pos/v1/area",e);return a.data.data||a.data},bulkCreateTables:async e=>{var n,t,r;const a=[];for(const i of e.tables){const s=S(),o=S(),d={store_uid:e.store_uid,area_uid:i.area_uid,extra_data:{order_list:((n=i.extra_data)==null?void 0:n.order_list)||[],color:((t=i.extra_data)==null?void 0:t.color)||"",font_size:((r=i.extra_data)==null?void 0:r.font_size)||"15"},table_name:i.table_name,source_id:i.source_id||"",description:i.description||"",sort:a.length+1,company_uid:e.company_uid,brand_uid:e.brand_uid,table_id:s,area_id:o},u=await _.post("/pos/v1/table",d),c=u.data.data||u.data;a.push(c)}return a},bulkUpdateTables:async e=>{const a=e.map(t=>{var r,i,s;return{id:t.id,table_name:t.table_name,store_uid:t.store_uid,company_uid:t.company_uid,brand_uid:t.brand_uid,source_id:t.source_id||"",area_uid:t.area_uid,sort:t.sort,extra_data:{order_list:((r=t.extra_data)==null?void 0:r.order_list)||[],color:((i=t.extra_data)==null?void 0:i.color)||"",font_size:((s=t.extra_data)==null?void 0:s.font_size)||""},description:t.description||""}}),n=await _.post("/pos/v1/table",a,{headers:{Accept:"application/json, text/plain, */*","Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return n.data.data||n.data},getAreasList:async e=>{const a=new URLSearchParams({company_uid:e.company_uid,brand_uid:e.brand_uid,store_uid:e.store_uid,skip_limit:"true",page:(e.page||1).toString(),results_per_page:(e.results_per_page||15e3).toString()});return(await _.get(`/pos/v1/area?${a.toString()}`)).data},deleteTable:async e=>(await _.delete("/pos/v1/table",{data:e})).data,updateTableStatus:async e=>{const a=await _.post("/pos/v1/table",e);return a.data.data||a.data},updateTablePositions:async e=>(await _.post("/pos/v1/table",e)).data},U=(e={})=>{var i;const{company:a,brands:n}=b(s=>s.auth),t=n==null?void 0:n[0],r=x({queryKey:[p.TABLES_LIST,e.storeUid,e.page,e.limit,e.skip_limit],queryFn:async()=>{const s=(a==null?void 0:a.id)||"",o=(t==null?void 0:t.id)||"",d=e.storeUid||"";if(!s||!o||!d)throw new Error("Thiếu thông tin cần thiết");const u={company_uid:s,brand_uid:o,store_uid:d,page:e.page||1,limit:e.limit,skip_limit:e.skip_limit};return await y.getTablesList(u)},enabled:!!(a!=null&&a.id&&(t!=null&&t.id)&&e.storeUid),staleTime:5*60*1e3});return{...r,data:((i=r.data)==null?void 0:i.data)||[]}},C=()=>{const e=g(),{mutate:a,isPending:n}=m({mutationFn:async t=>await y.updateTable(t),onSuccess:()=>{e.invalidateQueries({queryKey:[p.TABLES_LIST]}),e.invalidateQueries({queryKey:[p.TABLES_DETAIL]}),l.success("Cập nhật bàn thành công")},onError:t=>{var i,s;const r=((s=(i=t==null?void 0:t.response)==null?void 0:i.data)==null?void 0:s.message)||"Có lỗi xảy ra khi cập nhật bàn";l.error(r)}});return{updateTable:a,isUpdating:n}},k=()=>{const e=g(),{mutate:a,isPending:n}=m({mutationFn:async t=>{var s,o,d,u,c;const r=t.active===1?0:1,i={id:t.id,table_id:t.table_id,table_name:t.table_name,description:t.description||null,extra_data:{color:((s=t.extra_data)==null?void 0:s.color)||"",font_size:((o=t.extra_data)==null?void 0:o.font_size)||"15",order_list:((d=t.extra_data)==null?void 0:d.order_list)||[]},active:r,revision:t.revision?String(t.revision):null,sort:t.sort,is_fabi:t.is_fabi,source_id:t.source_id||null,area_uid:t.area_uid,store_uid:t.store_uid,brand_uid:t.brand_uid,company_uid:t.company_uid,area_id:t.area_id,store_id:t.store_id||null,brand_id:t.brand_id||null,company_id:t.company_id||null,created_by:t.created_by,updated_by:t.updated_by||t.created_by,created_at:t.created_at,updated_at:t.updated_at,area:{...t.area,extra_data:{background:((c=(u=t.area)==null?void 0:u.extra_data)==null?void 0:c.background)||""}}};return await y.updateTableStatus(i)},onSuccess:t=>{e.invalidateQueries({queryKey:[p.TABLES_LIST]});const r=t.active===1?"kích hoạt":"vô hiệu hóa";l.success(`${r} bàn thành công`)},onError:t=>{var i,s;const r=((s=(i=t==null?void 0:t.response)==null?void 0:i.data)==null?void 0:s.message)||"Có lỗi xảy ra khi cập nhật trạng thái bàn";l.error(r)}});return{toggleTableStatus:a,isToggling:n}},I=()=>{const e=g(),{company:a,brands:n}=b(i=>i.auth),{mutate:t,isPending:r}=m({mutationFn:async i=>{var u;const s=(a==null?void 0:a.id)||"",o=((u=n==null?void 0:n[0])==null?void 0:u.id)||"";if(!s||!o||!i.storeUid)throw new Error("Missing required authentication information");const d={company_uid:s,brand_uid:o,list_id:i.areaIds,store_uid:i.storeUid,area_uid:""};return console.log("Deleting tables:",d),await y.deleteTables(d)},onSuccess:(i,s)=>{e.invalidateQueries({queryKey:[p.TABLES_LIST]});const o=s.areaIds.length,d=o===1?"Xóa bàn thành công":`Xóa ${o} bàn thành công`;l.success(d)},onError:i=>{var o,d;const s=((d=(o=i==null?void 0:i.response)==null?void 0:o.data)==null?void 0:d.message)||"Có lỗi xảy ra khi xóa bàn";l.error(s)}});return{deleteTables:t,isDeleting:r}},D=(e,a)=>{const{company:n,brands:t}=b(i=>i.auth),r=t==null?void 0:t[0];return x({queryKey:[p.TABLES_DETAIL,e,a],queryFn:async()=>{const i=(n==null?void 0:n.id)||"",s=(r==null?void 0:r.id)||"";if(!i||!s||!e||!a)throw new Error("Thiếu thông tin cần thiết");return await y.getTableById(e,i,s,a)},enabled:!!(n!=null&&n.id&&(r!=null&&r.id)&&e&&a),staleTime:5*60*1e3})},w=()=>{const e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";let a="AREA-";for(let n=0;n<4;n++)a+=e.charAt(Math.floor(Math.random()*e.length));return a},P=()=>{const e=g(),{company:a,brands:n}=b(s=>s.auth),t=n==null?void 0:n[0],{mutate:r,isPending:i}=m({mutationFn:async s=>{var h;const o=(a==null?void 0:a.id)||"",d=(t==null?void 0:t.id)||"";if(!o||!d)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");const u=w(),c=w(),f={store_uid:s.store_uid,area_uid:s.area_uid,extra_data:{order_list:((h=s.selectedItems)==null?void 0:h.map(T=>({item_id:T.item_id,quantity:T.quantity})))||[],color:s.color||"",font_size:s.fontSize||"15"},table_name:s.table_name,source_id:s.sourceId||"",description:s.description||"",sort:s.sort||1,company_uid:o,brand_uid:d,table_id:u,area_id:c};return await y.createTable(f)},onSuccess:()=>{e.invalidateQueries({queryKey:[p.TABLES_LIST]}),l.success("Tạo bàn thành công")},onError:s=>{var d,u;const o=((u=(d=s==null?void 0:s.response)==null?void 0:d.data)==null?void 0:u.message)||"Có lỗi xảy ra khi tạo bàn";l.error(o)}});return{createTable:r,isCreating:i}},M=()=>{const e=g(),{company:a,brands:n}=b(s=>s.auth),t=n==null?void 0:n[0],{mutateAsync:r,isPending:i}=m({mutationFn:async s=>{const o=(a==null?void 0:a.id)||"",d=(t==null?void 0:t.id)||"";if(!o||!d)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");const u={store_uid:s.storeUid,company_uid:o,brand_uid:d,tables:s.tables.map(c=>({table_name:c.table_name,area_uid:c.area_uid,description:c.description||"",extra_data:{order_list:[],color:"",font_size:"15"}}))};return await y.bulkCreateTables(u)},onSuccess:()=>{e.invalidateQueries({queryKey:[p.TABLES_LIST]})},onError:s=>{var d,u;const o=((u=(d=s==null?void 0:s.response)==null?void 0:d.data)==null?void 0:u.message)||"Có lỗi xảy ra khi import bàn";l.error(o)}});return{mutateAsync:r,isPending:i}},Q=e=>{const a=g(),{company:n,brands:t}=b(o=>o.auth),r=t==null?void 0:t[0],{mutateAsync:i,isPending:s}=m({mutationFn:async o=>{const d=(n==null?void 0:n.id)||"",u=(r==null?void 0:r.id)||"";if(!d||!u||!e)throw new Error("Thiếu thông tin cần thiết");const c={company_uid:d,brand_uid:u,store_uid:e,list_id:[o]};return await y.deleteTable(c)},onSuccess:()=>{a.invalidateQueries({queryKey:[p.TABLES_LIST]}),l.success("Xóa bàn thành công!")},onError:o=>{var u,c;const d=((c=(u=o==null?void 0:o.response)==null?void 0:u.data)==null?void 0:c.message)||"Có lỗi xảy ra khi xóa bàn";l.error(d)}});return{deleteTable:i,isDeleting:s}},z=()=>{const e=g(),{mutateAsync:a,isPending:n}=m({mutationFn:async t=>await y.bulkUpdateTables(t.tables),onSuccess:()=>{e.invalidateQueries({queryKey:[p.TABLES_LIST]})},onError:t=>{var i,s;const r=((s=(i=t==null?void 0:t.response)==null?void 0:i.data)==null?void 0:s.message)||"Có lỗi xảy ra khi cập nhật bàn";l.error(r)}});return{mutateAsync:a,isPending:n}};export{I as a,k as b,Q as c,M as d,z as e,P as f,C as g,D as h,y as t,U as u};
