const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/excel-export-DcxXieA9.js","assets/exceljs.min-CLCFqwcY.js","assets/index-DZ2N7iEN.js","assets/index-CVkaVZVo.css"])))=>i.map(i=>d[i]);
import{r as m,a as Se,R as Y,j as e,ay as Ve,B as re,c as De,o as Ue,p as Ge,q as We}from"./index-DZ2N7iEN.js";import{v as z,D as Xe}from"./date-range-picker-extpnOqj.js";import"./form-CUvXDg29.js";import"./user-B3TbfMPQ.js";import{s as de}from"./sale-sources-api-BXcDzcIc.js";import{u as be}from"./use-pos-data-DvaAesJs.js";import{f as J}from"./isSameMonth-C8JQo-AN.js";import{u as Ye}from"./use-pos-cities-data-Dv0QkUF3.js";import{C as ze}from"./combobox-B_tJR0V_.js";import{B as oe}from"./badge-5IdLxoVq.js";import{C as Me}from"./checkbox-Bqls14Dj.js";import{C as Je,a as Qe,b as Ze}from"./collapsible-Dk62AUDY.js";import{P as et,a as tt,b as st}from"./popover-BK8t3srL.js";import{C as ve,f as nt}from"./select-B60YVMMU.js";import{D as Ie}from"./download-BL_XCcwW.js";import{C as me,a as ue,b as he,c as xe,d as ge}from"./chart-BnLAIGkg.js";import{C as Z,a as te,b as se,d as ne}from"./card-CNcDcBw_.js";import{L as fe}from"./LineChart-CmvkcaWp.js";import{C as _e}from"./CartesianGrid-B9FcF9d1.js";import{X as pe,Y as je}from"./generateCategoricalChart-Wqzmb34j.js";import{L as Ne}from"./Line-CsCquKIv.js";import{T as Te,a as Le,b as Q,c as S,d as Ee,e as _}from"./table-DQgmazAN.js";import{D as Ae,a as Pe,b as Fe,c as $e}from"./dialog-DDb7K8l1.js";import{I as at}from"./input-DpObCffE.js";import"./crm-api-DyqsDFNF.js";import{b as He}from"./pos-api-PZMeNc3U.js";import{u as Be}from"./useQuery-CCx3k6lE.js";import{C as rt}from"./clock-H9kN5N-j.js";import"./calendar-CoHe9sRq.js";import"./createLucideIcon-2r9cCEY3.js";import"./index-CC24pdSB.js";import"./chevron-right-D-SI6PAy.js";import"./react-icons.esm-Ck8h5xF8.js";import"./use-auth-qNRX4JC8.js";import"./useMutation-zADCXE7W.js";import"./utils-km2FGkQ4.js";import"./command-By9h7q-C.js";import"./search-Cr9zvKK2.js";import"./chevrons-up-down-Cl92_NdW.js";import"./check-CP51yA4o.js";import"./index-DIwSqRjm.js";import"./index-Csh0LmDQ.js";import"./index-Chjiymov.js";const Oe=m.createContext(void 0),ot=({children:r})=>{var Ce;const[y,D]=m.useState(()=>{const o=new Date;return{from:o,to:o}}),[C,p]=m.useState(0),[R,I]=m.useState([]),[O,K]=m.useState([]),[b,F]=m.useState([]),[M,L]=m.useState([]),{company:k,activeBrands:v}=be(),A=(k==null?void 0:k.id)||"",T=((Ce=v[0])==null?void 0:Ce.id)||"",{currentBrandStores:N}=Se(),H=N.map(o=>o.id||o.store_id),a=(()=>{const o=[];return R.forEach(f=>{N.filter(E=>E.city_uid===f).forEach(E=>{const U=E.id||E.store_id;U&&!o.includes(U)&&o.push(U)})}),O.forEach(f=>{N.find(E=>(E.id||E.store_id)===f)&&!o.includes(f)&&o.push(f)}),o})(),x=a.length>0?a:H,s=(()=>{const o=[];return b.forEach(f=>{N.filter(E=>E.city_uid===f).forEach(E=>{const U=E.id||E.store_id;U&&!o.includes(U)&&o.push(U)})}),M.forEach(f=>{N.find(E=>(E.id||E.store_id)===f)&&!o.includes(f)&&o.push(f)}),o})(),{startDate:l,endDate:d}=m.useMemo(()=>{const o=new Date(y.from);o.setHours(0,0,0,0);const f=new Date(y.to);return f.setHours(23,59,59,999),{startDate:o.getTime(),endDate:f.getTime()}},[y.from,y.to,C]),$=()=>p(o=>o+1),q=o=>{const f=typeof o=="function"?o(R):o,j=[...O];f.length===0&&j.length===0?(I([]),K([]),F([]),L([]),p(U=>U+1)):I(f)},t=o=>{const f=typeof o=="function"?o(O):o;[...R].length===0&&f.length===0?(I([]),K([]),F([]),L([]),p(U=>U+1)):K(f)},[n,i]=m.useState([]),[g,h]=m.useState(!1),[c,V]=m.useState([]),[W,G]=m.useState(!1);Y.useEffect(()=>{let o=!0;return(async()=>{if(!A||!T||x.length===0){i([]);return}h(!0);try{const j=await de.getSourcesSummary({companyUid:A,brandUid:T,startDate:l,endDate:d,storeUids:x,byDays:1,limit:100});o&&i(j.data||[])}finally{o&&h(!1)}})(),()=>{o=!1}},[A,T,l,d,x.join(","),C]),Y.useEffect(()=>{let o=!0;return(async()=>{if(!A||!T||s.length===0){V([]);return}G(!0);try{const j=await de.getSourcesSummary({companyUid:A,brandUid:T,startDate:l,endDate:d,storeUids:s,byDays:1,limit:100});o&&V(j.data||[])}finally{o&&G(!1)}})(),()=>{o=!1}},[A,T,l,d,s.join(","),C]);const B=o=>{try{const f=new Date(o);if(!isNaN(f.getTime()))return J(f,"dd/MM",{locale:z});if(/^\d{2}\/\d{2}$/.test(o))return o;const j=o.match(/^(\d{4})-(\d{2})-(\d{2})$/);return j?`${j[3]}/${j[2]}`:o}catch{return o}},X=Y.useMemo(()=>{const o=new Map;return(n||[]).forEach(f=>{(f.list_data||[]).forEach(j=>{const E=o.get(j.date)||{total_amount:0,total_bill:0,discount_amount:0};o.set(j.date,{total_amount:E.total_amount+(j.revenue_net??0),total_bill:E.total_bill+(j.total_bill??0),discount_amount:E.discount_amount+(j.discount_amount??0)})})}),Array.from(o.entries()).sort(([f],[j])=>f<j?-1:f>j?1:0).map(([f,j])=>({date_label:B(f),total_amount:j.total_amount,total_bill:j.total_bill,discount_amount:j.discount_amount}))},[n]),ce=Y.useMemo(()=>{const o=new Map;return(c||[]).forEach(f=>{(f.list_data||[]).forEach(j=>{const E=o.get(j.date)||{total_amount:0,total_bill:0,discount_amount:0};o.set(j.date,{total_amount:E.total_amount+(j.revenue_net??0),total_bill:E.total_bill+(j.total_bill??0),discount_amount:E.discount_amount+(j.discount_amount??0)})})}),Array.from(o.entries()).sort(([f],[j])=>f<j?-1:f>j?1:0).map(([f,j])=>({date_label:B(f),total_amount:j.total_amount,total_bill:j.total_bill,discount_amount:j.discount_amount}))},[c]),le=Y.useMemo(()=>(n||[]).map(o=>({source_id:o.source_id||"",source_name:o.source_name||"",total_bill:o.total_bill||0,revenue_net:o.revenue_net||0,revenue_gross:o.revenue_gross||0,commission_amount:o.commission_amount||0,discount_amount:o.discount_amount||0,partner_marketing_amount:o.partner_marketing_amount||0,peo_count:o.peo_count||0})),[n]),Ke={dateRange:y,setDateRange:D,selectedCities:R,selectedStores:O,setSelectedCities:q,setSelectedStores:t,compareCities:b,compareStores:M,setCompareCities:F,setCompareStores:L,selectedStoreIds:a,compareStoreIds:s,data:X,compareData:ce,rawData:n,compareRawData:c,isLoading:g,isCompareLoading:W,tableRows:le,handleUpdateDateRange:$,handleExport:async o=>{try{const f=o==="selected"?x:H,E=(await de.getSourcesSummary({companyUid:A,brandUid:T,startDate:l,endDate:d,storeUids:f,byDays:1,limit:100})).data||[];if(E.length===0)return;const U=f.map(ee=>{var ke;return(ke=N.find(we=>(we.id||we.store_id)===ee))==null?void 0:ke.store_name}).filter(ee=>!!ee).slice(0,3),{exportSourceRevenueToExcel:qe}=await Ve(async()=>{const{exportSourceRevenueToExcel:ee}=await import("./excel-export-DcxXieA9.js");return{exportSourceRevenueToExcel:ee}},__vite__mapDeps([0,1,2,3]));await qe(E,{dateRange:y,selectedStoreNames:U,filename:"sale-sources.xlsx"})}catch(f){console.error("Export failed:",f)}}};return e.jsx(Oe.Provider,{value:Ke,children:r})},ie=()=>{const r=m.useContext(Oe);if(!r)throw new Error("useSourceContext must be used within SourceProvider");return r},Re=({selectedCities:r,selectedStores:y,onCitiesChange:D,onStoresChange:C,className:p,excludeCities:R=[],excludeStores:I=[]})=>{const[O,K]=m.useState(!1),[b,F]=m.useState(new Set),[M,L]=m.useState("all"),[k,v]=m.useState(""),{cities:A}=Ye(),{currentBrandStores:T}=Se(),N=m.useMemo(()=>A.filter(n=>!R.includes(n.id)),[A,R]),H=m.useMemo(()=>T.filter(n=>!I.includes(n.id||n.store_id)).filter(n=>M==="all"?!0:M==="chain"?n.is_franchise===0:n.is_franchise===1),[T,I,M]),P=n=>H.filter(i=>i.city_uid===n),a=n=>{const i=new Set(b);i.has(n)?i.delete(n):i.add(n),F(i)},x=(n,i)=>{if(i)D([...r,n]);else{D(r.filter(c=>c!==n));const h=P(n).map(c=>c.id||c.store_id);C(y.filter(c=>!h.includes(c)))}},u=(n,i)=>{const g=T.find(c=>(c.id||c.store_id)===n),h=g==null?void 0:g.city_uid;if(i){const c=[...y,n];if(C(c),h){const W=P(h).map(B=>B.id||B.store_id);if(W.every(B=>c.includes(B)||r.includes(h))){const B=c.filter(X=>!W.includes(X));C(B),D([...r,h])}}}else if(h&&r.includes(h)){const W=P(h).map(G=>G.id||G.store_id).filter(G=>G!==n);D(r.filter(G=>G!==h)),C([...y,...W])}else C(y.filter(c=>c!==n))},s=n=>r.includes(n),l=n=>{const g=P(n).map(c=>c.id||c.store_id),h=y.filter(c=>g.includes(c));return h.length>0&&h.length<g.length},d=n=>y.includes(n),$=m.useMemo(()=>{const n=r.reduce((g,h)=>{const c=P(h);return g+c.length},0),i=y.filter(g=>{const h=T.find(V=>(V.id||V.store_id)===g);if(!h)return!0;const c=h.city_uid;return!r.includes(c)}).length;return n+i},[r,y,T,P]),q=$>0?`Đã chọn ${$} cửa hàng`:"Chọn cửa hàng",t=m.useMemo(()=>{const n=k.trim().toLowerCase();return N.some(i=>{const g=i.id||i.city_id,h=P(g);return n?h.some(c=>String(c.store_name||"").toLowerCase().includes(n)):h.length>0})},[N,H,k]);return e.jsxs(et,{open:O,onOpenChange:K,children:[e.jsx(tt,{asChild:!0,children:e.jsxs(re,{variant:"outline",role:"combobox","aria-expanded":O,className:De("w-[320px] justify-between",p),children:[e.jsx("span",{className:"truncate",children:q}),e.jsx(ve,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),e.jsx(st,{className:"w-80 p-0",align:"start",children:e.jsx("div",{className:"max-h-96 overflow-y-auto",children:e.jsxs("div",{className:"p-2",children:[e.jsx("div",{className:"mb-2 flex flex-col items-start justify-center gap-2",children:e.jsx(ze,{options:[{value:"all",label:"Tất cả cửa hàng"},{value:"chain",label:"Chuỗi"},{value:"franchise",label:"Nhượng quyền"}],value:M,onValueChange:n=>L(n||"all"),placeholder:"Loại cửa hàng",searchPlaceholder:"Tìm kiếm...",className:"w-full"})}),!t&&e.jsx("div",{className:"text-muted-foreground flex h-40 items-center justify-center text-lg font-semibold",children:"Không có dữ liệu"}),t&&N.filter(n=>{const i=n.id||n.city_id,g=P(i),h=k.trim().toLowerCase();return h?g.some(c=>String(c.store_name||"").toLowerCase().includes(h)):g.length>0}).map(n=>{const i=n.id||n.city_id,g=k.trim().toLowerCase(),h=P(i),c=g?h.filter(B=>String(B.store_name||"").toLowerCase().includes(g)):h,V=g?!0:b.has(i),W=s(i),G=l(i);return e.jsx("div",{className:"mb-2",children:e.jsxs(Je,{open:V,onOpenChange:()=>a(i),children:[e.jsxs("div",{className:"flex items-center space-x-2 rounded p-2 hover:bg-gray-50",children:[e.jsx(Me,{checked:W,ref:B=>{if(B&&G&&!W){const X=B.querySelector('input[type="checkbox"]');X&&(X.indeterminate=!0)}},onCheckedChange:B=>x(i,B)}),e.jsxs(Qe,{className:"flex flex-1 items-center space-x-2 text-left",children:[e.jsx("span",{className:"font-medium",children:n.city_name}),e.jsxs("span",{className:"text-muted-foreground text-sm",children:["(",c.length," cửa hàng)"]}),e.jsx(ve,{className:De("ml-auto h-4 w-4 transition-transform",V&&"rotate-180 transform")})]})]}),e.jsx(Ze,{children:V&&e.jsx("div",{className:"ml-6 space-y-1",children:c.map(B=>{const X=B.id||B.store_id,ce=d(X)||W;return e.jsxs("div",{className:"flex items-center space-x-2 rounded p-2 hover:bg-gray-50",children:[e.jsx(Me,{checked:ce,onCheckedChange:le=>u(X,le)}),e.jsx("span",{className:"text-sm",children:B.store_name}),typeof B.is_franchise<"u"&&(B.is_franchise===1?e.jsx(oe,{className:"ml-2",children:"Nhượng quyền"}):e.jsx(oe,{className:"ml-2",children:"Chuỗi"}))]},X)})})})]})},i)})]})})})]})},it=()=>{const{dateRange:r,setDateRange:y,selectedCities:D,selectedStores:C,setSelectedCities:p,setSelectedStores:R,compareCities:I,compareStores:O,setCompareCities:K,setCompareStores:b,handleUpdateDateRange:F,handleExport:M}=ie(),L=D.length>0||C.length>0;return e.jsxs("div",{className:"flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between",children:[e.jsxs("div",{className:"flex flex-col gap-4 md:flex-row md:gap-6",children:[e.jsx(Xe,{initialDateFrom:r.from,initialDateTo:r.to,onUpdate:({range:k})=>{k!=null&&k.from&&(k!=null&&k.to)&&(y({from:k.from,to:k.to}),F())},align:"start",locale:"vi-VN"}),e.jsx(Re,{selectedCities:D,selectedStores:C,onCitiesChange:p,onStoresChange:R,className:"h-[40px] min-w-[200px]"}),L&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"text-muted-foreground flex items-center text-sm",children:"so với"}),e.jsx(Re,{selectedCities:I,selectedStores:O,onCitiesChange:K,onStoresChange:b,className:"h-[40px] min-w-[200px]",excludeCities:D,excludeStores:C})]})]}),e.jsxs(re,{variant:"outline",size:"sm",onClick:()=>M("selected"),children:[e.jsx(Ie,{className:"mr-2 h-4 w-4"}),"Xuất báo cáo"]})]})},ye=r=>r>=1e6?Math.round(r/1e6)+"M":r>=1e3?Math.round(r/1e3)+"K":new Intl.NumberFormat("vi-VN").format(r),ct=()=>{const{rawData:r,compareRawData:y,isLoading:D,isCompareLoading:C,dateRange:p,compareCities:R,compareStores:I}=ie(),O=(R==null?void 0:R.length)>0||(I==null?void 0:I.length)>0;if(D)return e.jsxs(Z,{children:[e.jsx(te,{children:e.jsx(se,{children:"Doanh thu bao gồm: Phí ship, VAT"})}),e.jsx(ne,{children:e.jsx("div",{className:"flex h-64 items-center justify-center",children:e.jsx("div",{className:"text-muted-foreground",children:"Đang tải dữ liệu..."})})})]});const K=(a,x)=>a.getFullYear()===x.getFullYear()&&a.getMonth()===x.getMonth()&&a.getDate()===x.getDate(),b=m.useMemo(()=>{const a=new Date(p.from),x=new Date(p.to);return a.setHours(0,0,0,0),x.setHours(0,0,0,0),K(a,x)},[p.from,p.to]),F=m.useMemo(()=>{if(!b)return!1;const a=new Date(p.from),x=new Date;return a.setHours(0,0,0,0),x.setHours(0,0,0,0),K(a,x)},[p.from,b]),M=m.useMemo(()=>{if(!p)return{data:[],series:[]};const a=(r||[]).map(t=>({id:String(t.source_id),name:String(t.source_name||t.source_id)})),x=t=>{const n=new Date(t);if(!Number.isNaN(n.getTime()))return J(n,"dd/MM",{locale:z});const i=String(t).match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/);return i?`${i[3]}/${i[2]}`:String(t)},u=new Map;(r||[]).forEach(t=>{const n=String(t.source_id),i=new Map;(t.list_data||[]).forEach(g=>{const h=x(String(g.date)),c=Number(g.revenue_net??g.total_amount??0);i.set(h,(i.get(h)||0)+c)}),u.set(n,i)});const s=new Date(p.from),l=new Date(p.to);s.setHours(0,0,0,0),l.setHours(0,0,0,0);const d=[];for(let t=new Date(s);t<=l;t.setDate(t.getDate()+1)){const n=J(t,"dd/MM",{locale:z}),i={name:n};a.forEach(g=>{var V;const h=`s_${g.id}`,c=((V=u.get(g.id))==null?void 0:V.get(n))||0;i[h]=c}),d.push(i)}if(d.length===1&&!b){const t=new Date(p.from);t.setDate(t.getDate()-1),d.unshift({...d[0],name:J(t,"dd/MM",{locale:z})})}const $=t=>`var(--chart-${t%12+1})`,q=a.map((t,n)=>({key:`s_${t.id}`,name:t.name,color:$(n)}));return{data:d,series:q}},[r,p,b]),L=m.useMemo(()=>{if(!p||!y)return{data:[],series:[]};const a=(y||[]).map(t=>({id:String(t.source_id),name:String(t.source_name||t.source_id)})),x=t=>{const n=new Date(t);if(!Number.isNaN(n.getTime()))return J(n,"dd/MM",{locale:z});const i=String(t).match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/);return i?`${i[3]}/${i[2]}`:String(t)},u=new Map;(y||[]).forEach(t=>{const n=String(t.source_id),i=new Map;(t.list_data||[]).forEach(g=>{const h=x(String(g.date)),c=Number(g.revenue_net??g.total_amount??0);i.set(h,(i.get(h)||0)+c)}),u.set(n,i)});const s=new Date(p.from),l=new Date(p.to);s.setHours(0,0,0,0),l.setHours(0,0,0,0);const d=[];for(let t=new Date(s);t<=l;t.setDate(t.getDate()+1)){const n=J(t,"dd/MM",{locale:z}),i={name:n};a.forEach(g=>{var V;const h=`s_${g.id}`,c=((V=u.get(g.id))==null?void 0:V.get(n))||0;i[h]=c}),d.push(i)}if(d.length===1&&!b){const t=new Date(p.from);t.setDate(t.getDate()-1),d.unshift({...d[0],name:J(t,"dd/MM",{locale:z})})}const $=t=>`var(--chart-${t%12+1})`,q=a.map((t,n)=>({key:`s_${t.id}`,name:t.name,color:$(n)}));return{data:d,series:q}},[y,p,b]),k=m.useMemo(()=>{const a=M.data;return a.length===0?!0:a.every(x=>Object.keys(x).every(u=>u==="name"||(Number(x[u])||0)===0))},[M]),v=m.useMemo(()=>{const a=L.data;return a.length===0?!0:a.every(x=>Object.keys(x).every(u=>u==="name"||(Number(x[u])||0)===0))},[L]),A=m.useMemo(()=>k?[{name:"01/01",revenue:0,detail:{total_amount:0,total_bill:0,discount_amount:0}},{name:"01/01",revenue:0,detail:{total_amount:0,total_bill:0,discount_amount:0}}]:M.data,[k,M]),T=m.useMemo(()=>v?[{name:"01/01",revenue:0,detail:{total_amount:0,total_bill:0,discount_amount:0}},{name:"01/01",revenue:0,detail:{total_amount:0,total_bill:0,discount_amount:0}}]:L.data,[v,L]),N=m.useMemo(()=>{const a=A.map(l=>l.name);if(a.length<=10)return a;const u=Math.max(1,Math.ceil(a.length/8)),s=a.filter((l,d)=>d%u===0);return s[s.length-1]!==a[a.length-1]&&s.push(a[a.length-1]),s},[A]),H=m.useMemo(()=>{const a=T.map(l=>l.name);if(a.length<=10)return a;const u=Math.max(1,Math.ceil(a.length/8)),s=a.filter((l,d)=>d%u===0);return s[s.length-1]!==a[a.length-1]&&s.push(a[a.length-1]),s},[T]),P=m.useMemo(()=>{const a={};return M.series.forEach(x=>{a[x.key]={label:x.name,color:x.color}}),a},[M]);return O?e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs(Z,{children:[e.jsx(te,{children:e.jsx(se,{children:"Doanh thu bao gồm: Phí ship, VAT"})}),e.jsx(ne,{children:e.jsx(me,{config:P,className:"h-[300px] w-full",children:e.jsxs(fe,{accessibilityLayer:!0,data:A,margin:{left:12,right:12},children:[e.jsx(_e,{vertical:!1}),e.jsx(pe,{dataKey:"name",tickLine:!1,axisLine:!1,tickMargin:8,interval:0,ticks:N}),e.jsx(je,{tickLine:!1,axisLine:!1,tickMargin:8,tickFormatter:ye,domain:k?[0,5]:[0,a=>Math.ceil(Number(a)*1.2)],ticks:k?[0,1,2,3,4,5]:void 0}),e.jsx(ue,{cursor:!1,content:e.jsx(he,{indicator:"line"})}),M.series.map(a=>e.jsx(Ne,{dataKey:a.key,type:"linear",stroke:F?"transparent":`var(--color-${a.key})`,strokeWidth:2,dot:F?{r:4,stroke:`var(--color-${a.key})`,fill:`var(--color-${a.key})`}:!1},a.key)),e.jsx(xe,{verticalAlign:"bottom",content:e.jsx(ge,{})})]})})})]}),e.jsxs(Z,{children:[e.jsx(te,{children:e.jsx(se,{children:"Doanh thu bao gồm: Phí ship, VAT"})}),e.jsx(ne,{children:C?e.jsx("div",{className:"flex h-[300px] items-center justify-center",children:e.jsx("div",{className:"text-muted-foreground",children:"Đang tải dữ liệu so sánh..."})}):e.jsx(me,{config:P,className:"h-[300px] w-full",children:e.jsxs(fe,{accessibilityLayer:!0,data:T,margin:{left:12,right:12},children:[e.jsx(_e,{vertical:!1}),e.jsx(pe,{dataKey:"name",tickLine:!1,axisLine:!1,tickMargin:8,interval:0,ticks:H}),e.jsx(je,{tickLine:!1,axisLine:!1,tickMargin:8,tickFormatter:ye,domain:v?[0,5]:[0,a=>Math.ceil(Number(a)*1.2)],ticks:v?[0,1,2,3,4,5]:void 0}),e.jsx(ue,{cursor:!1,content:e.jsx(he,{indicator:"line"})}),M.series.map(a=>e.jsx(Ne,{dataKey:a.key,type:"linear",stroke:F?"transparent":`var(--color-${a.key})`,strokeWidth:2,dot:F?{r:4,stroke:`var(--color-${a.key})`,fill:`var(--color-${a.key})`}:!1},a.key)),e.jsx(xe,{verticalAlign:"bottom",content:e.jsx(ge,{})})]})})})]})]}):e.jsxs(Z,{children:[e.jsx(te,{children:e.jsx(se,{children:"Doanh thu bao gồm: Phí ship, VAT"})}),e.jsx(ne,{children:e.jsx(me,{config:P,className:"h-[300px] w-full",children:e.jsxs(fe,{accessibilityLayer:!0,data:A,margin:{left:12,right:12},children:[e.jsx(_e,{vertical:!1}),e.jsx(pe,{dataKey:"name",tickLine:!1,axisLine:!1,tickMargin:8,interval:0,ticks:N}),e.jsx(je,{tickLine:!1,axisLine:!1,tickMargin:8,tickFormatter:ye,domain:k?[0,5]:[0,a=>Math.ceil(Number(a)*1.2)],ticks:k?[0,1,2,3,4,5]:void 0}),e.jsx(ue,{cursor:!1,content:e.jsx(he,{indicator:"line"})}),M.series.map(a=>e.jsx(Ne,{dataKey:a.key,type:"linear",stroke:F?"transparent":`var(--color-${a.key})`,strokeWidth:2,dot:F?{r:4,stroke:`var(--color-${a.key})`,fill:`var(--color-${a.key})`}:!1},a.key)),e.jsx(xe,{verticalAlign:"bottom",content:e.jsx(ge,{})})]})})})]})},lt=(r,y=!0)=>Be({queryKey:["invoice-source-detail",r],queryFn:async()=>{const D=Array.isArray(r.list_store_uid)?r.list_store_uid.join(","):r.list_store_uid,C=new URLSearchParams({company_uid:r.company_uid,brand_uid:r.brand_uid,start_date:r.start_date.toString(),end_date:r.end_date.toString(),source_id:r.source_id,list_store_uid:D,page:(r.page||1).toString(),results_per_page:(r.results_per_page||20).toString()});return(await He.get(`/v3/pos-cms/report/sale-by-source?${C.toString()}`)).data},enabled:y&&!!r.source_id,staleTime:5*60*1e3,gcTime:10*60*1e3,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1}),dt=({companyUid:r,brandUid:y,startDate:D,endDate:C,sourceId:p,storeUids:R,fallbackStartDate:I,fallbackEndDate:O,shouldFetchApi:K})=>{const[b,F]=m.useState(1),[M,L]=m.useState([]),[k,v]=m.useState(null),[A,T]=m.useState(!0),N=m.useMemo(()=>({company_uid:r||"",brand_uid:y||"",start_date:D||I,end_date:C||O,source_id:p||"",list_store_uid:R||[],page:b,results_per_page:20}),[r,y,D,C,p,R,I,O,b]),{data:H,isLoading:P,error:a}=lt(N,K&&A);return m.useEffect(()=>{if(H!=null&&H.data&&Array.isArray(H.data)){const x=H.data.map(u=>{var t,n,i,g,h;const s=(u.sale_detail||[]).map(c=>({name:c.item_name||c.name||"N/A",item_name:c.item_name||c.name||"N/A",quantity:c.quantity||0,price:c.price||0,toppings:c.toppings||[],change_data:c.change_data||{sale_detail:[]}})),l=((n=(t=u.sale_payment_method)==null?void 0:t[0])==null?void 0:n.payment_method_name)||"N/A",d=((g=(i=u.sale_payment_method)==null?void 0:i[0])==null?void 0:g.trace_no)||"",$=((h=u.extra_data)==null?void 0:h.tran_no_partner)||u.tran_no||"N/A",q=c=>c?new Date(c).toLocaleString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}):new Date().toISOString();return{id:u.id||`invoice-${Math.random()}`,tran_id:u.tran_id||"",invoiceCode:u.tran_no||"N/A",partnerInvoiceNumber:$,branchName:u.table_name||"N/A",sourceType:u.source_deli||"N/A",amount:u.total_amount||0,dateTime:q(u.tran_date),items:s,paymentMethod:l,paymentReference:d,employeeName:u.employee_name||"Ca 1 Sekai Bà Hạt",tranDate:u.tran_date}});L(b===1?x:u=>[...u,...x]),x.length===0||x.length<20?T(!1):setTimeout(()=>{F(u=>u+1)},100)}},[H,b]),m.useEffect(()=>{K&&(F(1),L([]),T(!0),v(null))},[K,p,r,y,D,C]),m.useEffect(()=>{a&&(v(a),T(!1))},[a]),{invoices:M,isLoading:P||b===1&&M.length===0,error:k,hasMorePages:A,currentPage:b,totalInvoices:M.length}},mt=(r,y=!0)=>Be({queryKey:["sale-change-log",r],queryFn:async()=>{const D=new URLSearchParams({company_uid:r.company_uid,brand_uid:r.brand_uid,store_uid:r.store_uid,tran_id:r.tran_id,start_date:r.start_date.toString(),end_date:r.end_date.toString()});return(await He.get(`/v3/pos-cms/sale-change-log?${D.toString()}`)).data},enabled:y&&!!r.tran_id,staleTime:5*60*1e3,gcTime:10*60*1e3,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1});function ut({open:r,onOpenChange:y,invoice:D}){var k;if(!D)return null;const{selectedStoreIds:C,dateRange:p}=ie(),{company:R,activeBrands:I}=be(),O=m.useMemo(()=>{const v=new Date(p.from);return v.setHours(0,0,0,0),v.getTime()},[p.from]),K=m.useMemo(()=>{const v=new Date(p.to);return v.setHours(23,59,59,999),v.getTime()},[p.to]),{data:b,isLoading:F,error:M}=mt({company_uid:(R==null?void 0:R.id)||"",brand_uid:((k=I==null?void 0:I[0])==null?void 0:k.id)||"",store_uid:(C==null?void 0:C[0])||"",tran_id:D.tran_id,start_date:O,end_date:K},r&&!!D.tran_id),L=v=>v?new Date(v).toLocaleTimeString("vi-VN",{hour:"2-digit",minute:"2-digit"}):"N/A";return e.jsx(Ae,{open:r,onOpenChange:y,children:e.jsxs(Pe,{className:"sm:max-w-2xl",children:[e.jsx(Fe,{children:e.jsx($e,{className:"flex items-center justify-center",children:e.jsx("span",{children:"Nhật ký order"})})}),e.jsx("div",{className:"space-y-4",children:F?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải nhật ký..."}):M?e.jsxs("div",{className:"py-4 text-center text-sm text-red-500",children:["Lỗi khi tải nhật ký: ",M.message]}):b!=null&&b.data&&b.data.length>0?b.data.map((v,A)=>{var T,N,H,P,a,x;return e.jsxs("div",{className:"space-y-4 rounded-lg border p-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-lg font-bold text-gray-900",children:["#",((T=v.tran_id)==null?void 0:T.slice(-5))||"N/A"," - ",((N=v.change_data)==null?void 0:N.tran_no)||"N/A"]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[new Date(v.created_at*1e3).toLocaleDateString("vi-VN")," ",L(((H=v.change_data)==null?void 0:H.tran_date)||0)]})]}),e.jsx("div",{className:"text-sm text-gray-600",children:v.table_name||"N/A"}),e.jsxs("div",{className:"text-sm text-gray-600",children:["TN: ",((P=v.change_data)==null?void 0:P.employee_name)||"N/A"," - STT: ",((a=v.change_data)==null?void 0:a.order_no)||"N/A"]})]}),((x=v.change_data)==null?void 0:x.sale_detail)&&v.change_data.sale_detail.length>0&&e.jsx("div",{className:"space-y-3",children:v.change_data.sale_detail.map((u,s)=>{var l;return e.jsxs("div",{className:"border-b border-gray-200 pb-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx("span",{className:"text-gray-900",children:u.item_name||"N/A"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(rt,{className:"mr-2 h-4 w-4 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-500",children:L(((l=v.change_data)==null?void 0:l.tran_date)||0)})]})]}),e.jsx("div",{className:"text-right",children:e.jsx("span",{className:"text-gray-900",children:u.quantity})})]}),u.toppings&&u.toppings.length>0&&e.jsx("div",{className:"mt-2 text-xs text-gray-500",children:u.toppings.map((d,$)=>e.jsxs("span",{children:[d.item_name||"N/A"," ",d.quantity||1,$<u.toppings.length-1?", ":""]},$))})]},s)})})]},A)}):e.jsx("div",{className:"py-4 text-center text-sm text-gray-500 italic",children:"Không có nhật ký thay đổi"})})]})})}const ae=r=>new Intl.NumberFormat("vi-VN").format(r)+" ₫";function ht({open:r,onOpenChange:y,sourceName:D,sourceId:C,companyUid:p,brandUid:R,startDate:I,endDate:O,storeUids:K}){const[b,F]=m.useState(""),[M,L]=m.useState(new Set),[k,v]=m.useState(!1),[A,T]=m.useState(null),N=r&&!!C,H=m.useMemo(()=>{const t=new Date;return t.setHours(0,0,0,0),t.getTime()},[]),P=m.useMemo(()=>{const t=new Date;return t.setHours(23,59,59,999),t.getTime()},[]),{invoices:a,isLoading:x,error:u,totalInvoices:s}=dt({companyUid:p,brandUid:R,startDate:I,endDate:O,sourceId:C,storeUids:K,fallbackStartDate:H,fallbackEndDate:P,shouldFetchApi:N}),l=m.useMemo(()=>a.filter(t=>{const n=b.toLowerCase();return t.invoiceCode.toLowerCase().includes(n)||t.partnerInvoiceNumber.toLowerCase().includes(n)}),[a,b]),d=()=>{},$=t=>{L(n=>{const i=new Set(n);return i.has(t)?i.delete(t):i.add(t),i})},q=({invoice:t})=>{var g;const n=M.has(t.id),i=()=>{$(t.id)};return e.jsxs("div",{className:"cursor-pointer rounded-lg border p-4 hover:bg-gray-50",onClick:i,children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"mb-1 flex items-center gap-2",children:[e.jsxs("span",{className:"font-bold",children:["#",((g=t.paymentReference)==null?void 0:g.slice(-5))||"N/A"]}),e.jsx("span",{className:"text-sm text-gray-500",children:"-"}),e.jsxs("span",{className:"font-bold",children:["SỐ HĐ: ",t.invoiceCode]})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Số hoá đơn đối tác: ",t.partnerInvoiceNumber]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[t.branchName," - Kênh ",t.sourceType," (",t.partnerInvoiceNumber,") -"," ",ae(t.amount)]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"text-sm text-gray-600",children:t.dateTime}),e.jsx("div",{className:"flex h-6 w-6 items-center justify-center",children:n?e.jsx(nt,{className:"h-4 w-4"}):e.jsx(ve,{className:"h-4 w-4"})})]}),e.jsx(re,{variant:"link",className:"h-auto p-0 text-sm text-blue-600",onClick:h=>{h.stopPropagation(),T(t),v(!0)},children:"Xem nhật ký order"})]})]}),n&&e.jsxs("div",{className:"mt-4 space-y-3 border-t pt-4",children:[t.items&&t.items.length>0?e.jsx("div",{className:"space-y-2",children:t.items.map((h,c)=>e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsxs("span",{className:"flex-1",children:[e.jsxs("span",{className:"text-gray-600",children:["(x",h.quantity,")"]})," ",h.name]}),e.jsx("span",{className:"font-mono",children:ae(h.price)})]},c))}):e.jsx("div",{className:"text-sm text-gray-500 italic",children:"Không có thông tin chi tiết món hàng"}),e.jsxs("div",{className:"space-y-2 border-t pt-3",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Thành tiền:"}),e.jsx("span",{className:"font-mono",children:ae(t.amount)})]}),e.jsxs("div",{className:"flex justify-between text-base font-semibold",children:[e.jsx("span",{children:"Tổng tiền:"}),e.jsx("span",{className:"font-mono",children:ae(t.amount)})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Phương thức thanh toán:"}),e.jsxs("span",{className:"text-right",children:[t.paymentMethod,t.paymentReference&&e.jsxs("div",{className:"text-xs text-gray-500",children:["(",t.paymentReference,")"]})]})]})]})]})]})};return e.jsxs(Ae,{open:r,onOpenChange:y,children:[e.jsxs(Pe,{className:"sm:max-w-2xl",children:[e.jsx(Fe,{children:e.jsx($e,{className:"flex items-center justify-center",children:e.jsxs("span",{children:["Hoá đơn áp dụng nguồn ",D]})})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex gap-4",children:e.jsx("div",{className:"flex-1",children:e.jsx(at,{placeholder:"Tìm kiếm theo mã hoá đơn hoặc số hoá đơn đối tác",value:b,onChange:t=>F(t.target.value)})})}),e.jsx("div",{className:"max-h-96 overflow-y-auto",children:x&&s===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"Đang tải dữ liệu..."}):u?e.jsxs("div",{className:"py-8 text-center text-red-500",children:[e.jsx("div",{className:"font-medium",children:"Có lỗi xảy ra khi tải dữ liệu:"}),e.jsxs("details",{className:"mt-2 text-xs",children:[e.jsx("summary",{className:"cursor-pointer",children:"Chi tiết lỗi"}),e.jsx("pre",{className:"mt-1 max-h-32 overflow-auto text-left",children:JSON.stringify(u,null,2)})]})]}):l.length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:e.jsx("div",{className:"font-medium",children:"Không tìm thấy hóa đơn nào"})}):e.jsx("div",{className:"space-y-3",children:l.map(t=>e.jsx(q,{invoice:t},t.id))})}),e.jsx("div",{className:"flex justify-end border-t pt-4",children:e.jsxs(re,{onClick:d,className:"flex items-center gap-2",children:[e.jsx(Ie,{className:"h-4 w-4"}),"Xuất hoá đơn"]})})]})]}),e.jsx(ut,{open:k,onOpenChange:v,invoice:A})]})}const w=r=>new Intl.NumberFormat("vi-VN").format(r),xt=()=>{var u;const{tableRows:r,isLoading:y,compareCities:D,compareStores:C,selectedStoreIds:p,compareStoreIds:R,compareRawData:I,startDate:O,endDate:K}=ie(),{currentBrandStores:b}=Se(),{company:F,activeBrands:M}=be(),[L,k]=m.useState(null),[v,A]=m.useState(!1),T=((D==null?void 0:D.length)||0)>0||((C==null?void 0:C.length)||0)>0,N=Y.useMemo(()=>r.reduce((s,l)=>({total_bill:s.total_bill+(l.total_bill||0),revenue_net:s.revenue_net+(l.revenue_net||0),revenue_gross:s.revenue_gross+(l.revenue_gross||0),peo_count:s.peo_count+(l.peo_count||0),commission_amount:s.commission_amount+(l.commission_amount||0),discount_amount:s.discount_amount+(l.discount_amount||0),partner_marketing_amount:s.partner_marketing_amount+(l.partner_marketing_amount||0)}),{total_bill:0,revenue_net:0,revenue_gross:0,peo_count:0,commission_amount:0,discount_amount:0,partner_marketing_amount:0}),[r]),H=Y.useMemo(()=>{const s=new Map;return(I||[]).forEach(l=>{const d=l.source_id||l.source_name;d&&s.set(String(d),l)}),s},[I]),P=Y.useMemo(()=>(I||[]).reduce((s,l)=>({total_bill:s.total_bill+(l.total_bill||0),revenue_net:s.revenue_net+(l.revenue_net||0),revenue_gross:s.revenue_gross+(l.revenue_gross||0),commission_amount:s.commission_amount+(l.commission_amount||0),discount_amount:s.discount_amount+(l.discount_amount||0)}),{total_bill:0,revenue_net:0,revenue_gross:0,commission_amount:0,discount_amount:0}),[I]),a=s=>{var q;const l=s.map(t=>b.find(n=>(n.id||n.store_id)===t)).filter(Boolean),d=((q=l[0])==null?void 0:q.store_name)||"Tất cả cửa hàng",$=Math.max(0,l.length-1);return e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsxs(Ue,{children:[e.jsx(Ge,{asChild:!0,children:e.jsx(oe,{variant:"outline",className:"cursor-default",children:d})}),l.length>0&&e.jsx(We,{side:"bottom",children:e.jsx("div",{className:"max-h-60 max-w-[260px] overflow-auto pr-1",children:l.map(t=>e.jsx("div",{className:"whitespace-nowrap",children:t.store_name},t.id||t.store_id))})})]}),$>0&&e.jsxs(oe,{variant:"secondary",children:["+",$]})]})},x=s=>{k({name:s.source_name,id:s.source_id||s.source_name||""}),A(!0)};return T?y?e.jsx(Z,{children:e.jsx("div",{className:"text-muted-foreground flex h-32 items-center justify-center",children:"Đang tải dữ liệu..."})}):e.jsx("div",{className:"overflow-x-auto rounded-md border",children:e.jsxs(Te,{children:[e.jsxs(Le,{children:[e.jsxs(Q,{children:[e.jsx(S,{className:"border-b-2",colSpan:2}),e.jsx(S,{className:"border-r-2 border-b-2 text-center",colSpan:9,children:(p==null?void 0:p.length)>0?a(p):"Tất cả cửa hàng"}),e.jsx(S,{className:"border-b-2 text-center",colSpan:9,children:(R==null?void 0:R.length)>0?a(R):"So sánh"})]}),e.jsxs(Q,{children:[e.jsx(S,{className:"w-10",children:"#"}),e.jsx(S,{children:"Nguồn đơn"}),e.jsx(S,{className:"text-right",children:"Tổng hoá đơn"}),e.jsx(S,{className:"text-right",children:"Số khách"}),e.jsx(S,{className:"text-right",children:"Hoa hồng"}),e.jsx(S,{className:"text-right",children:"Giảm giá (merchant)"}),e.jsx(S,{className:"text-right",children:"Giảm giá (đối tác)"}),e.jsx(S,{className:"text-right",children:"Thuế khấu trừ"}),e.jsx(S,{className:"text-right",children:"Tổng chi phí"}),e.jsx(S,{className:"text-right",children:"Doanh thu (net)"}),e.jsx(S,{className:"border-r-2 text-right",children:"Doanh thu (gross)"}),e.jsx(S,{className:"border-l-2 text-right",children:"Tổng hoá đơn"}),e.jsx(S,{className:"text-right",children:"Số khách"}),e.jsx(S,{className:"text-right",children:"Hoa hồng"}),e.jsx(S,{className:"text-right",children:"Giảm giá (merchant)"}),e.jsx(S,{className:"text-right",children:"Giảm giá (đối tác)"}),e.jsx(S,{className:"text-right",children:"Thuế khấu trừ"}),e.jsx(S,{className:"text-right",children:"Tổng chi phí"}),e.jsx(S,{className:"text-right",children:"Doanh thu (net)"}),e.jsx(S,{className:"text-right",children:"Doanh thu (gross)"})]})]}),e.jsx(Ee,{children:r.map((s,l)=>{const d=H.get(String(s.source_id))||H.get(String(s.source_name))||{},$=(s.commission_amount||0)+(s.discount_amount||0)+(s.partner_marketing_amount||0),q=s.revenue_gross||s.revenue_net||0,t=q>0?($/q*100).toFixed(2):"0",n=(d.commission_amount||0)+(d.discount_amount||0)+(d.partner_marketing_amount||0),i=d.revenue_gross||d.revenue_net||0,g=i>0?(n/i*100).toFixed(2):"0";return e.jsxs(Q,{className:"cursor-pointer hover:bg-gray-50",onClick:()=>x(s),children:[e.jsx(_,{className:"font-mon text-left",children:l+1}),e.jsx(_,{className:"font-mon text-left",children:s.source_name}),e.jsx(_,{className:"text-right font-mono",children:w(s.total_bill)}),e.jsx(_,{className:"text-right font-mono",children:w(s.peo_count||0)}),e.jsxs(_,{className:"text-right font-mono",children:[w(s.commission_amount)," đ"]}),e.jsxs(_,{className:"text-right font-mono",children:[w(s.discount_amount)," đ"]}),e.jsxs(_,{className:"text-right font-mono",children:[w(s.partner_marketing_amount)," đ"]}),e.jsx(_,{className:"text-right font-mono",children:"0.00 đ"}),e.jsxs(_,{className:"text-right",children:[e.jsxs("div",{className:"font-mono",children:[w($)," đ"]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Chi phí mất ",t,"% doanh thu"]})]}),e.jsxs(_,{className:"text-right",children:[e.jsxs("div",{className:"font-mono",children:[w(s.revenue_net)," đ"]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Chiếm ",N.revenue_net>0?(s.revenue_net/N.revenue_net*100).toFixed(2):"0","% tổng doanh thu net"]})]}),e.jsxs(_,{className:"border-r-2 text-right",children:[e.jsxs("div",{className:"font-mono",children:[w(s.revenue_gross)," đ"]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Chiếm"," ",N.revenue_gross>0?(s.revenue_gross/N.revenue_gross*100).toFixed(2):"0","% tổng doanh thu gross"]})]}),e.jsx(_,{className:"border-l-2 text-right font-mono",children:w(d.total_bill||0)}),e.jsx(_,{className:"text-right font-mono",children:w(d.peo_count||0)}),e.jsxs(_,{className:"text-right font-mono",children:[w(d.commission_amount||0)," đ"]}),e.jsxs(_,{className:"text-right font-mono",children:[w(d.discount_amount||0)," đ"]}),e.jsxs(_,{className:"text-right font-mono",children:[w(d.partner_marketing_amount||0)," đ"]}),e.jsx(_,{className:"text-right font-mono",children:"0.00 đ"}),e.jsxs(_,{className:"text-right",children:[e.jsxs("div",{className:"font-mono",children:[w(n)," đ"]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Chi phí mất ",g,"% doanh thu"]})]}),e.jsxs(_,{className:"text-right",children:[e.jsxs("div",{className:"font-mono",children:[w(d.revenue_net||0)," đ"]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Chiếm"," ",P.revenue_net>0?((d.revenue_net||0)/P.revenue_net*100).toFixed(2):"0","% tổng doanh thu net"]})]}),e.jsxs(_,{className:"text-right",children:[e.jsxs("div",{className:"font-mono",children:[w(d.revenue_gross||0)," đ"]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Chiếm"," ",P.revenue_gross>0?((d.revenue_gross||0)/P.revenue_gross*100).toFixed(2):"0","% tổng doanh thu gross"]})]})]},s.source_id||s.source_name)})})]})}):y?e.jsx(Z,{children:e.jsx("div",{className:"text-muted-foreground flex h-32 items-center justify-center",children:"Đang tải dữ liệu..."})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"rounded-md border",children:[e.jsxs(Te,{children:[e.jsx(Le,{children:e.jsxs(Q,{children:[e.jsx(S,{className:"w-10",children:"#"}),e.jsx(S,{children:"Mã nguồn"}),e.jsx(S,{children:"Tên nguồn"}),e.jsx(S,{className:"text-right",children:"Tổng hoá đơn"}),e.jsx(S,{className:"text-right",children:"Số khách"}),e.jsx(S,{className:"text-right",children:"Hoa hồng (1)"}),e.jsx(S,{className:"text-right",children:"Giảm giá (merchant) (2)"}),e.jsx(S,{className:"text-right",children:"Giảm giá (đối tác) (3)"}),e.jsx(S,{className:"text-right",children:"Thuế khấu trừ"}),e.jsx(S,{className:"text-right",children:"Tổng chi phí (1) + (2) + (3)"}),e.jsx(S,{className:"text-right",children:"Doanh thu (net)"}),e.jsx(S,{className:"text-right",children:"Doanh thu (gross)"})]})}),e.jsxs(Ee,{children:[r.map((s,l)=>{const d=(s.commission_amount||0)+(s.discount_amount||0)+(s.partner_marketing_amount||0),$=s.revenue_gross||s.revenue_net||0,q=$>0?(d/$*100).toFixed(2):"0";return e.jsxs(Q,{className:"cursor-pointer hover:bg-gray-50",onClick:()=>x(s),children:[e.jsx(_,{children:l+1}),e.jsx(_,{className:"font-mono",children:s.source_id||"-"}),e.jsx(_,{children:s.source_name}),e.jsx(_,{className:"text-right font-mono",children:w(s.total_bill)}),e.jsx(_,{className:"text-right font-mono",children:w(s.peo_count||0)}),e.jsxs(_,{className:"text-right font-mono",children:[w(s.commission_amount)," đ"]}),e.jsxs(_,{className:"text-right font-mono",children:[w(s.discount_amount)," đ"]}),e.jsxs(_,{className:"text-right font-mono",children:[w(s.partner_marketing_amount)," đ"]}),e.jsx(_,{className:"text-right font-mono",children:"0.00 đ"}),e.jsxs(_,{className:"text-right",children:[e.jsxs("div",{className:"font-mono",children:[w(d)," đ"]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Chi phí mất ",q,"% doanh thu"]})]}),e.jsxs(_,{className:"text-right",children:[e.jsxs("div",{className:"font-mono",children:[w(s.revenue_net)," đ"]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Chiếm ",N.revenue_net>0?(s.revenue_net/N.revenue_net*100).toFixed(2):"0","% tổng doanh thu net"]})]}),e.jsxs(_,{className:"text-right",children:[e.jsxs("div",{className:"font-mono",children:[w(s.revenue_gross)," đ"]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Chiếm"," ",N.revenue_gross>0?(s.revenue_gross/N.revenue_gross*100).toFixed(2):"0","% tổng doanh thu gross"]})]})]},s.source_id||s.source_name)}),r.length>0&&e.jsxs(Q,{children:[e.jsx(_,{className:"font-semibold",children:"Tổng"}),e.jsx(_,{}),e.jsx(_,{}),e.jsx(_,{className:"text-right font-mono",children:w(N.total_bill)}),e.jsx(_,{className:"text-right font-mono",children:w(N.peo_count)}),e.jsxs(_,{className:"text-right font-mono",children:[w(N.commission_amount)," đ"]}),e.jsxs(_,{className:"text-right font-mono",children:[w(N.discount_amount)," đ"]}),e.jsxs(_,{className:"text-right font-mono",children:[w(N.partner_marketing_amount)," đ"]}),e.jsx(_,{className:"text-right font-mono",children:"0.00 đ"}),e.jsxs(_,{className:"text-right font-mono",children:[w(N.commission_amount+N.discount_amount+N.partner_marketing_amount)," đ"]}),e.jsxs(_,{className:"text-right font-mono",children:[w(N.revenue_net)," đ"]}),e.jsxs(_,{className:"text-right font-mono",children:[w(N.revenue_gross)," đ"]})]})]})]}),r.length===0&&e.jsx("div",{className:"text-muted-foreground flex h-24 items-center justify-center",children:"Không có dữ liệu"})]}),e.jsx(ht,{open:v,onOpenChange:A,sourceName:L==null?void 0:L.name,sourceId:L==null?void 0:L.id,companyUid:F==null?void 0:F.id,brandUid:(u=M[0])==null?void 0:u.id,startDate:O,endDate:K,storeUids:p.length>0?p:b.map(s=>s.id)})]})},gt=()=>e.jsx(ot,{children:e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsx(it,{}),e.jsx(ct,{}),e.jsx(xt,{})]})}),os=gt;export{os as component};
