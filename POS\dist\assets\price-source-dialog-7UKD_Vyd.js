import{a3 as k,u as B,l as U,a4 as g,r as _,j as t,R as ue,B as j,z as e}from"./index-D0Grd55b.js";import{u as _e}from"./use-dialog-state-Bi2BGkq6.js";import{C as Q}from"./checkbox-C_eqgJmW.js";import{I as X}from"./input-C-0UnKOB.js";import{P as ge}from"./modal-hDollbt0.js";import"./pos-api-B09qRspF.js";import"./date-range-picker-CruKYeHR.js";import{L as xe,u as W,F as G,a as M,b as L,c as T,d as R,e as w}from"./form-Bk1C9kLO.js";import{C as re}from"./chevron-right-D2H_xC9V.js";import{C as ae}from"./select-DBO-8fSu.js";import{s as Y}from"./zod-G2vIgQkk.js";import"./user-CN3Oxfhq.js";import{D as J,a as Z,b as ee,c as te,e as ne,f as se}from"./dialog-C8IVKkOo.js";import{C as me}from"./combobox-NkstXop_.js";import"./vietqr-api-C42F6d9k.js";import"./crm-api-Bz_HnEz3.js";import{u as E}from"./useMutation-ATsU-ht7.js";import{Q as S}from"./query-keys-3lmd-xp6.js";import{i as D}from"./items-in-store-api-BAXUzzVL.js";import{D as oe}from"./date-picker-CnBJ_htZ.js";import{C as ce}from"./circle-help-CEmvpE3g.js";import{X as be}from"./calendar-5lpy20z0.js";const Je=()=>{const n=k(),s=E({mutationFn:o=>D.createItemInStore(o),onSuccess:()=>{D.clearCache(),n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_LIST]}),g.success("Tạo món thành công!")},onError:o=>{g.error(o.message||"Có lỗi xảy ra khi tạo món")}});return{createItemAsync:s.mutateAsync,isPending:s.isPending}},ye=()=>{const n=k(),s=E({mutationFn:o=>D.updateItemInStore(o),onSuccess:()=>{D.clearCache(),n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_LIST]}),n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_DETAIL]}),g.success("Cập nhật món thành công!")},onError:o=>{g.error(o.message||"Có lỗi xảy ra khi cập nhật món")}});return{updateItemAsync:s.mutateAsync,isPending:s.isPending}},Ze=()=>{const n=k(),{company:s}=B(i=>i.auth),{selectedBrand:o}=U(),l=E({mutationFn:i=>{const a={company_uid:(s==null?void 0:s.id)||"",brand_uid:(o==null?void 0:o.id)||"",id:i};return D.deleteItemInStore(a)},onSuccess:()=>{n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_LIST]}),g.success("Xóa món thành công!")},onError:i=>{g.error(i.message||"Có lỗi xảy ra khi xóa món")}});return{deleteItemAsync:l.mutateAsync,isPending:l.isPending}},et=()=>{const n=k(),{company:s}=B(i=>i.auth),{selectedBrand:o}=U(),l=E({mutationFn:i=>{const a={company_uid:(s==null?void 0:s.id)||"",brand_uid:(o==null?void 0:o.id)||"",list_item_uid:i};return D.deleteMultipleItemsInStore(a)},onSuccess:()=>{n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_LIST]}),g.success("Xóa món ăn thành công")},onError:i=>{g.error((i==null?void 0:i.message)||"Có lỗi xảy ra khi xóa món ăn")}});return{deleteMultipleItemsAsync:l.mutateAsync,isPending:l.isPending}},tt=()=>{const n=k(),{company:s}=B(i=>i.auth),{selectedBrand:o}=U(),l=E({mutationFn:i=>{const a={id:i.id,active:i.active,company_uid:(s==null?void 0:s.id)||"",brand_uid:(o==null?void 0:o.id)||""};return D.updateItemStatus(a)},onSuccess:()=>{D.clearCache(),n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_LIST]}),n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_DETAIL]}),g.success("Cập nhật trạng thái thành công!")},onError:i=>{g.error(i.message||"Có lỗi xảy ra khi cập nhật trạng thái")}});return{updateStatusAsync:l.mutateAsync,isPending:l.isPending}},nt=()=>{const n=k();return E({mutationFn:s=>D.bulkUpdateItemsInStore(s),onSuccess:()=>{n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_LIST]}),n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_FOR_TABLE]}),g.success("Cập nhật thứ tự món ăn thành công")},onError:s=>{g.error((s==null?void 0:s.message)||"Có lỗi xảy ra khi cập nhật thứ tự món ăn")}})},st=()=>{const n=k(),{mutate:s,isPending:o}=E({mutationFn:async l=>D.bulkCreateItemsInStore(l),onSuccess:()=>{n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_LIST],refetchType:"none"}),setTimeout(()=>{n.refetchQueries({queryKey:[S.ITEMS_IN_STORE_LIST]})},100),g.success("Tạo món ăn thành công!")},onError:l=>{g.error((l==null?void 0:l.message)||"Có lỗi xảy ra khi tạo món ăn")}});return{bulkCreateItemsInStore:s,isBulkCreating:o}},it=()=>{const n=k();return E({mutationFn:s=>D.cloneMenu(s),onSuccess:()=>{n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_LIST]}),n.invalidateQueries({queryKey:[S.ITEMS_IN_STORE_FOR_TABLE]}),g.success("Sao chép thực đơn thành công")},onError:s=>{g.error((s==null?void 0:s.message)||"Có lỗi xảy ra khi sao chép thực đơn")}})},de=ue.createContext(null);function rt({children:n,selectedStoreUid:s,setSelectedStoreUid:o}){const[l,i]=_e(null),[a,f]=_.useState(null);return t.jsx(de,{value:{open:l,setOpen:i,currentRow:a,setCurrentRow:f,selectedStoreUid:s,setSelectedStoreUid:o},children:n})}const at=()=>{const n=ue.useContext(de);if(!n)throw new Error("useItemsInStore has to be used within <ItemsInStoreContext>");return n};function ot({itemsBuffet:n,open:s,onOpenChange:o,onItemsChange:l,items:i,hide:a=!0,enable:f=!0,onEnableChange:y}){const[N,p]=_.useState(""),[r,u]=_.useState([]),[v,F]=_.useState(!1),[q,x]=_.useState(!1),[P,K]=_.useState(!1);_.useEffect(()=>{s&&(u(Array.isArray(n)?n:[]),K(f))},[n,s]);const C=_.useMemo(()=>N?i.filter(m=>{var b;return(b=m.item_name)==null?void 0:b.toLowerCase().includes(N.toLowerCase())}):i,[i,N]),z=_.useMemo(()=>C.length?C.filter(m=>r.includes(m.item_id||"")):[],[C,r]),O=_.useMemo(()=>C.length?C.filter(m=>!r.includes(m.item_id||"")):[],[C,r]),c=m=>{u(b=>b.includes(m)?b.filter(A=>A!==m):[...b,m])},d=z.length,h=C.length,I=h>0&&d===h,H=d>0&&d<h,V=()=>{if(I){const m=C.map(b=>b.item_id);u(b=>b.filter(A=>!m.includes(A)))}else{const m=C.map(b=>b.item_id);u(b=>{const A=[...b];return m.forEach(ie=>{A.includes(ie||"")||A.push(ie||"")}),A})}},he=()=>{l(r),o(!1)},pe=()=>{u([]),o(!1)};return t.jsx(ge,{title:"Chọn danh sách món không đi kèm vé buffet",centerTitle:!0,open:s,onOpenChange:o,onCancel:pe,onConfirm:he,confirmText:"Lưu",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:t.jsxs("div",{className:"space-y-4",children:[!a&&t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Q,{id:"enable-buffet",checked:P,onCheckedChange:m=>{const b=!!m;K(b),y==null||y(b)}}),t.jsx(xe,{htmlFor:"enable-buffet",className:"cursor-pointer text-blue-600",children:"Cấu hình món ăn là vé buffet"})]}),(a||P)&&t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"flex items-center gap-2",children:t.jsx(X,{placeholder:"Tìm kiếm",value:N,onChange:m=>p(m.target.value),className:"w-full"})}),!a&&t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(j,{type:"button",variant:"outline",className:"flex-1 justify-start",children:"Danh sách món không đi kèm vé buffet"}),t.jsx(j,{type:"button",variant:"link",className:"flex-1 justify-start text-blue-600",onClick:()=>{},children:"Danh sách vé buffet được upsize"})]}),t.jsxs("div",{className:"rounded-lg bg-green-50 p-3",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx(Q,{id:"select-all",checked:I,...H&&{"data-indeterminate":"true"},onCheckedChange:V,className:"data-[state=checked]:border-green-600 data-[state=checked]:bg-green-600"}),t.jsxs("label",{htmlFor:"select-all",className:"cursor-pointer text-sm font-medium text-green-700",children:["Đã chọn ",d]}),t.jsx(j,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>F(!v),children:v?t.jsx(re,{className:"h-3 w-3"}):t.jsx(ae,{className:"h-3 w-3"})})]}),!v&&z.length>0&&t.jsx("div",{className:"mt-3 space-y-2",children:z.map(m=>t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx(Q,{id:`selected-${m.item_id}`,checked:r.includes(m.item_id),onCheckedChange:()=>c(m.item_id)}),t.jsx("label",{htmlFor:`selected-${m.item_id}`,className:"flex-1 cursor-pointer text-sm",children:m.item_name})]},m.item_id))})]}),t.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsxs("div",{className:"text-sm font-medium text-gray-700",children:["Còn lại ",O.length]}),t.jsx(j,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>x(!q),children:q?t.jsx(re,{className:"h-3 w-3"}):t.jsx(ae,{className:"h-3 w-3"})})]}),!q&&t.jsx("div",{className:"mt-3 max-h-60 space-y-2 overflow-y-auto",children:O.map(m=>t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx(Q,{id:m.item_id,checked:r.includes(m.item_id),onCheckedChange:()=>c(m.item_id)}),t.jsx("label",{htmlFor:m.item_id,className:"flex-1 cursor-pointer text-sm",children:m.item_name})]},m.item_id))})]})]})]})})}const fe=e.object({customization_uid:e.string().nullable()});function ct({item:n,customizations:s,open:o,onOpenChange:l}){const{updateItemAsync:i}=ye(),{company:a}=B(p=>p.auth),{selectedBrand:f}=U(),y=W({resolver:Y(fe),defaultValues:{customization_uid:"none"}});_.useEffect(()=>{if(o)try{y.reset({customization_uid:(n==null?void 0:n.customization_uid)??null})}catch{g.error("Lỗi khi load customization data")}},[o,y,n]);const N=async p=>{try{if(!(n!=null&&n.id)||!(a!=null&&a.id)||!(f!=null&&f.id))throw new Error("Required data is missing");const r=p.customization_uid==="none"?null:p.customization_uid;await i({...n,customization_uid:r}),l(!1)}catch{g.error("Lỗi khi cập nhật customization")}};return n?t.jsx(J,{open:o,onOpenChange:p=>{l(p),y.reset()},children:t.jsxs(Z,{className:"top-[20%] w-full max-w-4xl translate-y-[-50%]",children:[t.jsx(ee,{children:t.jsx(te,{className:"text-center",children:"Cấu hình customization"})}),t.jsx(G,{...y,children:t.jsxs("form",{onSubmit:y.handleSubmit(N),className:"space-y-4",children:[t.jsx(M,{control:y.control,name:"customization_uid",render:({field:p})=>t.jsxs(L,{children:[t.jsx(T,{children:"Customization áp dụng cho món"}),t.jsx(R,{children:t.jsx(me,{value:p.value??"",onValueChange:r=>p.onChange(r===""?null:r),options:s.map(r=>({value:r.id,label:r.name})),placeholder:"Chọn customization...",searchPlaceholder:"Tìm kiếm customization...",emptyText:"Không có dữ liệu",className:"w-full"})}),t.jsx(w,{})]})}),t.jsxs(ne,{children:[t.jsx(se,{asChild:!0,children:t.jsx(j,{variant:"outline",type:"button",children:"Hủy"})}),t.jsx(j,{type:"submit",disabled:y.formState.isSubmitting,children:y.formState.isSubmitting?"Đang lưu...":"Lưu"})]})]})})]})}):null}const lt=()=>{const n="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";return`ITEM-${Array.from({length:4},()=>n[Math.floor(Math.random()*n.length)]).join("")}`},ut=(n,s)=>{var o;return(o=s==null?void 0:s.find(l=>l.sourceId===n||l.id===n))==null?void 0:o.sourceName},je=n=>{const s={1:"Thứ 2",2:"Thứ 3",4:"Thứ 4",8:"Thứ 5",16:"Thứ 6",32:"Thứ 7",64:"Chủ nhật"};return n.map(o=>s[o]).join(", ")},Ne=n=>n.map(s=>`${s}h`).join(", "),Se=n=>{const s=[];return n&1&&s.push(1),n&2&&s.push(2),n&4&&s.push(4),n&8&&s.push(8),n&16&&s.push(16),n&32&&s.push(32),n&64&&s.push(64),s},ve=n=>{const s=[];for(let o=0;o<24;o++)n&1<<o&&s.push(o);return s},le=n=>n.toLocaleDateString("vi-VN"),$=n=>n.map(s=>{const o=s.selectedDays.reduce((i,a)=>i|a,0),l=s.selectedHours.reduce((i,a)=>i|1<<a,0);return{price:s.price,from_date:s.startDate.getTime(),to_date:new Date(s.endDate.getTime()+24*60*60*1e3-1).getTime(),time_sale_date_week:o,time_sale_hour_day:l}}),mt=n=>n==="Hoạt động"||n===1||n==="1"||n===!0?1:0,dt=n=>n==="Có"||n===1||n==="1"||n===!0?1:0;e.object({id:e.string().optional(),item_id:e.string().optional(),item_name:e.string().optional(),description:e.string().optional(),ots_price:e.coerce.number().optional(),ots_tax:e.coerce.number().optional(),ta_price:e.coerce.number().optional(),ta_tax:e.coerce.number().optional(),time_sale_hour_day:e.coerce.number().optional(),time_sale_date_week:e.coerce.number().optional(),allow_take_away:e.coerce.number().optional(),is_eat_with:e.coerce.number().optional(),image_path:e.string().optional(),image_path_thumb:e.string().optional(),item_color:e.string().optional(),list_order:e.coerce.number().optional(),is_service:e.coerce.number().optional(),is_material:e.coerce.number().optional(),active:e.coerce.number().optional(),user_id:e.string().optional(),is_foreign:e.coerce.number().optional(),quantity_default:e.coerce.number().optional(),price_change:e.coerce.number().optional(),currency_type_id:e.string().optional(),point:e.coerce.number().optional(),is_gift:e.coerce.number().optional(),is_fc:e.coerce.number().optional(),show_on_web:e.coerce.number().optional(),show_price_on_web:e.coerce.number().optional(),cost_price:e.coerce.number().optional(),is_print_label:e.coerce.number().optional(),quantity_limit:e.coerce.number().optional(),is_kit:e.coerce.number().optional(),time_cooking:e.coerce.number().optional(),item_id_barcode:e.string().optional(),process_index:e.coerce.number().optional(),is_allow_discount:e.coerce.number().optional(),quantity_per_day:e.coerce.number().optional(),item_id_eat_with:e.string().optional(),is_parent:e.coerce.number().optional(),is_sub:e.coerce.number().optional(),item_id_mapping:e.string().optional(),effective_date:e.coerce.number().optional(),expire_date:e.coerce.number().optional(),sort:e.coerce.number().optional(),sort_online:e.coerce.number().optional(),revision:e.coerce.number().optional(),unit_uid:e.string().optional(),unit_secondary_uid:e.string().nullable().optional(),item_type_uid:e.string().optional(),item_class_uid:e.string().optional(),source_uid:e.string().nullable().optional(),brand_uid:e.string().optional(),city_uid:e.string().optional(),store_uid:e.string().optional(),company_uid:e.string().optional(),customization_uid:e.string().nullable().optional(),is_fabi:e.coerce.number().optional(),deleted:e.boolean().optional(),created_by:e.string().optional(),updated_by:e.string().optional(),deleted_by:e.string().nullable().optional(),created_at:e.coerce.number().optional(),updated_at:e.coerce.number().optional(),deleted_at:e.coerce.number().nullable().optional(),apply_with_store:e.coerce.number().optional(),extra_data:e.object({cross_price:e.array(e.object({price:e.coerce.number().optional(),quantity:e.coerce.number().optional()})).optional(),formula_qrcode:e.string().optional(),is_buffet_item:e.coerce.number().optional(),up_size_buffet:e.array(e.any()).optional(),is_item_service:e.coerce.number().optional(),is_virtual_item:e.coerce.number().optional(),price_by_source:e.array(e.object({price:e.coerce.number().optional(),source_id:e.string().optional(),price_times:e.array(e.any()).optional(),is_source_exist_in_city:e.boolean().optional()})).optional(),enable_edit_price:e.coerce.number().optional(),exclude_items_buffet:e.array(e.string()).optional(),no_update_quantity_toping:e.coerce.number().optional()}).optional(),enable_custom_item_id:e.coerce.number().optional(),Stores:e.array(e.any()).optional(),item_old:e.record(e.any()).optional()});const ht=e.object({id:e.string().optional(),item_id:e.string().optional(),item_name:e.string().min(1,"Tên món là bắt buộc"),description:e.string().optional(),ots_price:e.coerce.number().min(0).optional(),ots_tax:e.coerce.number().min(0).max(1).optional(),ta_price:e.coerce.number().min(0).optional(),ta_tax:e.coerce.number().min(0).max(1).optional(),time_cooking:e.coerce.number().optional(),time_sale_hour_day:e.coerce.number().optional(),time_sale_date_week:e.coerce.number().optional(),allow_take_away:e.coerce.number().optional(),is_eat_with:e.coerce.number().optional(),image_path:e.string().url().optional(),image_path_thumb:e.string().url().optional(),item_color:e.string().optional(),list_order:e.coerce.number().optional(),is_service:e.coerce.number().optional(),is_material:e.coerce.number().optional(),is_print_label:e.coerce.number().optional(),is_allow_discount:e.coerce.number().optional(),item_id_barcode:e.string().optional(),process_index:e.coerce.number().optional(),quantity_per_day:e.coerce.number().optional(),item_id_eat_with:e.string().optional(),is_parent:e.coerce.number().optional(),is_sub:e.coerce.number().optional(),item_id_mapping:e.string().optional(),effective_date:e.coerce.number().optional(),expire_date:e.coerce.number().optional(),sort:e.coerce.number().optional(),sort_online:e.coerce.number().optional(),unit_uid:e.string().min(1,"Đơn vị là bắt buộc").optional(),unit_secondary_uid:e.string().nullable().optional(),item_type_uid:e.string().min(1,"Loại món là bắt buộc"),item_class_uid:e.string().optional(),city_uid:e.string().optional(),store_uid:e.string().optional(),customization_uid:e.string().nullable().optional(),cross_price:e.array(e.object({price:e.coerce.number().min(0).optional(),quantity:e.coerce.number().min(0).optional()})).optional(),formula_qrcode:e.string().optional(),is_buffet_item:e.coerce.number().optional(),up_size_buffet:e.array(e.any()).optional(),is_item_service:e.coerce.number().optional(),is_virtual_item:e.coerce.number().optional(),price_by_source:e.array(e.object({price:e.coerce.number().min(0).optional(),source_id:e.string().optional(),price_times:e.array(e.any()).optional(),is_source_exist_in_city:e.boolean().optional()})).optional(),enable_edit_price:e.coerce.number().optional(),exclude_items_buffet:e.array(e.string()).optional(),no_update_quantity_toping:e.coerce.number().optional(),quantity:e.coerce.number().optional(),price:e.coerce.number().optional(),enable_custom_item_id:e.coerce.number().optional(),apply_with_store:e.coerce.number().optional()}),Ce=e.object({amount:e.coerce.number().min(0,"Số tiền phải lớn hơn hoặc bằng 0"),startDate:e.date({required_error:"Vui lòng chọn ngày bắt đầu"}),endDate:e.date({required_error:"Vui lòng chọn ngày kết thúc"}),selectedDays:e.array(e.number()).min(1,"Vui lòng chọn ít nhất 1 ngày"),selectedHours:e.array(e.number()).min(1,"Vui lòng chọn ít nhất 1 giờ")}).refine(n=>n.startDate&&n.endDate?n.endDate>=n.startDate:!0,{message:"Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu",path:["endDate"]}),Ie=[{value:1,label:"Thứ 2"},{value:2,label:"Thứ 3"},{value:4,label:"Thứ 4"},{value:8,label:"Thứ 5"},{value:16,label:"Thứ 6"},{value:32,label:"Thứ 7"},{value:64,label:"Chủ nhật"}],De=Array.from({length:24},(n,s)=>({value:s,label:`${s}h`}));function Te({open:n,onOpenChange:s,onConfirm:o,sourceName:l,data:i}){const a=W({resolver:Y(Ce),defaultValues:{amount:0,startDate:new Date,endDate:new Date,selectedDays:[],selectedHours:[]}});_.useEffect(()=>{if(n&&i){a.setValue("amount",Number(i.amount??0));const r=i.startDate?new Date(i.startDate):new Date,u=i.endDate?new Date(i.endDate):new Date;a.setValue("startDate",r),a.setValue("endDate",u),Array.isArray(i.selectedDays)&&a.setValue("selectedDays",i.selectedDays),Array.isArray(i.selectedHours)&&a.setValue("selectedHours",i.selectedHours)}},[n,i,a]);const f=r=>{o==null||o(r),s(!1),a.reset()},y=r=>{s(r),r||a.reset()},N=r=>{const u=a.getValues("selectedDays"),v=u.includes(r)?u.filter(F=>F!==r):[...u,r];a.setValue("selectedDays",v)},p=r=>{const u=a.getValues("selectedHours"),v=u.includes(r)?u.filter(F=>F!==r):[...u,r];a.setValue("selectedHours",v)};return t.jsx(J,{open:n,onOpenChange:y,children:t.jsxs(Z,{className:"max-w-2xl lg:max-w-2xl",children:[t.jsx(ee,{children:t.jsxs(te,{children:["Cấu hình giá theo khung thời gian nguồn ",l]})}),t.jsx(G,{...a,children:t.jsxs("form",{onSubmit:a.handleSubmit(f),className:"space-y-6",children:[t.jsxs("div",{className:"grid grid-cols-[120px_1fr] items-center gap-4",children:[t.jsx(T,{className:"text-sm font-medium text-gray-600",children:"Số tiền"}),t.jsx(M,{control:a.control,name:"amount",render:({field:r})=>t.jsxs(L,{children:[t.jsx(R,{children:t.jsx(X,{type:"text",placeholder:"0",value:r.value,onChange:u=>{r.onChange(u.target.value)},onKeyDown:u=>{!/[0-9]/.test(u.key)&&!["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(u.key)&&u.preventDefault()}})}),t.jsx(w,{})]})})]}),t.jsxs("div",{className:"space-y-3",children:[t.jsx(T,{className:"text-sm font-medium text-gray-600",children:"Ngày áp dụng"}),t.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[t.jsx(M,{control:a.control,name:"startDate",render:({field:r})=>t.jsxs(L,{children:[t.jsx(T,{className:"text-xs text-gray-500",children:"Ngày bắt đầu"}),t.jsx(R,{children:t.jsx(oe,{date:r.value,onDateChange:r.onChange,placeholder:"Chọn ngày bắt đầu",className:"border-blue-200 bg-blue-50"})}),t.jsx(w,{})]})}),t.jsx(M,{control:a.control,name:"endDate",render:({field:r})=>t.jsxs(L,{children:[t.jsx(T,{className:"text-xs text-gray-500",children:"Ngày kết thúc"}),t.jsx(R,{children:t.jsx(oe,{date:r.value,onDateChange:r.onChange,placeholder:"Chọn ngày kết thúc",className:"border-blue-200 bg-blue-50"})}),t.jsx(w,{})]})})]})]}),t.jsxs("div",{className:"space-y-4",children:[t.jsx(T,{className:"text-sm font-medium text-gray-600",children:"Khung thời gian áp dụng"}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(T,{className:"text-xs text-gray-500",children:"Chọn ngày"}),t.jsx(ce,{className:"h-3 w-3 text-gray-400"})]}),t.jsx("div",{className:"grid grid-cols-7 gap-2",children:Ie.map(r=>{const u=a.watch("selectedDays").includes(r.value);return t.jsx(j,{type:"button",variant:u?"default":"outline",size:"sm",onClick:()=>N(r.value),className:`w-full ${u?"bg-blue-600 text-white hover:bg-blue-700":"border-blue-200 text-gray-700 hover:bg-blue-50"}`,children:r.label},r.value)})}),t.jsx(w,{})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(T,{className:"text-xs text-gray-500",children:"Chọn giờ"}),t.jsx(ce,{className:"h-3 w-3 text-gray-400"})]}),t.jsx("div",{className:"grid grid-cols-6 gap-2",children:De.map(r=>{const u=a.watch("selectedHours").includes(r.value);return t.jsx(j,{type:"button",variant:u?"default":"outline",size:"sm",onClick:()=>p(r.value),className:`w-full ${u?"bg-blue-600 text-white hover:bg-blue-700":"border-blue-200 text-gray-700 hover:bg-blue-50"}`,children:r.label},r.value)})}),t.jsx(w,{})]})]}),t.jsxs(ne,{className:"pt-4",children:[t.jsx(se,{asChild:!0,children:t.jsx(j,{type:"button",variant:"outline",children:"Hủy"})}),t.jsx(j,{type:"button",onClick:a.handleSubmit(f),children:"Xác nhận"})]})]})})]})})}const we=e.object({price:e.coerce.number().min(0,"Số tiền phải lớn hơn hoặc bằng 0"),source_id:e.string().optional(),source_name:e.string().optional(),price_times:e.array(e.any()).optional(),is_source_exist_in_city:e.boolean().optional()});function pt({open:n,onOpenChange:s,onConfirm:o,sources:l,data:i}){const[a,f]=_.useState(null),[y,N]=_.useState(!1),[p,r]=_.useState([]),[u,v]=_.useState(null),[F,q]=_.useState(),x=W({resolver:Y(we),defaultValues:{source_id:(i==null?void 0:i.source_id)||"",price:(i==null?void 0:i.price)||0,price_times:(i==null?void 0:i.price_times)||[]}});_.useEffect(()=>{if(n&&i){x.setValue("source_id",i.source_id),x.setValue("price",i.price),x.setValue("price_times",i.price_times);const c=l.find(d=>d.sourceId===i.source_id);if(f(c||null),Array.isArray(i.price_times)&&i.price_times.length>0){const d=i.price_times.map(h=>({id:Date.now().toString()+Math.random(),price:h.price,startDate:new Date(h.from_date),endDate:new Date(h.to_date),selectedDays:h.time_sale_date_week?Se(h.time_sale_date_week):[],selectedHours:h.time_sale_hour_day?ve(h.time_sale_hour_day):[]}));r(d)}else r([])}},[n,i,x,l]);const P=c=>{const d={...c,source_id:a==null?void 0:a.sourceId,source_name:a==null?void 0:a.sourceName,price_times:$(p),is_source_exist_in_city:!0};o==null||o(d),s(!1),x.reset(),f(null),r([])},K=c=>{const d={id:u||Date.now().toString(),price:Number(c.amount||0),startDate:c.startDate,endDate:c.endDate,selectedDays:c.selectedDays,selectedHours:c.selectedHours};r(h=>{let I;u?I=h.map(V=>V.id===u?d:V):I=[...h,d];const H=$(I);return x.setValue("price_times",H),I}),v(null)},C=c=>{r(d=>{const h=d.filter(H=>H.id!==c),I=$(h);return x.setValue("price_times",I),h})},z=c=>{c.preventDefault(),c.stopPropagation(),x.handleSubmit(P)(c)},O=c=>{s(c),c||(x.reset(),f(null),N(!1),r([]),x.setValue("price_times",[]),v(null),q(void 0))};return t.jsxs(J,{open:n,onOpenChange:O,children:[t.jsxs(Z,{className:"max-w-md lg:max-w-2xl",children:[t.jsx(ee,{children:t.jsx(te,{children:"Cấu hình giá theo nguồn"})}),t.jsx(G,{...x,children:t.jsxs("form",{onSubmit:z,className:"space-y-4",children:[t.jsxs("div",{className:"grid grid-cols-[120px_1fr] items-center gap-4",children:[t.jsx(T,{className:"text-sm font-medium text-gray-600",children:"Nguồn đơn"}),t.jsx(M,{control:x.control,name:"source_id",render:({field:c})=>t.jsxs(L,{children:[t.jsx(me,{options:l.map(d=>({value:d.sourceId,label:d.sourceName})),value:c.value,onValueChange:d=>{c.onChange(d);const h=l.find(I=>I.sourceId===d);f(h||null)},placeholder:"Chọn nguồn đơn",searchPlaceholder:"Tìm nguồn đơn...",emptyText:"Không có nguồn phù hợp.",className:"w-full"}),t.jsx(w,{})]})})]}),t.jsxs("div",{className:"grid grid-cols-[120px_1fr] items-center gap-4",children:[t.jsx(T,{className:"text-sm font-medium text-gray-600",children:"Số tiền"}),t.jsx(M,{control:x.control,name:"price",render:({field:c})=>t.jsxs(L,{children:[t.jsx(R,{children:t.jsx(X,{type:"text",placeholder:"0",value:c.value,onChange:d=>{c.onChange(d.target.value)},onKeyDown:d=>{!/[0-9]/.test(d.key)&&!["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(d.key)&&d.preventDefault()}})}),t.jsx(w,{})]})})]}),a&&t.jsxs("div",{className:"space-y-4 border-t pt-4",children:[t.jsxs("div",{className:"space-y-3",children:[t.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Cấu hình giá theo khung thời gian"}),t.jsxs("div",{className:"text-sm leading-relaxed text-gray-600",children:["Giá theo nguồn ",t.jsx("span",{className:"font-semibold text-blue-600",children:a.sourceName})," sẽ được lấy theo số tiền"," ",t.jsxs("span",{className:"font-semibold text-blue-600",children:[x.getValues("price")||"0"," đ"]}),". Khi cấu hình giá theo khung thời gian số tiền sẽ hiển thị theo các khung thời gian cấu hình dưới đây"]}),t.jsx("div",{className:"flex justify-end",children:t.jsx(j,{type:"button",variant:"outline",className:"text-blue-600 hover:text-blue-700",onClick:()=>{v(null),q({amount:Number(x.getValues("price")||0)}),N(!0)},children:"Thêm cấu hình"})})]}),p.length===0?t.jsx("div",{className:"flex min-h-[120px] items-center justify-center rounded-lg border-2 border-dashed border-gray-200 bg-gray-50",children:t.jsxs("div",{className:"text-center text-gray-500",children:[t.jsx("p",{className:"text-sm",children:"Chưa có cấu hình khung thời gian nào"}),t.jsx("p",{className:"mt-1 text-xs",children:'Nhấn "Thêm cấu hình" để bắt đầu'})]})}):t.jsx("div",{className:"space-y-3",children:p.map(c=>{var d;return t.jsxs("div",{className:"flex cursor-pointer items-start gap-3 rounded-lg border border-gray-200 bg-white p-4",onClick:h=>{h.preventDefault(),h.stopPropagation(),v(c.id),q({amount:Number(c.price||0),startDate:c.startDate,endDate:c.endDate,selectedDays:c.selectedDays,selectedHours:c.selectedHours}),N(!0)},children:[t.jsx("div",{className:"mt-1 h-2 w-2 rounded-full bg-gray-400"}),t.jsxs("div",{className:"flex-1 space-y-1",children:[t.jsxs("div",{className:"text-sm text-gray-900",children:["Từ ngày ",t.jsx("span",{className:"font-semibold",children:le(c.startDate)})," đến ngày"," ",t.jsx("span",{className:"font-semibold",children:le(c.endDate)})]}),t.jsxs("div",{className:"text-sm text-gray-900",children:["Giá: ",t.jsxs("span",{className:"font-semibold",children:[((d=c.price)==null?void 0:d.toLocaleString("vi-VN"))||"0"," ₫"]})]}),t.jsxs("div",{className:"text-sm text-gray-900",children:["Khung giờ ",t.jsx("span",{className:"font-semibold",children:Ne(c.selectedHours)})," Thứ"," ",t.jsx("span",{className:"font-semibold",children:je(c.selectedDays)})]})]}),t.jsx(j,{type:"button",variant:"ghost",size:"sm",onClick:()=>C(c.id),className:"h-8 w-8 p-0 text-gray-400 hover:text-gray-600",children:t.jsx(be,{className:"h-4 w-4"})})]},c.id)})})]}),t.jsxs(ne,{className:"pt-4",children:[t.jsx(se,{asChild:!0,children:t.jsx(j,{type:"button",variant:"outline",children:"Hủy"})}),t.jsx(j,{type:"submit",children:"Xác nhận"})]})]})})]}),t.jsx(Te,{open:y,onOpenChange:N,onConfirm:K,sourceName:(a==null?void 0:a.sourceName)||"",data:F})]})}export{ot as B,ct as C,rt as I,pt as P,et as a,nt as b,mt as c,st as d,it as e,Ze as f,lt as g,tt as h,ye as i,ut as j,Je as k,ht as l,dt as p,at as u};
