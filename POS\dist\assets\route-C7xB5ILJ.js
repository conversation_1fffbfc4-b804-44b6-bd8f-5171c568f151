import{j as r,O as t}from"./index-DZ2N7iEN.js";import{C as i}from"./index-_PrUA_f4.js";import"./date-range-picker-extpnOqj.js";import"./search-context-bUKMT4ET.js";import"./pos-api-PZMeNc3U.js";import"./form-CUvXDg29.js";import"./main-DEy6aM-s.js";import"./index-BZV3Nu1l.js";import"./calendar-CoHe9sRq.js";import"./createLucideIcon-2r9cCEY3.js";import"./index-CC24pdSB.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-D-SI6PAy.js";import"./react-icons.esm-Ck8h5xF8.js";import"./popover-BK8t3srL.js";import"./select-B60YVMMU.js";import"./index-Csh0LmDQ.js";import"./index-DIwSqRjm.js";import"./check-CP51yA4o.js";import"./command-By9h7q-C.js";import"./dialog-DDb7K8l1.js";import"./search-Cr9zvKK2.js";import"./createReactComponent-CQMxkbLi.js";import"./scroll-area-CoEnZUVR.js";import"./IconChevronRight-Cvwf7k8w.js";const o="...",K=function(){return r.jsx(i,{publishableKey:o,afterSignOutUrl:"/clerk/sign-in",signInUrl:"/clerk/sign-in",signUpUrl:"/clerk/sign-up",signInFallbackRedirectUrl:"/clerk/user-management",signUpFallbackRedirectUrl:"/clerk/user-management",children:r.jsx(t,{})})};export{K as component};
