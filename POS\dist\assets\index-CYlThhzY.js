import{j as e,r as A,h as Q,a4 as M,ay as z,aA as q,e as $,B as y}from"./index-DZ2N7iEN.js";import"./date-range-picker-extpnOqj.js";import{L as C}from"./form-CUvXDg29.js";import{I as Y}from"./input-DpObCffE.js";import{T as J,a as Z,c as x,b as W}from"./tabs-BB7Je59V.js";import{C as P}from"./checkbox-Bqls14Dj.js";import{C as U,a as f,b as X}from"./collapsible-Dk62AUDY.js";import{C as B}from"./select-B60YVMMU.js";import{c as v,d as ee,e as Ee}from"./use-roles-RquLkYNb.js";import{X as se}from"./calendar-CoHe9sRq.js";const ne={"ACCOUNT.VIEW":"Xem","ACCOUNT.CHANGE_PWD":"Đổi mật khẩu","ACCOUNT.DEL_MENU":"Xóa thực đơn","ACCOUNT.CREATE_ACCOUNT":"Tạo tài khoản","ACCOUNT.CREATE_CASHIER":"Tạo thu ngân","BRAND.VIEW":"Xem","BRAND.CREATE":"Thêm","BRAND.EDIT":"Sửa","PIN.VIEW":"Xem, đổi mã pin","STORE.VIEW":"Xem","STORE.CREATE":"Thêm","STORE.EDIT":"Sửa","PMT_METHOD.VIEW":"Xem","PMT_METHOD.CREATE":"Thêm","PMT_METHOD.EDIT":"Sửa","PMT_METHOD.DELETE":"Xóa","SOURCE.VIEW":"Xem","SOURCE.CREATE":"Thêm","SOURCE.EDIT":"Sửa","SOURCE.DELETE":"Xóa","PRINTER.VIEW":"Xem","PRINTER.CREATE":"Thêm","PRINTER.EDIT":"Sửa","PRINTER.DELETE":"Xóa","STORE_PRINTER.VIEW":"Xem","STORE_PRINTER.CREATE":"Thêm","STORE_PRINTER.EDIT":"Sửa","STORE_PRINTER.DELETE":"Xóa","AREA.VIEW":"Xem","AREA.CREATE":"Thêm","AREA.EDIT":"Sửa","AREA.DELETE":"Xóa","AREA.ATTACH_AREA_TABLE":"Gán bàn vào khu vực","TABLE.VIEW":"Xem","TABLE.CREATE":"Thêm","TABLE.EDIT":"Sửa","TABLE.DELETE":"Xóa","BILL_TEMPLATE.VIEW":"Xem","BILL_TEMPLATE.EDIT":"Sửa","BRAND_ITEM.VIEW":"Xem","BRAND_ITEM.CREATE":"Thêm","BRAND_ITEM.EDIT":"Sửa","BRAND_ITEM.DELETE":"Xóa","STORE_ITEM.VIEW":"Xem","STORE_ITEM.CREATE":"Thêm","STORE_ITEM.EDIT":"Sửa","STORE_ITEM.DELETE":"Xóa","ITEM_CATEGORIES.VIEW":"Xem","ITEM_CATEGORIES.CREATE":"Thêm","ITEM_CATEGORIES.EDIT":"Sửa","ITEM_CATEGORIES.DELETE":"Xóa","STORE_ITEM_CATEGORIES.VIEW":"Xem","STORE_ITEM_CATEGORIES.CREATE":"Thêm","STORE_ITEM_CATEGORIES.EDIT":"Sửa","STORE_ITEM_CATEGORIES.DELETE":"Xóa","ITEM_TYPES.VIEW":"Xem","ITEM_TYPES.CREATE":"Thêm","ITEM_TYPES.EDIT":"Sửa","ITEM_TYPES.DELETE":"Xóa","CITY_CUS_ITEM.VIEW":"Xem","CITY_CUS_ITEM.CREATE":"Thêm","CITY_CUS_ITEM.EDIT":"Sửa","CITY_CUS_ITEM.DELETE":"Xóa","STORE_CUS_ITEM.VIEW":"Xem","STORE_CUS_ITEM.CREATE":"Thêm","STORE_CUS_ITEM.EDIT":"Sửa","STORE_CUS_ITEM.DELETE":"Xóa","SCHEDULE.VIEW":"Xem","SCHEDULE.CREATE":"Thêm","SCHEDULE.EDIT":"Sửa","SCHEDULE.DELETE":"Xóa","PROMO.VIEW":"Xem","PROMO.CREATE":"Thêm","PROMO.EDIT":"Sửa","COMBO.VIEW":"Xem","COMBO.CREATE":"Thêm","COMBO.EDIT":"Sửa","DISCOUNT.VIEW":"Xem","DISCOUNT.CREATE":"Thêm","DISCOUNT.EDIT":"Sửa","MEMBER_DISCOUNT.VIEW":"Xem","MEMBER_DISCOUNT.CREATE":"Thêm","MEMBER_DISCOUNT.EDIT":"Sửa","DISCOUNT_EXTRA.VIEW":"Xem","DISCOUNT_EXTRA.CREATE":"Thêm","DISCOUNT_EXTRA.EDIT":"Sửa","SERVICE_CHARGE.VIEW":"Xem","SERVICE_CHARGE.CREATE":"Thêm","SERVICE_CHARGE.EDIT":"Sửa","ROLE.VIEW":"Xem","ROLE.CREATE":"Thêm","ROLE.EDIT":"Sửa","ROLE.DELETE":"Xóa","EMPLOYEE.VIEW":"Xem","EMPLOYEE.CREATE":"Thêm","EMPLOYEE.EDIT":"Sửa","SALE_CHANNEL.VIEW":"Xem","SALE_CHANNEL.CREATE":"Thêm","SALE_CHANNEL.EDIT":"Sửa","SALE_CHANNEL.DELETE":"Xóa","SALE_CHANNEL_PROMO.VIEW":"Xem","SALE_CHANNEL_PROMO.CREATE":"Thêm","SALE_CHANNEL_PROMO.EDIT":"Sửa","SALE_CHANNEL_PROMO.DELETE":"Xóa","SALE_CHANNEL_RECONCILE.VIEW":"Xem","SALE_CHANNEL_RECONCILE.PAYMENT":"Thanh toán","SALE_CHANNEL_RECONCILE.EXCEL_EXPORT":"Xuất Excel","DEVICE.VIEW":"Xem","DEVICE.CREATE":"Thêm","DEVICE.EDIT":"Sửa","DEBT.PAYMENT":"Thanh toán công nợ","REPORT.VIEW":"Xem","REPORT.DELETE_SALE":"Xoá hoá đơn","REPORT.EDIT_SALE":"Sửa hoá đơn","REPORT.EXPORT_VAT":"Xuất VAT","REPORT.CLONE_SALE":"Sao chép hoá đơn","APPLICATION.VIEW":"Xem","APPLICATION.CONNECT":"Kết nối","KTV.VIEW":"Xem"},G=[{id:"account-settings",title:"Tài khoản và cài đặt",items:[{id:"account-info",label:"Thông tin tài khoản",permissions:["ACCOUNT.VIEW","ACCOUNT.CHANGE_PWD","ACCOUNT.DEL_MENU","ACCOUNT.CREATE_ACCOUNT","ACCOUNT.CREATE_CASHIER"]},{id:"brand",label:"Thương hiệu",permissions:["BRAND.VIEW","BRAND.CREATE","BRAND.EDIT"]},{id:"pin-management",label:"Cho phép hiển thị, quản lý mã PIN",permissions:["PIN.VIEW"]}]},{id:"restaurant",title:"Nhà hàng",items:[{id:"store-list",label:"Danh sách điểm bán hàng",permissions:["STORE.VIEW","STORE.CREATE","STORE.EDIT"]},{id:"payment-methods",label:"Phương thức thanh toán",permissions:["PMT_METHOD.VIEW","PMT_METHOD.CREATE","PMT_METHOD.EDIT","PMT_METHOD.DELETE"]},{id:"order-sources",label:"Nguồn đơn hàng",permissions:["SOURCE.VIEW","SOURCE.CREATE","SOURCE.EDIT","SOURCE.DELETE"]},{id:"printer-locations",label:"Vị trí máy in",permissions:["PRINTER.VIEW","PRINTER.CREATE","PRINTER.EDIT","PRINTER.DELETE"]},{id:"store-printer-locations",label:"Vị trí máy in theo cửa hàng",permissions:["STORE_PRINTER.VIEW","STORE_PRINTER.CREATE","STORE_PRINTER.EDIT","STORE_PRINTER.DELETE"]},{id:"areas",label:"Khu vực",permissions:["AREA.VIEW","AREA.CREATE","AREA.EDIT","AREA.DELETE","AREA.ATTACH_AREA_TABLE"]},{id:"tables",label:"Bàn",permissions:["TABLE.VIEW","TABLE.CREATE","TABLE.EDIT","TABLE.DELETE"]},{id:"invoice-templates",label:"Mẫu hóa đơn",permissions:["BILL_TEMPLATE.VIEW","BILL_TEMPLATE.EDIT"]}]},{id:"menu",title:"Thực đơn",items:[{id:"brand-items",label:"Món ăn toàn thương hiệu",permissions:["BRAND_ITEM.VIEW","BRAND_ITEM.CREATE","BRAND_ITEM.EDIT","BRAND_ITEM.DELETE"]},{id:"store-items",label:"Món ăn theo cửa hàng",permissions:["STORE_ITEM.VIEW","STORE_ITEM.CREATE","STORE_ITEM.EDIT","STORE_ITEM.DELETE"]},{id:"item-groups",label:"Nhóm món",permissions:["ITEM_CATEGORIES.VIEW","ITEM_CATEGORIES.CREATE","ITEM_CATEGORIES.EDIT","ITEM_CATEGORIES.DELETE"]},{id:"store-item-groups",label:"Nhóm món theo cửa hàng",permissions:["STORE_ITEM_CATEGORIES.VIEW","STORE_ITEM_CATEGORIES.CREATE","STORE_ITEM_CATEGORIES.EDIT","STORE_ITEM_CATEGORIES.DELETE"]},{id:"item-types",label:"Loại món",permissions:["ITEM_TYPES.VIEW","ITEM_TYPES.CREATE","ITEM_TYPES.EDIT","ITEM_TYPES.DELETE"]},{id:"city-customizations",label:"Customization toàn thành phố",permissions:["CITY_CUS_ITEM.VIEW","CITY_CUS_ITEM.CREATE","CITY_CUS_ITEM.EDIT","CITY_CUS_ITEM.DELETE"]},{id:"store-customizations",label:"Customization theo cửa hàng",permissions:["STORE_CUS_ITEM.VIEW","STORE_CUS_ITEM.CREATE","STORE_CUS_ITEM.EDIT","STORE_CUS_ITEM.DELETE"]},{id:"menu-scheduling",label:"Lịch trình thực đơn",permissions:["SCHEDULE.VIEW","SCHEDULE.CREATE","SCHEDULE.EDIT","SCHEDULE.DELETE"]}]},{id:"programs",title:"Chương trình",items:[{id:"promotions",label:"Chương trình khuyến mãi",permissions:["PROMO.VIEW","PROMO.CREATE","PROMO.EDIT"]},{id:"combos",label:"Combos",permissions:["COMBO.VIEW","COMBO.CREATE","COMBO.EDIT"]},{id:"discounts",label:"Giảm giá",permissions:["DISCOUNT.VIEW","DISCOUNT.CREATE","DISCOUNT.EDIT"]},{id:"member-discounts",label:"Giảm giá hội viên",permissions:["MEMBER_DISCOUNT.VIEW","MEMBER_DISCOUNT.CREATE","MEMBER_DISCOUNT.EDIT"]},{id:"payment-discounts",label:"Chiết khấu thanh toán",permissions:["DISCOUNT_EXTRA.VIEW","DISCOUNT_EXTRA.CREATE","DISCOUNT_EXTRA.EDIT"]},{id:"service-charges",label:"Phí dịch vụ",permissions:["SERVICE_CHARGE.VIEW","SERVICE_CHARGE.CREATE","SERVICE_CHARGE.EDIT"]}]},{id:"employees",title:"Nhân viên",items:[{id:"roles",label:"Chức vụ",permissions:["ROLE.VIEW","ROLE.CREATE","ROLE.EDIT","ROLE.DELETE"]},{id:"employees",label:"Nhân viên",permissions:["EMPLOYEE.VIEW","EMPLOYEE.CREATE","EMPLOYEE.EDIT"]}]},{id:"sales-channels",title:"Kênh bán hàng",items:[{id:"sales-channels",label:"Kênh bán hàng",permissions:["SALE_CHANNEL.VIEW","SALE_CHANNEL.CREATE","SALE_CHANNEL.EDIT","SALE_CHANNEL.DELETE"]},{id:"channel-discounts",label:"Chiết khấu kênh",permissions:["SALE_CHANNEL_PROMO.VIEW","SALE_CHANNEL_PROMO.CREATE","SALE_CHANNEL_PROMO.EDIT","SALE_CHANNEL_PROMO.DELETE"]},{id:"reconciliation",label:"Đối soát",permissions:["SALE_CHANNEL_RECONCILE.VIEW","SALE_CHANNEL_RECONCILE.PAYMENT","SALE_CHANNEL_RECONCILE.EXCEL_EXPORT"]}]},{id:"devices",title:"Thiết bị",items:[{id:"devices",label:"Thiết bị",permissions:["DEVICE.VIEW","DEVICE.CREATE","DEVICE.EDIT"]}]},{id:"debt-management",title:"Quản lý công nợ",items:[{id:"debt-payment",label:"Thanh toán công nợ",permissions:["DEBT.PAYMENT"]}]},{id:"reports",title:"Báo cáo",items:[{id:"reports",label:"Báo cáo",permissions:["REPORT.VIEW","REPORT.DELETE_SALE","REPORT.EDIT_SALE","REPORT.EXPORT_VAT","REPORT.CLONE_SALE"]}]},{id:"applications",title:"Ứng dụng",items:[{id:"applications",label:"Ứng dụng",permissions:["APPLICATION.VIEW","APPLICATION.CONNECT"]}]},{id:"accounting-vo",title:"Kế toán Vo",items:[{id:"accounting-vo",label:"Kế toán Vo",permissions:["KTV.VIEW"]}]}],ie={"MANAGER.PIN":"Quản lý mã PIN","MANAGER_REPORT.VIEW":"Xem báo cáo","MANAGER_KTV.VIEW":"Xem kế toán","MANAGER_KTV.FUND_VIEW":"Xem quỹ tiền","MANAGER_KTV.REPORT_VIEW":"Xem báo cáo kế toán","MANAGER_KTV.SUPPLIER_VIEW":"Xem nhà cung cấp","MANAGER_MARKET.VIEW":"Xem marketplace","MANAGER_STORE.VIEW":"Xem cửa hàng online","MANAGER_ITEM.VIEW":"Xem thực đơn"},H=[{id:"pin",title:"PIN",items:[{id:"pin-management",label:"Cho phép hiển thị, quản lý mã PIN tại app MANAGER",permissions:["MANAGER.PIN"]}]},{id:"reports",title:"Báo cáo",items:[{id:"report-management",label:"Quản lý Báo cáo",permissions:["MANAGER_REPORT.VIEW"]}]},{id:"accounting-vo",title:"Kế Toán Vo",items:[{id:"accounting-vo-management",label:"Quản lý Kế toán Vo",permissions:["MANAGER_KTV.VIEW","MANAGER_KTV.FUND_VIEW","MANAGER_KTV.REPORT_VIEW","MANAGER_KTV.SUPPLIER_VIEW"]}]},{id:"marketplace",title:"Marketplace",items:[{id:"marketplace-management",label:"Quản lý Marketplace",permissions:["MANAGER_MARKET.VIEW"]}]},{id:"online-store",title:"CH online",items:[{id:"online-store-management",label:"Quản lý CH online",permissions:["MANAGER_STORE.VIEW"]}]},{id:"menu",title:"Thực đơn",items:[{id:"menu-management",label:"Quản lý thực đơn",permissions:["MANAGER_ITEM.VIEW"]}]}],ae={"SALE_ITEM.DROP":"Bỏ món","SALE_ITEM.ASSIGN":"Gán bàn","SALE_ITEM.CUSTOM":"Tạo món tùy chọn","SALE_PROMO.CHANGE_PRICE":"Sửa giá trực tiếp","SALE_PROMO.ITEM_DISCOUNT":"Nhận giảm giá món","SALE_PROMO.DISCOUNT_DIRECT":"Nhập chiết khấu trực tiếp","SALE_PROMO.DISCOUNT_PAYMENT":"Chọn chiết khấu thanh toán","SALE_PROMO.SVC":"Phí dịch vụ","SALE_PROMO.VAT":"VAT (cho phép nhận điều chỉnh Vat)","SALE_PROMO.E_VOUCHER":"E-Voucher","SALE_PROMO.SHIP_FEE":"Phí vận chuyển","POS_PAYMENT.TEMPORARY":"Tạm tính","POS_PAYMENT.PMT":"Thanh toán","POS_UTILITY.MERGE":"Gộp đơn","POS_UTILITY.SPLIT":"Tách đơn","POS_UTILITY.MOVE_TAB":"Chuyển bàn","POS_UTILITY.MERGE_TAB":"Ghép bàn","POS_UTILITY.LOCK_ACTION_AFTER_PRINT":"Khóa thao tác sau khi in tạm tính","POS_UTILITY.CONFIRM":"Xác nhận món đã phục vụ","POS_UTILITY.REQUEST":"Yêu cầu bỏ món (KDS)","POS_UTILITY.PRIO":"Ưu tiên món ăn (KDS)","POS_UTILITY.PRIO_TAB":"Ưu tiên bàn (KDS)","POS_UTILITY.REPRINT_ORDER":"In lại phiếu order","POS_UTILITY.UPDATE_MEMBER":"Chỉnh sửa thông tin hội viên","SHIFT.VIEW":"Xem ca","SHIFT.HISTORY":"Xem danh sách ca quá khứ","SHIFT.OPEN":"Mở ca","SHIFT.CLOSE":"Đóng ca","SHIFT.DETAIL":"Xem chi tiết của ca","SHIFT.REPRINT":"In lại báo cáo ca","SHIFT.REUSE":"Sử dụng ca của nhân viên khác","BILL.VIEW":"Xem hóa đơn","BILL.EDIT":"Sửa hóa đơn","BILL.DELETE":"Xóa hóa đơn","BILL.REPRINT":"In lại hóa đơn","BILL.EXPORT_VAT":"Xuất VAT hóa đơn","BILL.EDIT_PAYMENT":"Sửa phương thức thanh toán","CONNECT.VIEW":"Xem kết nối","BOOKING.VIEW":"Xem đặt bàn","BOOKING.CREATE":"Tạo đặt bàn","BOOKING.MOVE":"Chuyển vào bàn","BOOKING.INSERT":"Nhập mã booking","BOOKING.REPORT":"Báo cáo đặt bàn","CANTEEN.VIEW":"Xem căng tin","POS_ITEM.VIEW":"Xem món ăn","POS_ITEM.CREATE":"Thêm món ăn","POS_ITEM.EDIT":"Sửa món ăn","POS_ITEM.CITY":"Quản lý món ăn cấp thành phố","POS_ITEM_TYPE.VIEW":"Xem nhóm món","POS_ITEM_TYPE.CREATE":"Thêm nhóm món","POS_ITEM_TYPE.EDIT":"Sửa nhóm món","POS_ITEM_TYPE.STORE_VIEW":"Xem nhóm món tại cửa hàng","POS_ITEM_TYPE.STORE_CREATE_EDIT":"Thêm/sửa nhóm món tại cửa hàng","POS_CUS.VIEW":"Xem customization","POS_CUS.CREATE":"Thêm customization","POS_CUS.EDIT":"Sửa customization","POS_CUS.STORE_VIEW":"Xem customization tại cửa hàng","POS_CUS.STORE_CREATE_EDIT":"Thêm/sửa customization tại cửa hàng","POS_PROMO.VIEW":"Xem khuyến mãi","POS_PROMO.CREATE":"Thêm khuyến mãi","POS_PROMO.EDIT":"Sửa khuyến mãi","POS_COMBO.VIEW":"Xem combo","POS_COMBO.CREATE":"Thêm combo","POS_COMBO.EDIT":"Sửa combo","POS_DISCOUNT.VIEW":"Xem giảm giá","POS_DISCOUNT.CREATE":"Thêm giảm giá","POS_DISCOUNT.EDIT":"Sửa giảm giá","POS_DISCOUNT_PAYMENT.VIEW":"Xem giảm giá thanh toán","POS_DISCOUNT_PAYMENT.CREATE":"Thêm giảm giá thanh toán","POS_DISCOUNT_PAYMENT.EDIT":"Sửa giảm giá thanh toán","POS_SVC.VIEW":"Xem phí dịch vụ","POS_SVC.CREATE":"Thêm phí dịch vụ","POS_SVC.EDIT":"Sửa phí dịch vụ","DM_PAYMENT.VIEW":"Xem phương thức thanh toán","DM_PAYMENT.CREATE":"Thêm phương thức thanh toán","DM_PAYMENT.EDIT":"Sửa phương thức thanh toán","POS_REPORT.VIEW":"Xem báo cáo","POS_PRINTER.VIEW":"Xem máy in","POS_PRINTER.CREATE":"Thêm máy in","POS_PRINTER.EDIT":"Sửa máy in","POS_PRINTER.DELETE":"Xóa máy in","POSITION_PRINTER.VIEW":"Xem vị trí máy in","POSITION_PRINTER.EDIT":"Sửa vị trí máy in","POSITION_PRINTER.DELETE":"Xóa vị trí máy in","POSITION_PRINTER.STORE_VIEW":"Xem vị trí máy in tại cửa hàng","POSITION_PRINTER.STORE_EDIT":"Sửa vị trí máy in tại cửa hàng","POSITION_PRINTER.STORE_DELETE":"Xóa vị trí máy in tại cửa hàng","POS_AREA.VIEW":"Xem khu vực","POS_AREA.CREATE":"Thêm khu vực","POS_AREA.EDIT":"Sửa khu vực","POS_TAB.VIEW":"Xem bàn","POS_TAB.CREATE":"Thêm bàn","POS_TAB.EDIT":"Sửa bàn","POS_SOURCE.VIEW":"Xem nguồn đơn","POS_SOURCE.CREATE":"Thêm nguồn đơn","POS_SOURCE.EDIT":"Sửa nguồn đơn","POS_STORE.VIEW":"Xem cửa hàng","POS_STORE.EDIT":"Sửa cửa hàng","POS_HISTORY.VIEW":"Xem lịch sử","CASH_IN_OUT.VIEW":"Xem thu chi","CASH_IN_OUT.CREATE":"Thêm thu chi","CASH_IN_OUT.EDIT":"Sửa thu chi","EXPORT_BAR.VIEW":"Xem xuất mã vạch","EXPORT_BAR.CREATE":"Tạo xuất mã vạch","POS_DEBT.VIEW":"Xem công nợ","POS_DEBT.CREATE":"Thêm công nợ","POS_DEBT.PAYMENT":"Thanh toán công nợ","EXPORT_VAT.VIEW":"Xem xuất VAT","EXPORT_VAT.EXPORT":"Xuất VAT","POS_DEVICE.VIEW":"Xem thiết bị","POS_DEVICE.EDIT":"Sửa thiết bị",MANAGER_PERMISSION:"Quyền đại diện quản lý"},k=[{id:"sales",title:"Bán hàng",items:[{id:"food-items",label:"Món ăn",permissions:["SALE_ITEM.DROP","SALE_ITEM.ASSIGN","SALE_ITEM.CUSTOM"]},{id:"promotions",label:"Khuyến mãi",permissions:["SALE_PROMO.CHANGE_PRICE","SALE_PROMO.ITEM_DISCOUNT","SALE_PROMO.DISCOUNT_DIRECT","SALE_PROMO.DISCOUNT_PAYMENT","SALE_PROMO.SVC","SALE_PROMO.VAT","SALE_PROMO.E_VOUCHER","SALE_PROMO.SHIP_FEE"]},{id:"payment",label:"Thanh toán",permissions:["POS_PAYMENT.TEMPORARY","POS_PAYMENT.PMT"]},{id:"utilities",label:"Tiện ích",permissions:["POS_UTILITY.MERGE","POS_UTILITY.SPLIT","POS_UTILITY.MOVE_TAB","POS_UTILITY.MERGE_TAB","POS_UTILITY.LOCK_ACTION_AFTER_PRINT","POS_UTILITY.CONFIRM","POS_UTILITY.REQUEST","POS_UTILITY.PRIO","POS_UTILITY.PRIO_TAB","POS_UTILITY.REPRINT_ORDER","POS_UTILITY.UPDATE_MEMBER"]}]},{id:"shift-management",title:"Quản lý ca",items:[{id:"shift-management",label:"Ca bán hàng",permissions:["SHIFT.VIEW","SHIFT.HISTORY","SHIFT.OPEN","SHIFT.CLOSE","SHIFT.DETAIL","SHIFT.REPRINT","SHIFT.REUSE"]},{id:"invoices",label:"Hóa đơn",permissions:["BILL.VIEW","BILL.EDIT","BILL.DELETE","BILL.REPRINT","BILL.EXPORT_VAT","BILL.EDIT_PAYMENT"]}]},{id:"connection-management",title:"Quản lý kết nối",items:[{id:"connection-management",label:"Quản lý kết nối",permissions:["CONNECT.VIEW"]}]},{id:"reservation-management",title:"Quản lý đặt bàn",items:[{id:"reservation-management",label:"Quản lý đặt bàn",permissions:["BOOKING.VIEW","BOOKING.CREATE","BOOKING.MOVE","BOOKING.INSERT","BOOKING.REPORT"]}]},{id:"canteen",title:"Căng tin",items:[{id:"canteen",label:"Căng tin",permissions:["CANTEEN.VIEW"]}]},{id:"menu",title:"Thực đơn",items:[{id:"menu-items",label:"Món ăn",permissions:["POS_ITEM.VIEW","POS_ITEM.CREATE","POS_ITEM.EDIT","POS_ITEM.CITY"]},{id:"brand-item-groups",label:"Nhóm món",permissions:["POS_ITEM_TYPE.VIEW","POS_ITEM_TYPE.CREATE","POS_ITEM_TYPE.EDIT","POS_ITEM_TYPE.STORE_VIEW","POS_ITEM_TYPE.STORE_CREATE_EDIT"]},{id:"customization",label:"Customization",permissions:["POS_CUS.VIEW","POS_CUS.CREATE","POS_CUS.EDIT","POS_CUS.STORE_VIEW","POS_CUS.STORE_CREATE_EDIT"]}]},{id:"sales-programs",title:"Chương trình bán hàng",items:[{id:"promotion-programs",label:"Chương trình khuyến mãi",permissions:["POS_PROMO.VIEW","POS_PROMO.CREATE","POS_PROMO.EDIT"]},{id:"combos",label:"Combos",permissions:["POS_COMBO.VIEW","POS_COMBO.CREATE","POS_COMBO.EDIT"]},{id:"discounts",label:"Giảm giá",permissions:["POS_DISCOUNT.VIEW","POS_DISCOUNT.CREATE","POS_DISCOUNT.EDIT"]},{id:"payment-discounts",label:"Chiết khấu thanh toán",permissions:["POS_DISCOUNT_PAYMENT.VIEW","POS_DISCOUNT_PAYMENT.CREATE","POS_DISCOUNT_PAYMENT.EDIT"]},{id:"service-fees",label:"Phí dịch vụ",permissions:["POS_SVC.VIEW","POS_SVC.CREATE","POS_SVC.EDIT"]}]},{id:"payment-methods",title:"Phương thức thanh toán",items:[{id:"payment-methods",label:"Phương thức thanh toán",permissions:["DM_PAYMENT.VIEW","DM_PAYMENT.CREATE","DM_PAYMENT.EDIT"]}]},{id:"reports",title:"Báo cáo",items:[{id:"reports",label:"Báo cáo",permissions:["POS_REPORT.VIEW"]}]},{id:"printer-management",title:"Quản lý máy in",items:[{id:"printer-management",label:"Quản lý máy in",permissions:["POS_PRINTER.VIEW","POS_PRINTER.CREATE","POS_PRINTER.EDIT","POS_PRINTER.DELETE"]},{id:"printer-locations",label:"Vị trí máy in",permissions:["POSITION_PRINTER.VIEW","POSITION_PRINTER.EDIT","POSITION_PRINTER.DELETE","POSITION_PRINTER.STORE_VIEW","POSITION_PRINTER.STORE_EDIT","POSITION_PRINTER.STORE_DELETE"]}]},{id:"area-management",title:"Quản lý khu vực",items:[{id:"area-management",label:"Quản lý khu vực",permissions:["POS_AREA.VIEW","POS_AREA.CREATE","POS_AREA.EDIT"]},{id:"table-management",label:"Quản lý bàn",permissions:["POS_TAB.VIEW","POS_TAB.CREATE","POS_TAB.EDIT"]}]},{id:"order-source-management",title:"Quản lý nguồn đơn",items:[{id:"order-source-management",label:"Quản lý nguồn đơn",permissions:["POS_SOURCE.VIEW","POS_SOURCE.CREATE","POS_SOURCE.EDIT"]}]},{id:"store-settings",title:"Thiết lập cửa hàng",items:[{id:"store-settings",label:"Thiết lập cửa hàng",permissions:["POS_STORE.VIEW","POS_STORE.EDIT"]}]},{id:"order-log",title:"Nhật ký order",items:[{id:"order-log",label:"Nhật ký order",permissions:["POS_HISTORY.VIEW"]}]},{id:"cash-management",title:"Quản lý thu chi",items:[{id:"cash-management",label:"Quản lý thu chi",permissions:["CASH_IN_OUT.VIEW","CASH_IN_OUT.CREATE","CASH_IN_OUT.EDIT"]}]},{id:"barcode-export",title:"Xuất mã vạch",items:[{id:"barcode-export",label:"Xuất mã vạch",permissions:["EXPORT_BAR.VIEW","EXPORT_BAR.CREATE"]}]},{id:"debt-management",title:"Quản lý công nợ",items:[{id:"debt-management",label:"Quản lý công nợ",permissions:["POS_DEBT.VIEW","POS_DEBT.CREATE","POS_DEBT.PAYMENT"]}]},{id:"vat-export",title:"Xuất VAT",items:[{id:"vat-export",label:"Xuất VAT",permissions:["EXPORT_VAT.VIEW","EXPORT_VAT.EXPORT"]}]},{id:"device-management",title:"Quản lý thiết bị",items:[{id:"device-management",label:"Quản lý thiết bị",permissions:["POS_DEVICE.VIEW","POS_DEVICE.EDIT"]}]},{id:"management-permissions",title:"Các quyền dành cho cấp quản lý",items:[{id:"management-representative",label:"Quyền đại diện quản lý và có quyền ưu tiên trong một số chức năng",permissions:["MANAGER_PERMISSION"]}]}];function te({permissions:n,onParentPermissionChange:o,onChildPermissionChange:t}){return e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Quyền truy cập hệ thống quản lý nội dung (CMS)"}),e.jsx("div",{className:"space-y-3",children:G.map(i=>e.jsxs("div",{children:[e.jsx("h3",{className:"mb-3 text-base font-semibold",children:i.title}),e.jsx("div",{className:"space-y-2",children:i.items.map(s=>{const a=`cms-${s.id}`,T=s.permissions||[];return e.jsxs(U,{children:[e.jsxs(f,{className:"hover:bg-muted flex w-full items-center gap-3 rounded-md border p-3 text-left",children:[e.jsx(P,{id:a,checked:T.every(E=>n.includes(E)),onCheckedChange:E=>o(a,E,T),onClick:E=>E.stopPropagation()}),e.jsx("span",{className:"flex-1 text-sm font-medium",children:s.label}),e.jsx(B,{className:"h-4 w-4 transition-transform duration-200 data-[state=open]:rotate-180"})]}),e.jsx(X,{className:"bg-slate-100 p-3",children:e.jsx("div",{className:"mx-6",children:s.permissions&&s.permissions.length>0?e.jsx("div",{className:"flex flex-wrap gap-4",children:s.permissions.map(E=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{id:E,checked:n.includes(E),onCheckedChange:l=>t(E,l,a,T)}),e.jsx(C,{htmlFor:E,className:"cursor-pointer text-sm font-normal",children:ne[E]||E})]},E))}):e.jsx("p",{className:"text-muted-foreground text-xs",children:"Quyền chi tiết sẽ được thêm sau"})})})]},s.id)})})]},i.id))})]})}function Te({permissions:n,onParentPermissionChange:o,onChildPermissionChange:t}){return e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Quyền truy cập hệ thống bán hàng (POS/PDA)"}),e.jsx("div",{className:"space-y-3",children:k.map(i=>e.jsxs("div",{children:[e.jsx("h3",{className:"mb-3 text-base font-semibold",children:i.title}),e.jsx("div",{className:"space-y-2",children:i.items.map(s=>{const a=`pos-pda-${s.id}`,T=s.permissions||[];return e.jsxs(U,{children:[e.jsxs(f,{className:"hover:bg-muted flex w-full items-center gap-3 rounded-md border p-3 text-left",children:[e.jsx(P,{id:a,checked:T.every(E=>n.includes(E)),onCheckedChange:E=>o(a,E,T),onClick:E=>E.stopPropagation()}),e.jsx("span",{className:"flex-1 text-sm font-medium",children:s.label}),e.jsx(B,{className:"h-4 w-4 transition-transform duration-200 data-[state=open]:rotate-180"})]}),e.jsx(X,{className:"bg-slate-100 p-3",children:e.jsx("div",{className:"mx-6",children:s.permissions&&s.permissions.length>0?e.jsx("div",{className:"flex flex-wrap gap-4",children:s.permissions.map(E=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{id:E,checked:n.includes(E),onCheckedChange:l=>t(E,l,a,T)}),e.jsx(C,{htmlFor:E,className:"cursor-pointer text-sm font-normal",children:ae[E]||E})]},E))}):e.jsx("p",{className:"text-muted-foreground text-xs",children:"Quyền chi tiết sẽ được thêm sau"})})})]},s.id)})})]},i.id))})]})}function le({permissions:n,onParentPermissionChange:o,onChildPermissionChange:t}){return e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Quyền quản lý và giám sát"}),e.jsx("div",{className:"space-y-3",children:H.map(i=>e.jsxs("div",{children:[e.jsx("h3",{className:"mb-3 text-base font-semibold",children:i.title}),e.jsx("div",{className:"space-y-2",children:i.items.map(s=>{const a=`manager-${s.id}`,T=s.permissions||[];return e.jsxs(U,{children:[e.jsxs(f,{className:"hover:bg-muted flex w-full items-center gap-3 rounded-md border p-3 text-left",children:[e.jsx(P,{id:a,checked:T.every(E=>n.includes(E)),onCheckedChange:E=>o(a,E,T),onClick:E=>E.stopPropagation()}),e.jsx("span",{className:"flex-1 text-sm font-medium",children:s.label}),e.jsx(B,{className:"h-4 w-4 transition-transform duration-200 data-[state=open]:rotate-180"})]}),e.jsx(X,{className:"bg-slate-100 p-3",children:e.jsx("div",{className:"mx-6",children:s.permissions&&s.permissions.length>0?e.jsx("div",{className:"flex flex-wrap gap-4",children:s.permissions.map(E=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{id:E,checked:n.includes(E),onCheckedChange:l=>t(E,l,a,T)}),e.jsx(C,{htmlFor:E,className:"cursor-pointer text-sm font-normal",children:ie[E]||E})]},E))}):e.jsx("p",{className:"text-muted-foreground text-xs",children:"Quyền chi tiết sẽ được thêm sau"})})})]},s.id)})})]},i.id))})]})}const me=({roleId:n,copyFromRoleId:o}={})=>{const[t,i]=A.useState(""),[s,a]=A.useState(""),T=!!n,E=!!o,{data:l,isLoading:_}=v(n||"",T),{data:m,isLoading:I}=v(o||"",E);A.useEffect(()=>{T&&l?(i(l.role_name||""),a(l.description||"")):E&&m&&(i(`Sao chép của ${m.role_name||""}`),a(m.description||""))},[T,l,E,m]);const O=t.trim()!==""&&s.trim()!=="";return{roleName:t,setRoleName:i,description:s,setDescription:a,isFormValid:O,resetForm:()=>{i(""),a("")},isEditMode:T,isCopyMode:E,roleData:l,copyFromRoleData:m,isLoading:_||I}},D=()=>{const n=[];return n.push("POS_CMS","POS_CLIENT","POS_MANAGER"),G.forEach(o=>{o.items.forEach(t=>{t.permissions&&t.permissions.forEach(i=>{n.push(i)})})}),k.forEach(o=>{o.items.forEach(t=>{t.permissions&&t.permissions.forEach(i=>{n.push(i)})})}),H.forEach(o=>{o.items.forEach(t=>{t.permissions&&t.permissions.forEach(i=>{n.push(i)})})}),n},oe=Object.freeze(Object.defineProperty({__proto__:null,getAllPermissions:D},Symbol.toStringTag,{value:"Module"})),Ie=({roleData:n,isEditMode:o,copyFromRoleId:t}={})=>{const[i,s]=A.useState([]),a=!!t,{data:T}=v(t||"",a);return A.useEffect(()=>{if(o&&n){const m=[...n.allow_access||[],...D().filter(I=>!["POS_CMS","POS_CLIENT","POS_MANAGER"].includes(I)&&!(n.reject_permissions||[]).includes(I))];s(m)}else if(a&&T){const m=[...T.allow_access||[],...D().filter(I=>!["POS_CMS","POS_CLIENT","POS_MANAGER"].includes(I)&&!(T.reject_permissions||[]).includes(I))];s(m)}else s(D())},[o,n,a,T]),{permissions:i,setPermissions:s,handlePermissionChange:(m,I)=>{s(I?O=>[...O,m]:O=>O.filter(h=>h!==m))},handleParentPermissionChange:(m,I,O)=>{s(h=>{let R=[...h];return I?O.forEach(S=>{R.includes(S)||R.push(S)}):R=R.filter(S=>!O.includes(S)),R})},handleChildPermissionChange:(m,I,O,h)=>{s(R=>{let S=[...R];return I?S.includes(m)||S.push(m):S=S.filter(d=>d!==m),S})}}},ce=({permissions:n,onPermissionChange:o})=>{const[t,i]=A.useState("cms"),a=(()=>{const l=[];return n.includes("POS_CMS")&&l.push("cms"),n.includes("POS_CLIENT")&&l.push("pos-pda"),n.includes("POS_MANAGER")&&l.push("manager"),l})(),T=a.length>0;return{activeTab:t,setActiveTab:i,enabledTabs:a,hasAnyEnabledTabs:T,handlePermissionChangeWithTabSwitch:(l,_)=>{o(l,_),["POS_CMS","POS_CLIENT","POS_MANAGER"].includes(l)&&setTimeout(()=>{const m=[],I=_?[...n,l]:n.filter(O=>O!==l);I.includes("POS_CMS")&&m.push("cms"),I.includes("POS_CLIENT")&&m.push("pos-pda"),I.includes("POS_MANAGER")&&m.push("manager"),!m.includes(t)&&m.length>0&&i(m[0])},0)}}},re=({roleName:n,description:o,permissions:t,isFormValid:i,isEditMode:s,roleData:a})=>{const T=Q(),E=ee(),l=Ee();return{handleBack:()=>{T({to:"/employee/role"})},handleSave:async()=>{if(!i){M.error("Vui lòng điền đầy đủ thông tin bắt buộc");return}try{const I=t.filter(r=>["POS_CMS","POS_CLIENT","POS_MANAGER"].includes(r)),O=t.filter(r=>!["POS_CMS","POS_CLIENT","POS_MANAGER"].includes(r)),{getAllPermissions:h}=await z(async()=>{const{getAllPermissions:r}=await Promise.resolve().then(()=>oe);return{getAllPermissions:r}},void 0),d=h().filter(r=>!["POS_CMS","POS_CLIENT","POS_MANAGER"].includes(r)).filter(r=>!O.includes(r)),u=localStorage.getItem("pos_company_data"),p=u?JSON.parse(u).id:null;if(s&&a){const r={allow_access:I,role_name:n,role_id:a.role_id,description:o,reject_permissions:d,id:a.id,scope:a.scope||"COMPANY",scope_value:a.scope_value||p};await l.mutateAsync(r),M.success("Cập nhật chức vụ thành công")}else{const L={allow_access:I,role_name:n,role_id:(g=>{const V=Math.random().toString(36).substring(2,6).toUpperCase();return`${g}_ROLE-${V}`})(n),description:o,reject_permissions:d,scope:"COMPANY",scope_value:p};await E.mutateAsync(L),M.success("Tạo chức vụ thành công")}T({to:"/employee/role"})}catch{M.error(s?"Có lỗi xảy ra khi cập nhật chức vụ":"Có lỗi xảy ra khi tạo chức vụ")}},createRoleMutation:E,updateRoleMutation:l,isLoading:E.isPending||l.isPending}};function ue({roleId:n}={}){let o=n,t;try{const c=q({strict:!1});!n&&c&&"roleId"in c&&(o=c.roleId)}catch{try{const c=window.location.pathname;if(c.includes("/employee/role/detail/")&&!n){const N=c.split("/"),b=N.findIndex(K=>K==="detail")+1;b>0&&N[b]&&(o=N[b])}}catch{}}try{const c=$({strict:!1});c&&"copyFromRoleId"in c&&(t=c.copyFromRoleId)}catch{}const i=!!o,s=!!t,{roleName:a,setRoleName:T,description:E,setDescription:l,isFormValid:_,roleData:m,copyFromRoleData:I,isLoading:O}=me({roleId:o,copyFromRoleId:t}),{permissions:h,handlePermissionChange:R,handleParentPermissionChange:S,handleChildPermissionChange:d}=Ie({roleData:i?m:s?I:void 0,isEditMode:i||s,copyFromRoleId:t}),{activeTab:u,setActiveTab:p,enabledTabs:r,hasAnyEnabledTabs:L,handlePermissionChangeWithTabSwitch:g}=ce({permissions:h,onPermissionChange:R}),{handleBack:V,handleSave:w,isLoading:j}=re({roleName:a,description:E,permissions:h,isFormValid:_,isEditMode:i,roleData:m}),F=[{value:"POS_CMS",label:"POS_CMS"},{value:"POS_CLIENT",label:"POS_CLIENT"},{value:"POS_MANAGER",label:"POS_MANAGER"}];return i&&O?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:"Đang tải thông tin chức vụ..."})})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(y,{variant:"ghost",size:"sm",onClick:V,className:"flex items-center",children:e.jsx(se,{className:"h-4 w-4"})}),e.jsx(y,{type:"button",disabled:j||!_,className:"min-w-[100px]",onClick:w,children:j?i?"Đang cập nhật...":"Đang tạo...":"Lưu"})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:i?"Chỉnh sửa chức vụ":"Tạo chức vụ mới"})})]}),e.jsxs("div",{className:"mx-auto max-w-4xl",children:[e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(C,{htmlFor:"role-name",className:"min-w-[200px] text-sm font-medium",children:["Tên chức vụ ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(Y,{id:"role-name",value:a,onChange:c=>T(c.target.value),placeholder:"Thêm chức vụ",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(C,{htmlFor:"description",className:"min-w-[200px] text-sm font-medium",children:["Mô tả ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(Y,{id:"description",value:E,onChange:c=>l(c.target.value),placeholder:"Thêm mô tả",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(C,{className:"min-w-[200px] text-sm font-medium",children:"Quyền truy cập"}),e.jsx("div",{className:"flex flex-1 items-center gap-6",children:F.map(c=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{id:c.value,checked:h.includes(c.value),onCheckedChange:N=>g(c.value,N)}),e.jsx(C,{htmlFor:c.value,className:"cursor-pointer text-sm font-normal",children:c.label})]},c.value))})]})]})}),e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Cấp quyền"}),L&&e.jsxs(J,{value:u,onValueChange:p,className:"w-full",children:[e.jsxs(Z,{className:"grid w-full grid-cols-3",children:[e.jsx(x,{value:"cms",disabled:!r.includes("cms"),className:r.includes("cms")?"":"cursor-not-allowed opacity-50",children:"CMS"}),e.jsx(x,{value:"pos-pda",disabled:!r.includes("pos-pda"),className:r.includes("pos-pda")?"":"cursor-not-allowed opacity-50",children:"POS/PDA"}),e.jsx(x,{value:"manager",disabled:!r.includes("manager"),className:r.includes("manager")?"":"cursor-not-allowed opacity-50",children:"MANAGER"})]}),e.jsx(W,{value:"cms",className:"mt-6",children:e.jsx(te,{permissions:h,onParentPermissionChange:S,onChildPermissionChange:d})}),e.jsx(W,{value:"pos-pda",className:"mt-6",children:e.jsx(Te,{permissions:h,onParentPermissionChange:S,onChildPermissionChange:d})}),e.jsx(W,{value:"manager",className:"mt-6",children:e.jsx(le,{permissions:h,onParentPermissionChange:S,onChildPermissionChange:d})})]})]})]})]})}export{ue as E};
