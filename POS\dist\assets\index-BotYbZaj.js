import{u as Se,r as g,a as Ze,b as Vt,j as t,B as vt,c as tt,d as ne,R as N,g as yt,f as me}from"./index-D0Grd55b.js";import{H as zt}from"./header-yApU5MZq.js";import{M as Yt}from"./main-Czv3HpP4.js";import{P as Wt}from"./profile-dropdown-dGP8eyih.js";import{S as Ht,T as Ut}from"./search-BEMocVbv.js";import{D as Gt}from"./date-range-picker-CruKYeHR.js";import"./form-Bk1C9kLO.js";import{a as ce}from"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import{u as le}from"./useQuery-Ck3BpOfq.js";import{Q as de}from"./query-keys-3lmd-xp6.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import{u as Zt}from"./use-pos-cities-data-BBSMUcaZ.js";import{C as Qt}from"./combobox-NkstXop_.js";import{B as Ye}from"./badge-DoEZpO1Y.js";import{C as nt}from"./checkbox-C_eqgJmW.js";import{C as Xt,a as Jt,b as en}from"./collapsible-MezBM5sx.js";import{P as tn,a as nn,b as rn}from"./popover-CMTiAV3j.js";import{C as Oe}from"./select-DBO-8fSu.js";import{C as A,a as k,b as T,c as b,d as P,e as W}from"./card-Z5f_tC2-.js";import{D as jt,a as bt,b as _t,c as sn}from"./dropdown-menu-BvqrmFsX.js";import{f as F,r as Nt,a as an,b as St,c as on,i as ie,T as Qe,p as J,L as U,g as wt,d as Xe,e as At,D as cn,C as ln,h as pe,j as ue,S as dn,A as un,k as mn,l as rt,m as hn,n as he,o as xn,u as fn,G as pn,q as Te,s as gn,t as kt,v as vn,w as ke,x as st,y as yn,z as jn,R as we,X as Le,Y as Ee,B as Ae,E as Ne}from"./generateCategoricalChart-CMXX3hIw.js";import{B as Re}from"./BarChart-C_nb_DlB.js";import{u as bn}from"./use-payment-method-revenue-B62UmsVd.js";import"./separator-CiUiq7rT.js";import"./avatar-C7dnn6zI.js";import"./search-context-CjM0jrYw.js";import"./command-Bcq7GTcy.js";import"./calendar-5lpy20z0.js";import"./createLucideIcon-DNzDbUBG.js";import"./index-CW7Xpojs.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-C8IVKkOo.js";import"./search-C4dfJjaw.js";import"./createReactComponent-eJgt86Cn.js";import"./scroll-area-r2ikcXUQ.js";import"./index-CI2TkimM.js";import"./IconChevronRight-DnVUSvDn.js";import"./IconSearch-ClUZlEwa.js";import"./chevron-right-D2H_xC9V.js";import"./react-icons.esm-BOnXQgjl.js";import"./utils-km2FGkQ4.js";import"./chevrons-up-down-BCCoC0zx.js";import"./check-TFQPNqMS.js";import"./index-C-UyCxtf.js";import"./index-DxQNaO1C.js";import"./index-Chjiymov.js";const _n=async e=>(await ce.get("/v1/reports/sale-summary/items",{params:e})).data,Nn=async e=>(await ce.get("/ktv/v2/reports/months",{params:e})).data,Sn=async e=>(await ce.get("/v1/reports/sale-summary/promotions",{params:e})).data,wn=async e=>(await ce.get("/v1/reports/sale-summary/sources",{params:{brand_uid:e.brand_uid,company_uid:e.company_uid,list_store_uid:e.list_store_uid,start_date:e.start_date,end_date:e.end_date,store_open_at:e.store_open_at??0,limit:e.limit??5}})).data,An=async e=>(await ce.get("/v1/reports/sale-summary/stores",{params:{brand_uid:e.brand_uid,company_uid:e.company_uid,list_store_uid:e.list_store_uid,start_date:e.start_date,end_date:e.end_date,store_open_at:e.store_open_at??0,limit:e.limit??5}})).data,kn=async e=>(await ce.get("/v1/reports/sale-summary/weekdays",{params:{brand_uid:e.brand_uid,company_uid:e.company_uid,list_store_uid:e.list_store_uid,start_date:e.start_date,end_date:e.end_date,store_open_at:e.store_open_at??0,limit:e.limit??5}})).data,Tn=async e=>(await ce.get("/v1/reports/sale-summary/overview",{params:{brand_uid:e.brand_uid,company_uid:e.company_uid,list_store_uid:e.list_store_uid,start_date:e.start_date,end_date:e.end_date,store_open_at:e.store_open_at??0}})).data,Pn=(e={})=>{const{params:n={},enabled:a=!0}=e,{company:r,brands:s}=Se(l=>l.auth),i=s==null?void 0:s[0],o={company_uid:n.company_uid||(r==null?void 0:r.id)||"",brand_uid:n.brand_uid||(i==null?void 0:i.id)||"",list_store_uid:n.list_store_uid||"",start_date:n.start_date??Date.now()-7*24*60*60*1e3,end_date:n.end_date??Date.now(),store_open_at:n.store_open_at??0,limit:n.limit??5},c=!!(o.company_uid&&o.brand_uid&&o.list_store_uid);return le({queryKey:[de.REPORTS_SOURCES,o],queryFn:async()=>await wn(o),enabled:a&&c,staleTime:5*60*1e3,refetchInterval:10*60*1e3})},On=(e={})=>{const{params:n={},enabled:a=!0}=e,{company:r,brands:s}=Se(l=>l.auth),i=s==null?void 0:s[0],o={company_uid:n.company_uid||(r==null?void 0:r.id)||"",brand_uid:n.brand_uid||(i==null?void 0:i.id)||"",list_store_uid:n.list_store_uid||"",start_date:n.start_date??Date.now()-7*24*60*60*1e3,end_date:n.end_date??Date.now(),store_open_at:n.store_open_at??0,limit:n.limit??5},c=!!(o.company_uid&&o.brand_uid&&o.list_store_uid);return le({queryKey:[de.REPORTS_STORES,o],queryFn:async()=>await An(o),enabled:a&&c,staleTime:5*60*1e3,refetchInterval:10*60*1e3})},Dn=(e={})=>{const{params:n={},enabled:a=!0}=e,{company:r,brands:s}=Se(l=>l.auth),i=s==null?void 0:s[0],o={company_uid:n.company_uid||(r==null?void 0:r.id)||"",brand_uid:n.brand_uid||(i==null?void 0:i.id)||"",list_store_uid:n.list_store_uid||"",start_date:n.start_date??Date.now()-7*24*60*60*1e3,end_date:n.end_date??Date.now(),store_open_at:n.store_open_at??0,limit:n.limit??5},c=!!(o.company_uid&&o.brand_uid&&o.list_store_uid);return le({queryKey:[de.REPORTS_WEEKDAYS,o],queryFn:async()=>await kn(o),enabled:a&&c,staleTime:5*60*1e3,refetchInterval:10*60*1e3})},Cn=(e={})=>{const{params:n={},enabled:a=!0}=e,{company:r,brands:s}=Se(l=>l.auth),i=s==null?void 0:s[0],o={company_uid:n.company_uid||(r==null?void 0:r.id)||"",brand_uid:n.brand_uid||(i==null?void 0:i.id)||"",list_store_uid:n.list_store_uid||"",start_date:n.start_date??Date.now()-7*24*60*60*1e3,end_date:n.end_date??Date.now(),store_open_at:n.store_open_at??0},c=!!(o.company_uid&&o.brand_uid&&o.list_store_uid);return le({queryKey:[de.REPORTS_SALE_SUMMARY_OVERVIEW,o],queryFn:async()=>await Tn(o),enabled:a&&c,staleTime:5*60*1e3,refetchInterval:10*60*1e3})},$n=({params:e,enabled:n=!0})=>{var i,o,c,l;const a=[de.PROMOTIONS,"sale-summary-top",e.company_uid,e.brand_uid,e.start_date,e.end_date,e.list_store_uid,e.limit],s=le({queryKey:a,queryFn:async()=>e.list_store_uid?Sn(e):{data:[],message:"No stores selected",track_id:""},enabled:n&&!!e.list_store_uid,staleTime:5*60*1e3,gcTime:10*60*1e3,retry:2,refetchOnWindowFocus:!1});return{data:((i=s.data)==null?void 0:i.data)||[],isLoading:s.isLoading,isError:s.isError,error:s.error,refetch:s.refetch,hasData:(((o=s.data)==null?void 0:o.data)||[]).length>0,message:((c=s.data)==null?void 0:c.message)||"",trackId:((l=s.data)==null?void 0:l.track_id)||""}},Ln=({params:e,enabled:n=!0})=>{var i,o,c,l;const a=[de.ITEMS,"sale-summary-top",e.company_uid,e.brand_uid,e.start_date,e.end_date,e.list_store_uid,e.limit,e.order_by],s=le({queryKey:a,queryFn:async()=>e.list_store_uid?_n(e):{data:{list_data_item_return:[]},message:"No stores selected",track_id:""},enabled:n&&!!e.list_store_uid,staleTime:5*60*1e3,gcTime:10*60*1e3,retry:2,refetchOnWindowFocus:!1});return{data:((i=s.data)==null?void 0:i.data.list_data_item_return)||[],isLoading:s.isLoading,isError:s.isError,error:s.error,refetch:s.refetch,hasData:(((o=s.data)==null?void 0:o.data.list_data_item_return)||[]).length>0,message:((c=s.data)==null?void 0:c.message)||"",trackId:((l=s.data)==null?void 0:l.track_id)||""}},En=({params:e,enabled:n=!0})=>{var i,o,c;const a=[de.KTV_REPORTS,"month-report",e.store_uid,e.date_time],s=le({queryKey:a,queryFn:async()=>e.store_uid?Nn(e):{data:{ts_time:0,store_uid:"",revenue_net:0,material_cost:0,material_cost_detail:[],purchase_detail:[],count_item_not_price:0,count_item_price:0,count_item:0,tool_cost:0,tool_cost_detail:[],count_tool:0,cost_amount_inex:0,cost_inex_detail:[],count_cost_out:0,count_cost_out_all:0,income_amount:0,income_detail:[],profit:0,date_time_filter:null,require_close_report:0,ivt_price:0,status_report:"open",percentage_material:0,percentage_tool:0,percentage_inex:0,percentage_profit:0},track_id:""},enabled:n&&!!e.store_uid,staleTime:5*60*1e3,gcTime:10*60*1e3,retry:2,refetchOnWindowFocus:!1});return{data:((i=s.data)==null?void 0:i.data)||null,isLoading:s.isLoading,isError:s.isError,error:s.error,refetch:s.refetch,hasData:!!((o=s.data)!=null&&o.data),trackId:((c=s.data)==null?void 0:c.track_id)||""}},Tt=g.createContext(void 0);function Rn({children:e}){const[n,a]=g.useState(()=>{const V=new Date;return{from:V,to:V}}),[r,s]=g.useState(["all-stores"]),[i,o]=g.useState("revenue_net"),[c,l]=g.useState(17539812e5),{currentBrandStores:m,selectedBrand:d}=Ze(),{company:h}=Vt(),p=g.useMemo(()=>{if(!m||m.length===0)return"";const V=r.filter(re=>re!=="all-stores");return V.length===0?m.map(re=>re.id).join(","):V.join(",")},[r,m]),v=g.useMemo(()=>{const V=new Date(n.from);return V.setHours(0,0,0,0),V.getTime()},[n.from]),y=g.useMemo(()=>{const V=new Date(n.to);return V.setHours(23,59,59,999),V.getTime()},[n.to]),u=g.useMemo(()=>{const V=new Date,re=new Date(V.getTime()-24*60*60*1e3);re.setHours(23,59,59,999);const et=new Date(re.getTime()-6*24*60*60*1e3);return et.setHours(0,0,0,0),{from:et,to:re}},[]),{data:x,isLoading:_,error:w}=Cn({params:{list_store_uid:p,start_date:v,end_date:y,store_open_at:0},enabled:!!p}),{data:C,isLoading:z,error:E}=Pn({params:{brand_uid:(d==null?void 0:d.id)||"",company_uid:(h==null?void 0:h.id)||"",list_store_uid:p,start_date:u.from.getTime(),end_date:u.to.getTime(),store_open_at:0,limit:5},enabled:!!p&&!!(d!=null&&d.id)&&!!(h!=null&&h.id)}),q=(C==null?void 0:C.data)||[],Q=(E==null?void 0:E.message)||null,{data:R,isLoading:G,error:M}=Dn({params:{brand_uid:(d==null?void 0:d.id)||"",company_uid:(h==null?void 0:h.id)||"",list_store_uid:p,start_date:u.from.getTime(),end_date:u.to.getTime(),store_open_at:0,limit:7},enabled:!!p&&!!(d!=null&&d.id)&&!!(h!=null&&h.id)}),te=(R==null?void 0:R.data)||[],X=(M==null?void 0:M.message)||null,{data:f,isLoading:O,error:$}=On({params:{brand_uid:(d==null?void 0:d.id)||"",company_uid:(h==null?void 0:h.id)||"",list_store_uid:p,start_date:u.from.getTime(),end_date:u.to.getTime(),store_open_at:0,limit:5},enabled:!!p&&!!(d!=null&&d.id)&&!!(h!=null&&h.id)}),S=(f==null?void 0:f.data)||[],j=($==null?void 0:$.message)||null,{data:I,isLoading:K,error:B}=$n({params:{brand_uid:(d==null?void 0:d.id)||"",company_uid:(h==null?void 0:h.id)||"",list_store_uid:p,start_date:u.from.getTime(),end_date:u.to.getTime(),store_open_at:0,limit:5},enabled:!!p&&!!(d!=null&&d.id)&&!!(h!=null&&h.id)}),L=(B==null?void 0:B.message)||null,{data:Z,isLoading:qe,error:je}=Ln({params:{brand_uid:(d==null?void 0:d.id)||"",company_uid:(h==null?void 0:h.id)||"",list_store_uid:p,start_date:u.from.getTime(),end_date:u.to.getTime(),store_open_at:0,limit:10,order_by:i},enabled:!!p&&!!(d!=null&&d.id)&&!!(h!=null&&h.id)}),Et=(je==null?void 0:je.message)||null,Rt=(x==null?void 0:x.data)||null,It=(w==null?void 0:w.message)||null,Je=g.useMemo(()=>p?p.split(",")[0]:"",[p]),{data:Mt,isLoading:Ft,error:Be}=En({params:{store_uid:Je,date_time:c},enabled:!!Je}),Kt=Mt||null,qt=(Be==null?void 0:Be.message)||null,Bt={dateRange:n,setDateRange:a,selectedStores:r,setSelectedStores:s,itemsSortBy:i,setItemsSortBy:o,ktvTimestamp:c,setKtvTimestamp:l,defaultDateRange:u,overviewData:Rt,isOverviewLoading:_,overviewError:It,sourcesData:q,isSourcesLoading:z,sourcesError:Q,storesData:S,isStoresLoading:O,storesError:j,weekdaysData:te,isWeekdaysLoading:G,weekdaysError:X,promotionsData:I,isPromotionsLoading:K,promotionsError:L,itemsData:Z,isItemsLoading:qe,itemsError:Et,ktvReportData:Kt,isKtvReportLoading:Ft,ktvReportError:qt};return t.jsx(Tt.Provider,{value:Bt,children:e})}function H(){const e=g.useContext(Tt);if(e===void 0)throw new Error("useDashboardContext must be used within a DashboardProvider");return e}const In=({selectedCities:e,selectedStores:n,onCitiesChange:a,onStoresChange:r,className:s,excludeCities:i=[],excludeStores:o=[]})=>{const[c,l]=g.useState(!1),[m,d]=g.useState(new Set),[h,p]=g.useState("all"),[v,y]=g.useState(""),{cities:u}=Zt(),{currentBrandStores:x}=Ze(),_=g.useMemo(()=>u.filter(f=>!i.includes(f.id||f.city_id)),[u,i]),w=g.useMemo(()=>x.filter(f=>!o.includes(f.id||f.store_id)).filter(f=>h==="all"?!0:h==="chain"?f.is_franchise===0:f.is_franchise===1),[x,o,h]),C=f=>w.filter(O=>O.city_uid===f),z=f=>{const O=new Set(m);O.has(f)?O.delete(f):O.add(f),d(O)},E=(f,O)=>{const S=C(f).map(j=>j.id||j.store_id);if(O){a([...e,f]);const j=n.filter(K=>K!=="all-stores"),I=Array.from(new Set([...j,...S]));r(I)}else{a(e.filter(I=>I!==f));const j=n.filter(I=>!S.includes(I));r(j.length>0?j:["all-stores"])}},q=(f,O)=>{const $=x.find(j=>(j.id||j.store_id)===f),S=$==null?void 0:$.city_uid;if(O){const j=[...n,f];if(r(j),S){const K=C(S).map(L=>L.id||L.store_id);if(K.every(L=>j.includes(L)||e.includes(S))){const L=j.filter(Z=>!K.includes(Z));r(L),a([...e,S])}}}else if(S&&e.includes(S)){const K=C(S).map(B=>B.id||B.store_id).filter(B=>B!==f);a(e.filter(B=>B!==S)),r([...n,...K])}else r(n.filter(j=>j!==f))},Q=f=>e.includes(f),R=f=>{const $=C(f).map(j=>j.id||j.store_id),S=n.filter(j=>$.includes(j));return S.length>0&&S.length<$.length},G=f=>n.includes(f),M=g.useMemo(()=>{const f=e.reduce((S,j)=>{const I=C(j);return S+I.length},0),$=n.filter(S=>S!=="all-stores").filter(S=>{const j=x.find(K=>(K.id||K.store_id)===S);if(!j)return!1;const I=j.city_uid;return!e.includes(I)}).length;return f+$},[e,n,x]),te=M>0?`Đã chọn ${M} cửa hàng`:"Tất cả cửa hàng",X=g.useMemo(()=>{const f=v.trim().toLowerCase();return _.some(O=>{const $=O.id||O.city_id,S=C($);return f?S.some(j=>String(j.store_name||"").toLowerCase().includes(f)):S.length>0})},[_,w,v]);return t.jsxs(tn,{open:c,onOpenChange:l,children:[t.jsx(nn,{asChild:!0,children:t.jsxs(vt,{variant:"outline",role:"combobox","aria-expanded":c,className:tt("w-full justify-between",s),children:[t.jsx("span",{className:"truncate",children:te}),t.jsx(Oe,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),t.jsx(rn,{className:"w-80 p-0",align:"start",children:t.jsx("div",{className:"max-h-96 overflow-y-auto",children:t.jsxs("div",{className:"p-2",children:[t.jsx("div",{className:"mb-2 flex flex-col items-start justify-center gap-2",children:t.jsx(Qt,{options:[{value:"all",label:"Tất cả cửa hàng"},{value:"chain",label:"Chuỗi"},{value:"franchise",label:"Nhượng quyền"}],value:h,onValueChange:f=>p(f||"all"),placeholder:"Loại cửa hàng",searchPlaceholder:"Tìm kiếm...",className:"w-full"})}),!X&&t.jsx("div",{className:"text-muted-foreground flex h-40 items-center justify-center text-lg font-semibold",children:"Không có dữ liệu"}),X&&_.filter(f=>{const O=f.id||f.city_id,$=C(O),S=v.trim().toLowerCase();return S?$.some(j=>String(j.store_name||"").toLowerCase().includes(S)):$.length>0}).map(f=>{const O=f.id||f.city_id,$=v.trim().toLowerCase(),S=C(O),j=$?S.filter(L=>String(L.store_name||"").toLowerCase().includes($)):S,I=$?!0:m.has(O),K=Q(O),B=R(O);return t.jsx("div",{className:"mb-2",children:t.jsxs(Xt,{open:I,onOpenChange:()=>z(O),children:[t.jsxs("div",{className:"flex items-center space-x-2 rounded p-2 hover:bg-gray-50",children:[t.jsx(nt,{checked:K,ref:L=>{if(L&&B&&!K){const Z=L.querySelector('input[type="checkbox"]');Z&&(Z.indeterminate=!0)}},onCheckedChange:L=>E(O,L)}),t.jsxs(Jt,{className:"flex flex-1 items-center space-x-2 text-left",children:[t.jsx("span",{className:"font-medium",children:f.city_name}),t.jsxs("span",{className:"text-muted-foreground text-sm",children:["(",j.length," cửa hàng)"]}),t.jsx(Oe,{className:tt("ml-auto h-4 w-4 transition-transform",I&&"rotate-180 transform")})]})]}),t.jsx(en,{children:I&&t.jsx("div",{className:"ml-6 space-y-1",children:j.map(L=>{const Z=L.id||L.store_id,qe=G(Z)||K;return t.jsxs("div",{className:"flex items-center space-x-2 rounded p-2 hover:bg-gray-50",children:[t.jsx(nt,{checked:qe,onCheckedChange:je=>q(Z,je)}),t.jsx("span",{className:"text-sm",children:L.store_name}),typeof L.is_franchise<"u"&&(L.is_franchise===1?t.jsx(Ye,{className:"ml-2",children:"Nhượng quyền"}):t.jsx(Ye,{className:"ml-2",children:"Chuỗi"}))]},Z)})})})]})},O)})]})})})]})},Mn=()=>{const{dateRange:e,setDateRange:n,selectedStores:a,setSelectedStores:r}=H(),[s,i]=g.useState([]);return t.jsx("div",{className:"mb-6 flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between",children:t.jsxs("div",{className:"flex flex-col items-center gap-4 md:flex-row md:gap-6",children:[t.jsx(Gt,{initialDateFrom:e.from,initialDateTo:e.to,onUpdate:({range:o})=>{o!=null&&o.from&&(o!=null&&o.to)&&n({from:o.from,to:o.to})},align:"start",locale:"vi-VN"}),t.jsx(In,{selectedCities:s,selectedStores:a,onCitiesChange:i,onStoresChange:r,className:"w-[200px]"})]})})};var Fn=["points","className","baseLinePoints","connectNulls"];function xe(){return xe=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var a=arguments[n];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},xe.apply(this,arguments)}function Kn(e,n){if(e==null)return{};var a=qn(e,n),r,s;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)r=i[s],!(n.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function qn(e,n){if(e==null)return{};var a={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;a[r]=e[r]}return a}function at(e){return Yn(e)||zn(e)||Vn(e)||Bn()}function Bn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Vn(e,n){if(e){if(typeof e=="string")return We(e,n);var a=Object.prototype.toString.call(e).slice(8,-1);if(a==="Object"&&e.constructor&&(a=e.constructor.name),a==="Map"||a==="Set")return Array.from(e);if(a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return We(e,n)}}function zn(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Yn(e){if(Array.isArray(e))return We(e)}function We(e,n){(n==null||n>e.length)&&(n=e.length);for(var a=0,r=new Array(n);a<n;a++)r[a]=e[a];return r}var it=function(n){return n&&n.x===+n.x&&n.y===+n.y},Wn=function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],a=[[]];return n.forEach(function(r){it(r)?a[a.length-1].push(r):a[a.length-1].length>0&&a.push([])}),it(n[0])&&a[a.length-1].push(n[0]),a[a.length-1].length<=0&&(a=a.slice(0,-1)),a},be=function(n,a){var r=Wn(n);a&&(r=[r.reduce(function(i,o){return[].concat(at(i),at(o))},[])]);var s=r.map(function(i){return i.reduce(function(o,c,l){return"".concat(o).concat(l===0?"M":"L").concat(c.x,",").concat(c.y)},"")}).join("");return r.length===1?"".concat(s,"Z"):s},Hn=function(n,a,r){var s=be(n,r);return"".concat(s.slice(-1)==="Z"?s.slice(0,-1):s,"L").concat(be(a.reverse(),r).slice(1))},Un=function(n){var a=n.points,r=n.className,s=n.baseLinePoints,i=n.connectNulls,o=Kn(n,Fn);if(!a||!a.length)return null;var c=ne("recharts-polygon",r);if(s&&s.length){var l=o.stroke&&o.stroke!=="none",m=Hn(a,s,i);return N.createElement("g",{className:c},N.createElement("path",xe({},F(o,!0),{fill:m.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:m})),l?N.createElement("path",xe({},F(o,!0),{fill:"none",d:be(a,i)})):null,l?N.createElement("path",xe({},F(o,!0),{fill:"none",d:be(s,i)})):null)}var d=be(a,i);return N.createElement("path",xe({},F(o,!0),{fill:d.slice(-1)==="Z"?o.fill:"none",className:c,d}))},Ve,ot;function Gn(){if(ot)return Ve;ot=1;var e=Nt(),n=an(),a=St();function r(s,i){return s&&s.length?e(s,a(i,2),n):void 0}return Ve=r,Ve}var Zn=Gn();const Qn=yt(Zn);var ze,ct;function Xn(){if(ct)return ze;ct=1;var e=Nt(),n=St(),a=on();function r(s,i){return s&&s.length?e(s,n(i,2),a):void 0}return ze=r,ze}var Jn=Xn();const er=yt(Jn);var tr=["cx","cy","angle","ticks","axisLine"],nr=["ticks","tick","angle","tickFormatter","stroke"];function ge(e){"@babel/helpers - typeof";return ge=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},ge(e)}function _e(){return _e=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var a=arguments[n];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},_e.apply(this,arguments)}function lt(e,n){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),a.push.apply(a,r)}return a}function se(e){for(var n=1;n<arguments.length;n++){var a=arguments[n]!=null?arguments[n]:{};n%2?lt(Object(a),!0).forEach(function(r){Ie(e,r,a[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):lt(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}function dt(e,n){if(e==null)return{};var a=rr(e,n),r,s;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)r=i[s],!(n.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function rr(e,n){if(e==null)return{};var a={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;a[r]=e[r]}return a}function sr(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function ut(e,n){for(var a=0;a<n.length;a++){var r=n[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ot(r.key),r)}}function ar(e,n,a){return n&&ut(e.prototype,n),a&&ut(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}function ir(e,n,a){return n=De(n),or(e,Pt()?Reflect.construct(n,a||[],De(e).constructor):n.apply(e,a))}function or(e,n){if(n&&(ge(n)==="object"||typeof n=="function"))return n;if(n!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cr(e)}function cr(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Pt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Pt=function(){return!!e})()}function De(e){return De=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)},De(e)}function lr(e,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),n&&He(e,n)}function He(e,n){return He=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,s){return r.__proto__=s,r},He(e,n)}function Ie(e,n,a){return n=Ot(n),n in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a,e}function Ot(e){var n=dr(e,"string");return ge(n)=="symbol"?n:n+""}function dr(e,n){if(ge(e)!="object"||!e)return e;var a=e[Symbol.toPrimitive];if(a!==void 0){var r=a.call(e,n);if(ge(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Me=function(e){function n(){return sr(this,n),ir(this,n,arguments)}return lr(n,e),ar(n,[{key:"getTickValueCoord",value:function(r){var s=r.coordinate,i=this.props,o=i.angle,c=i.cx,l=i.cy;return J(c,l,s,o)}},{key:"getTickTextAnchor",value:function(){var r=this.props.orientation,s;switch(r){case"left":s="end";break;case"right":s="start";break;default:s="middle";break}return s}},{key:"getViewBox",value:function(){var r=this.props,s=r.cx,i=r.cy,o=r.angle,c=r.ticks,l=Qn(c,function(d){return d.coordinate||0}),m=er(c,function(d){return d.coordinate||0});return{cx:s,cy:i,startAngle:o,endAngle:o,innerRadius:m.coordinate||0,outerRadius:l.coordinate||0}}},{key:"renderAxisLine",value:function(){var r=this.props,s=r.cx,i=r.cy,o=r.angle,c=r.ticks,l=r.axisLine,m=dt(r,tr),d=c.reduce(function(y,u){return[Math.min(y[0],u.coordinate),Math.max(y[1],u.coordinate)]},[1/0,-1/0]),h=J(s,i,d[0],o),p=J(s,i,d[1],o),v=se(se(se({},F(m,!1)),{},{fill:"none"},F(l,!1)),{},{x1:h.x,y1:h.y,x2:p.x,y2:p.y});return N.createElement("line",_e({className:"recharts-polar-radius-axis-line"},v))}},{key:"renderTicks",value:function(){var r=this,s=this.props,i=s.ticks,o=s.tick,c=s.angle,l=s.tickFormatter,m=s.stroke,d=dt(s,nr),h=this.getTickTextAnchor(),p=F(d,!1),v=F(o,!1),y=i.map(function(u,x){var _=r.getTickValueCoord(u),w=se(se(se(se({textAnchor:h,transform:"rotate(".concat(90-c,", ").concat(_.x,", ").concat(_.y,")")},p),{},{stroke:"none",fill:m},v),{},{index:x},_),{},{payload:u});return N.createElement(U,_e({className:ne("recharts-polar-radius-axis-tick",wt(o)),key:"tick-".concat(u.coordinate)},Xe(r.props,u,x)),n.renderTickItem(o,w,l?l(u.value,x):u.value))});return N.createElement(U,{className:"recharts-polar-radius-axis-ticks"},y)}},{key:"render",value:function(){var r=this.props,s=r.ticks,i=r.axisLine,o=r.tick;return!s||!s.length?null:N.createElement(U,{className:ne("recharts-polar-radius-axis",this.props.className)},i&&this.renderAxisLine(),o&&this.renderTicks(),At.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(r,s,i){var o;return N.isValidElement(r)?o=N.cloneElement(r,s):ie(r)?o=r(s):o=N.createElement(Qe,_e({},s,{className:"recharts-polar-radius-axis-tick-value"}),i),o}}])}(g.PureComponent);Ie(Me,"displayName","PolarRadiusAxis");Ie(Me,"axisType","radiusAxis");Ie(Me,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function ve(e){"@babel/helpers - typeof";return ve=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},ve(e)}function oe(){return oe=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var a=arguments[n];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},oe.apply(this,arguments)}function mt(e,n){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),a.push.apply(a,r)}return a}function ae(e){for(var n=1;n<arguments.length;n++){var a=arguments[n]!=null?arguments[n]:{};n%2?mt(Object(a),!0).forEach(function(r){Fe(e,r,a[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):mt(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}function ur(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function ht(e,n){for(var a=0;a<n.length;a++){var r=n[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ct(r.key),r)}}function mr(e,n,a){return n&&ht(e.prototype,n),a&&ht(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}function hr(e,n,a){return n=Ce(n),xr(e,Dt()?Reflect.construct(n,a||[],Ce(e).constructor):n.apply(e,a))}function xr(e,n){if(n&&(ve(n)==="object"||typeof n=="function"))return n;if(n!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fr(e)}function fr(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Dt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Dt=function(){return!!e})()}function Ce(e){return Ce=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)},Ce(e)}function pr(e,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),n&&Ue(e,n)}function Ue(e,n){return Ue=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,s){return r.__proto__=s,r},Ue(e,n)}function Fe(e,n,a){return n=Ct(n),n in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a,e}function Ct(e){var n=gr(e,"string");return ve(n)=="symbol"?n:n+""}function gr(e,n){if(ve(e)!="object"||!e)return e;var a=e[Symbol.toPrimitive];if(a!==void 0){var r=a.call(e,n);if(ve(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var vr=Math.PI/180,yr=1e-5,Ke=function(e){function n(){return ur(this,n),hr(this,n,arguments)}return pr(n,e),mr(n,[{key:"getTickLineCoord",value:function(r){var s=this.props,i=s.cx,o=s.cy,c=s.radius,l=s.orientation,m=s.tickSize,d=m||8,h=J(i,o,c,r.coordinate),p=J(i,o,c+(l==="inner"?-1:1)*d,r.coordinate);return{x1:h.x,y1:h.y,x2:p.x,y2:p.y}}},{key:"getTickTextAnchor",value:function(r){var s=this.props.orientation,i=Math.cos(-r.coordinate*vr),o;return i>yr?o=s==="outer"?"start":"end":i<-1e-5?o=s==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var r=this.props,s=r.cx,i=r.cy,o=r.radius,c=r.axisLine,l=r.axisLineType,m=ae(ae({},F(this.props,!1)),{},{fill:"none"},F(c,!1));if(l==="circle")return N.createElement(cn,oe({className:"recharts-polar-angle-axis-line"},m,{cx:s,cy:i,r:o}));var d=this.props.ticks,h=d.map(function(p){return J(s,i,o,p.coordinate)});return N.createElement(Un,oe({className:"recharts-polar-angle-axis-line"},m,{points:h}))}},{key:"renderTicks",value:function(){var r=this,s=this.props,i=s.ticks,o=s.tick,c=s.tickLine,l=s.tickFormatter,m=s.stroke,d=F(this.props,!1),h=F(o,!1),p=ae(ae({},d),{},{fill:"none"},F(c,!1)),v=i.map(function(y,u){var x=r.getTickLineCoord(y),_=r.getTickTextAnchor(y),w=ae(ae(ae({textAnchor:_},d),{},{stroke:"none",fill:m},h),{},{index:u,payload:y,x:x.x2,y:x.y2});return N.createElement(U,oe({className:ne("recharts-polar-angle-axis-tick",wt(o)),key:"tick-".concat(y.coordinate)},Xe(r.props,y,u)),c&&N.createElement("line",oe({className:"recharts-polar-angle-axis-tick-line"},p,x)),o&&n.renderTickItem(o,w,l?l(y.value,u):y.value))});return N.createElement(U,{className:"recharts-polar-angle-axis-ticks"},v)}},{key:"render",value:function(){var r=this.props,s=r.ticks,i=r.radius,o=r.axisLine;return i<=0||!s||!s.length?null:N.createElement(U,{className:ne("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(r,s,i){var o;return N.isValidElement(r)?o=N.cloneElement(r,s):ie(r)?o=r(s):o=N.createElement(Qe,oe({},s,{className:"recharts-polar-angle-axis-tick-value"}),i),o}}])}(g.PureComponent);Fe(Ke,"displayName","PolarAngleAxis");Fe(Ke,"axisType","angleAxis");Fe(Ke,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var Pe;function ye(e){"@babel/helpers - typeof";return ye=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},ye(e)}function fe(){return fe=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var a=arguments[n];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},fe.apply(this,arguments)}function xt(e,n){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),a.push.apply(a,r)}return a}function D(e){for(var n=1;n<arguments.length;n++){var a=arguments[n]!=null?arguments[n]:{};n%2?xt(Object(a),!0).forEach(function(r){Y(e,r,a[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):xt(Object(a)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(a,r))})}return e}function jr(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function ft(e,n){for(var a=0;a<n.length;a++){var r=n[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Lt(r.key),r)}}function br(e,n,a){return n&&ft(e.prototype,n),a&&ft(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}function _r(e,n,a){return n=$e(n),Nr(e,$t()?Reflect.construct(n,a||[],$e(e).constructor):n.apply(e,a))}function Nr(e,n){if(n&&(ye(n)==="object"||typeof n=="function"))return n;if(n!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Sr(e)}function Sr(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $t(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return($t=function(){return!!e})()}function $e(e){return $e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)},$e(e)}function wr(e,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),n&&Ge(e,n)}function Ge(e,n){return Ge=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,s){return r.__proto__=s,r},Ge(e,n)}function Y(e,n,a){return n=Lt(n),n in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a,e}function Lt(e){var n=Ar(e,"string");return ye(n)=="symbol"?n:n+""}function Ar(e,n){if(ye(e)!="object"||!e)return e;var a=e[Symbol.toPrimitive];if(a!==void 0){var r=a.call(e,n);if(ye(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ee=function(e){function n(a){var r;return jr(this,n),r=_r(this,n,[a]),Y(r,"pieRef",null),Y(r,"sectorRefs",[]),Y(r,"id",fn("recharts-pie-")),Y(r,"handleAnimationEnd",function(){var s=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),ie(s)&&s()}),Y(r,"handleAnimationStart",function(){var s=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),ie(s)&&s()}),r.state={isAnimationFinished:!a.isAnimationActive,prevIsAnimationActive:a.isAnimationActive,prevAnimationId:a.animationId,sectorToFocus:0},r}return wr(n,e),br(n,[{key:"isActiveIndex",value:function(r){var s=this.props.activeIndex;return Array.isArray(s)?s.indexOf(r)!==-1:r===s}},{key:"hasActiveIndex",value:function(){var r=this.props.activeIndex;return Array.isArray(r)?r.length!==0:r||r===0}},{key:"renderLabels",value:function(r){var s=this.props.isAnimationActive;if(s&&!this.state.isAnimationFinished)return null;var i=this.props,o=i.label,c=i.labelLine,l=i.dataKey,m=i.valueKey,d=F(this.props,!1),h=F(o,!1),p=F(c,!1),v=o&&o.offsetRadius||20,y=r.map(function(u,x){var _=(u.startAngle+u.endAngle)/2,w=J(u.cx,u.cy,u.outerRadius+v,_),C=D(D(D(D({},d),u),{},{stroke:"none"},h),{},{index:x,textAnchor:n.getTextAnchor(w.x,u.cx)},w),z=D(D(D(D({},d),u),{},{fill:"none",stroke:u.fill},p),{},{index:x,points:[J(u.cx,u.cy,u.outerRadius,_),w]}),E=l;return pe(l)&&pe(m)?E="value":pe(l)&&(E=m),N.createElement(U,{key:"label-".concat(u.startAngle,"-").concat(u.endAngle,"-").concat(u.midAngle,"-").concat(x)},c&&n.renderLabelLineItem(c,z,"line"),n.renderLabelItem(o,C,ue(u,E)))});return N.createElement(U,{className:"recharts-pie-labels"},y)}},{key:"renderSectorsStatically",value:function(r){var s=this,i=this.props,o=i.activeShape,c=i.blendStroke,l=i.inactiveShape;return r.map(function(m,d){if((m==null?void 0:m.startAngle)===0&&(m==null?void 0:m.endAngle)===0&&r.length!==1)return null;var h=s.isActiveIndex(d),p=l&&s.hasActiveIndex()?l:null,v=h?o:p,y=D(D({},m),{},{stroke:c?m.fill:m.stroke,tabIndex:-1});return N.createElement(U,fe({ref:function(x){x&&!s.sectorRefs.includes(x)&&s.sectorRefs.push(x)},tabIndex:-1,className:"recharts-pie-sector"},Xe(s.props,m,d),{key:"sector-".concat(m==null?void 0:m.startAngle,"-").concat(m==null?void 0:m.endAngle,"-").concat(m.midAngle,"-").concat(d)}),N.createElement(dn,fe({option:v,isActive:h,shapeType:"sector"},y)))})}},{key:"renderSectorsWithAnimation",value:function(){var r=this,s=this.props,i=s.sectors,o=s.isAnimationActive,c=s.animationBegin,l=s.animationDuration,m=s.animationEasing,d=s.animationId,h=this.state,p=h.prevSectors,v=h.prevIsAnimationActive;return N.createElement(un,{begin:c,duration:l,isActive:o,easing:m,from:{t:0},to:{t:1},key:"pie-".concat(d,"-").concat(v),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(y){var u=y.t,x=[],_=i&&i[0],w=_.startAngle;return i.forEach(function(C,z){var E=p&&p[z],q=z>0?mn(C,"paddingAngle",0):0;if(E){var Q=rt(E.endAngle-E.startAngle,C.endAngle-C.startAngle),R=D(D({},C),{},{startAngle:w+q,endAngle:w+Q(u)+q});x.push(R),w=R.endAngle}else{var G=C.endAngle,M=C.startAngle,te=rt(0,G-M),X=te(u),f=D(D({},C),{},{startAngle:w+q,endAngle:w+X+q});x.push(f),w=f.endAngle}}),N.createElement(U,null,r.renderSectorsStatically(x))})}},{key:"attachKeyboardHandlers",value:function(r){var s=this;r.onkeydown=function(i){if(!i.altKey)switch(i.key){case"ArrowLeft":{var o=++s.state.sectorToFocus%s.sectorRefs.length;s.sectorRefs[o].focus(),s.setState({sectorToFocus:o});break}case"ArrowRight":{var c=--s.state.sectorToFocus<0?s.sectorRefs.length-1:s.state.sectorToFocus%s.sectorRefs.length;s.sectorRefs[c].focus(),s.setState({sectorToFocus:c});break}case"Escape":{s.sectorRefs[s.state.sectorToFocus].blur(),s.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var r=this.props,s=r.sectors,i=r.isAnimationActive,o=this.state.prevSectors;return i&&s&&s.length&&(!o||!hn(o,s))?this.renderSectorsWithAnimation():this.renderSectorsStatically(s)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var r=this,s=this.props,i=s.hide,o=s.sectors,c=s.className,l=s.label,m=s.cx,d=s.cy,h=s.innerRadius,p=s.outerRadius,v=s.isAnimationActive,y=this.state.isAnimationFinished;if(i||!o||!o.length||!he(m)||!he(d)||!he(h)||!he(p))return null;var u=ne("recharts-pie",c);return N.createElement(U,{tabIndex:this.props.rootTabIndex,className:u,ref:function(_){r.pieRef=_}},this.renderSectors(),l&&this.renderLabels(o),At.renderCallByParent(this.props,null,!1),(!v||y)&&xn.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(r,s){return s.prevIsAnimationActive!==r.isAnimationActive?{prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,curSectors:r.sectors,prevSectors:[],isAnimationFinished:!0}:r.isAnimationActive&&r.animationId!==s.prevAnimationId?{prevAnimationId:r.animationId,curSectors:r.sectors,prevSectors:s.curSectors,isAnimationFinished:!0}:r.sectors!==s.curSectors?{curSectors:r.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(r,s){return r>s?"start":r<s?"end":"middle"}},{key:"renderLabelLineItem",value:function(r,s,i){if(N.isValidElement(r))return N.cloneElement(r,s);if(ie(r))return r(s);var o=ne("recharts-pie-label-line",typeof r!="boolean"?r.className:"");return N.createElement(ln,fe({},s,{key:i,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(r,s,i){if(N.isValidElement(r))return N.cloneElement(r,s);var o=i;if(ie(r)&&(o=r(s),N.isValidElement(o)))return o;var c=ne("recharts-pie-label-text",typeof r!="boolean"&&!ie(r)?r.className:"");return N.createElement(Qe,fe({},s,{alignmentBaseline:"middle",className:c}),o)}}])}(g.PureComponent);Pe=ee;Y(ee,"displayName","Pie");Y(ee,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!pn.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});Y(ee,"parseDeltaAngle",function(e,n){var a=Te(n-e),r=Math.min(Math.abs(n-e),360);return a*r});Y(ee,"getRealPieData",function(e){var n=e.data,a=e.children,r=F(e,!1),s=gn(a,kt);return n&&n.length?n.map(function(i,o){return D(D(D({payload:i},r),i),s&&s[o]&&s[o].props)}):s&&s.length?s.map(function(i){return D(D({},r),i.props)}):[]});Y(ee,"parseCoordinateOfPie",function(e,n){var a=n.top,r=n.left,s=n.width,i=n.height,o=vn(s,i),c=r+ke(e.cx,s,s/2),l=a+ke(e.cy,i,i/2),m=ke(e.innerRadius,o,0),d=ke(e.outerRadius,o,o*.8),h=e.maxRadius||Math.sqrt(s*s+i*i)/2;return{cx:c,cy:l,innerRadius:m,outerRadius:d,maxRadius:h}});Y(ee,"getComposedData",function(e){var n=e.item,a=e.offset,r=n.type.defaultProps!==void 0?D(D({},n.type.defaultProps),n.props):n.props,s=Pe.getRealPieData(r);if(!s||!s.length)return null;var i=r.cornerRadius,o=r.startAngle,c=r.endAngle,l=r.paddingAngle,m=r.dataKey,d=r.nameKey,h=r.valueKey,p=r.tooltipType,v=Math.abs(r.minAngle),y=Pe.parseCoordinateOfPie(r,a),u=Pe.parseDeltaAngle(o,c),x=Math.abs(u),_=m;pe(m)&&pe(h)?(st(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),_="value"):pe(m)&&(st(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),_=h);var w=s.filter(function(R){return ue(R,_,0)!==0}).length,C=(x>=360?w:w-1)*l,z=x-w*v-C,E=s.reduce(function(R,G){var M=ue(G,_,0);return R+(he(M)?M:0)},0),q;if(E>0){var Q;q=s.map(function(R,G){var M=ue(R,_,0),te=ue(R,d,G),X=(he(M)?M:0)/E,f;G?f=Q.endAngle+Te(u)*l*(M!==0?1:0):f=o;var O=f+Te(u)*((M!==0?v:0)+X*z),$=(f+O)/2,S=(y.innerRadius+y.outerRadius)/2,j=[{name:te,value:M,payload:R,dataKey:_,type:p}],I=J(y.cx,y.cy,S,$);return Q=D(D(D({percent:X,cornerRadius:i,name:te,tooltipPayload:j,midAngle:$,middleRadius:S,tooltipPosition:I},R),y),{},{value:ue(R,_),startAngle:f,endAngle:O,payload:R,paddingAngle:Te(u)*l}),Q})}return D(D({},y),{},{sectors:q,data:s})});var kr=yn({chartName:"PieChart",GraphicalChild:ee,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:Ke},{axisType:"radiusAxis",AxisComp:Me}],formatAxisMap:jn,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});const Tr=e=>{const n={MONDAY:"T2",TUESDAY:"T3",WEDNESDAY:"T4",THURSDAY:"T5",FRIDAY:"T6",SATURDAY:"T7",SUNDAY:"CN"},a=e.map(s=>({name:n[s.day_of_week],total:s.revenue,transactions:s.transaction_quantity})),r=["T2","T3","T4","T5","T6","T7","CN"];return a.sort((s,i)=>{const o=r.indexOf(s.name),c=r.indexOf(i.name);return o-c})},Pr=({active:e,payload:n,label:a})=>{if(e&&n&&n.length){const r=n[0].value,s=i=>new Intl.NumberFormat("vi-VN").format(i);return t.jsxs("div",{className:"rounded-lg border bg-white p-3 shadow-lg",children:[t.jsx("p",{className:"font-semibold text-gray-900",children:a}),t.jsxs("p",{className:"text-xs text-gray-600",children:["Doanh thu ngày trong tuần: ",s(r)," đ"]})]})}return null};function Or({weekdaysData:e}){if(!e||e.length===0)return t.jsx("div",{className:"flex h-[350px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})});const n=Tr(e);return t.jsx(we,{width:"100%",height:350,children:t.jsxs(Re,{data:n,children:[t.jsx(Le,{dataKey:"name",stroke:"#888888",fontSize:12,tickLine:!1,axisLine:!1}),t.jsx(Ee,{stroke:"#888888",fontSize:12,tickLine:!1,axisLine:!1,tickFormatter:a=>`${(a/1e6).toFixed(0)}M`}),t.jsx(Ae,{content:t.jsx(Pr,{})}),t.jsx(Ne,{dataKey:"total",fill:"currentColor",radius:[4,4,0,0],className:"fill-primary"})]})})}function Dr(){const{itemsData:e,isItemsLoading:n,itemsError:a,itemsSortBy:r}=H(),{topProducts:s,remainingAmount:i}=g.useMemo(()=>{if(!e.length)return{topProducts:[],remainingAmount:"0 ₫"};const o=e.slice(0,5),c=u=>new Intl.NumberFormat("vi-VN").format(u)+" ₫",l=u=>{switch(r){case"quantity_sold":return u.quantity_sold.toFixed(2);case"revenue_gross":return c(u.revenue_gross);case"revenue_net":default:return c(u.revenue_net)}},m=o.map(u=>({name:u.item_name,price:l(u),quantity:u.quantity_sold,unit:u.unit_name})),d=u=>{switch(r){case"quantity_sold":return u.quantity_sold;case"revenue_gross":return u.revenue_gross;case"revenue_net":default:return u.revenue_net}},h=o.reduce((u,x)=>u+d(x),0),v=e.reduce((u,x)=>u+d(x),0)-h,y=r==="quantity_sold"?v.toFixed(2):c(v);return{topProducts:m,remainingAmount:y}},[e,r]);return n?t.jsx("div",{className:"flex h-[200px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Đang tải dữ liệu..."})}):a?t.jsx("div",{className:"flex h-[200px] items-center justify-center",children:t.jsxs("div",{className:"text-sm text-red-500",children:["Lỗi: ",a]})}):s.length?t.jsxs("div",{className:"space-y-5",children:[s.map((o,c)=>t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx("div",{className:"w-4 text-lg font-bold",children:c+1}),t.jsxs("div",{className:"flex flex-1 items-center justify-between",children:[t.jsx("p",{className:"line-clamp-1 max-w-[200px] text-lg font-medium",children:o.name}),t.jsx("div",{className:"text-lg font-medium",children:o.price})]})]},c)),t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx("div",{className:"w-4"}),t.jsxs("div",{className:"flex flex-1 items-center justify-between",children:[t.jsx("p",{className:"text-muted-foreground line-clamp-1 max-w-[200px] text-lg font-medium",children:"Mặt hàng còn lại"}),t.jsx("div",{className:"text-muted-foreground text-lg font-medium",children:i})]})]})]}):t.jsx("div",{className:"flex h-[200px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})})}function Cr(){const{sourcesData:e,isSourcesLoading:n,sourcesError:a,defaultDateRange:r}=H(),s=g.useMemo(()=>{const l=m=>`${m.getDate().toString().padStart(2,"0")}/${(m.getMonth()+1).toString().padStart(2,"0")}/${m.getFullYear()}`;return`${l(r.from)} - ${l(r.to)}`},[r]),i=g.useMemo(()=>!e||e.length===0?null:e.find(l=>l.source_name==="GRABFOOD"),[e]),o=g.useMemo(()=>i?[{label:"Tổng giá trị đơn hàng",amount:i.revenue_gross},{label:"Tổng hoa hồng chiết khấu",amount:i.commission_amount},{label:"Tổng khuyến mại nhà hàng",amount:i.discount_amount},{label:"Tổng giảm giá đối tác",amount:i.partner_marketing_amount}]:[],[i]),c=l=>new Intl.NumberFormat("vi-VN").format(l)+" ₫";return n?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Đơn từ kênh: Grab"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s})]}),t.jsx(P,{children:t.jsx("div",{className:"text-muted-foreground py-8 text-center",children:"Đang tải dữ liệu..."})})]}):a?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Đơn từ kênh: Grab"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s})]}),t.jsx(P,{children:t.jsxs("div",{className:"py-8 text-center text-red-500",children:["Lỗi: ",a]})})]}):i?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Đơn từ kênh: Grab"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(W,{})]}),t.jsxs(P,{className:"space-y-4",children:[t.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-muted-foreground text-xs",children:"Tổng đơn"}),t.jsx("div",{className:"text-lg font-bold",children:i.total_bill.toLocaleString()})]}),t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-muted-foreground text-xs",children:"Tổng thực thu"}),t.jsx("div",{className:"text-lg font-bold",children:c(i.revenue_net)})]})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex items-center justify-between border-b-2 border-gray-200 pb-2",children:[t.jsx("span",{className:"text-xs font-semibold text-gray-900",children:"Danh mục"}),t.jsx("span",{className:"text-xs font-semibold text-gray-900",children:"Số tiền"})]}),o.map((l,m)=>t.jsxs("div",{className:"flex items-center justify-between border-b border-gray-100 py-2 last:border-b-0",children:[t.jsx("span",{className:"text-xs text-gray-700",children:l.label}),t.jsx("span",{className:"text-xs font-medium",children:c(l.amount)})]},m))]})]})]}):t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Đơn từ kênh: Grab"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s})]}),t.jsx(P,{children:t.jsx("div",{className:"flex h-[200px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})})})]})}function $r(){const{ktvReportData:e,isKtvReportLoading:n,ktvReportError:a,setKtvTimestamp:r}=H(),[s,i]=g.useState(8),[o,c]=g.useState(2025),l=g.useMemo(()=>`Tháng ${s}/${o}`,[s,o]),m=g.useMemo(()=>new Date(o,s-1,1).getTime(),[s,o]);g.useEffect(()=>{r(m)},[m,r]);const d=g.useMemo(()=>{const x=new Date,_=x.getMonth()+1,w=x.getFullYear();return Array.from({length:12},(C,z)=>{const E=z+1,q=o===w&&E>_;return{value:E,label:`Tháng ${E}`,disabled:q}})},[o]),h=g.useMemo(()=>{var x,_;return[{label:"Chi phí nguyên vật liệu",subtitle:"Bạn có",highlightNumber:((x=e==null?void 0:e.count_item_not_price)==null?void 0:x.toString())||"0",subtitleEnd:"món chưa có giá thành phẩm",amount:(e==null?void 0:e.material_cost)||0,percentage:e!=null&&e.percentage_material?`${e.percentage_material} %`:"0 %"},{label:"Chi phí trong tháng",subtitle:"Bạn có",highlightNumber:`${(e==null?void 0:e.count_cost_out)||0}/ ${(e==null?void 0:e.count_cost_out_all)||0}`,subtitleEnd:"mục chưa khai báo chi phí",amount:(e==null?void 0:e.cost_amount_inex)||0,percentage:e!=null&&e.percentage_inex?`${e.percentage_inex} %`:"0 %"},{label:"Chi phí phân bổ hàng tháng",subtitle:"Bạn đang có",highlightNumber:((_=e==null?void 0:e.count_tool)==null?void 0:_.toString())||"0",subtitleEnd:"khoản phân bổ",amount:(e==null?void 0:e.tool_cost)||0,percentage:e!=null&&e.percentage_tool?`${e.percentage_tool} %`:"0 %"}]},[e]),p=x=>new Intl.NumberFormat("vi-VN").format(x)+" ₫",v=(e==null?void 0:e.revenue_net)||0,y=(e==null?void 0:e.profit)||0,u=g.useMemo(()=>e?(e.material_cost||0)+(e.tool_cost||0)+(e.cost_amount_inex||0):0,[e]);return n?t.jsxs(A,{className:"col-span-1",children:[t.jsx(k,{children:t.jsx(T,{children:"Báo cáo lãi lỗ tháng"})}),t.jsx(P,{className:"flex h-[300px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Đang tải dữ liệu..."})})]}):a?t.jsxs(A,{className:"col-span-1",children:[t.jsx(k,{children:t.jsx(T,{children:"Báo cáo lãi lỗ tháng"})}),t.jsx(P,{className:"flex h-[300px] items-center justify-center",children:t.jsxs("div",{className:"text-sm text-red-500",children:["Lỗi: ",a]})})]}):!e||v===0&&y===0?t.jsxs(A,{className:"col-span-1",children:[t.jsx(k,{children:t.jsx(T,{children:"Báo cáo lãi lỗ tháng"})}),t.jsx(P,{className:"flex h-[300px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})})]}):t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Báo cáo lãi lỗ tháng"}),t.jsx("div",{className:"mt-2",children:t.jsxs(jt,{children:[t.jsx(bt,{asChild:!0,children:t.jsxs("button",{className:"flex items-center border border-gray-200 px-3 py-1 text-xs text-gray-900 hover:bg-gray-50",children:[l,t.jsx(Oe,{className:"ml-2 h-3 w-3"})]})}),t.jsx(_t,{align:"start",className:"w-48",children:t.jsxs("div",{className:"p-2",children:[t.jsxs("div",{className:"mb-2",children:[t.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Tháng"}),t.jsx("div",{className:"grid grid-cols-3 gap-1",children:d.map(x=>t.jsx("button",{onClick:()=>!x.disabled&&i(x.value),disabled:x.disabled,className:`rounded px-2 py-1 text-xs ${x.disabled?"cursor-not-allowed bg-gray-100 text-gray-400":s===x.value?"bg-blue-100 text-blue-700":"text-gray-700 hover:bg-gray-100"}`,children:x.value},x.value))})]}),t.jsxs("div",{children:[t.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Năm"}),t.jsx("input",{type:"number",value:o,onChange:x=>c(parseInt(x.target.value)||2025),className:"w-full rounded border border-gray-300 px-2 py-1 text-xs focus:border-blue-500 focus:outline-none",min:"2020",max:"2030"})]})]})})]})}),t.jsx(W,{})]}),t.jsxs(P,{className:"space-y-4",children:[t.jsxs("div",{className:"flex items-center justify-between pb-2",children:[t.jsx("span",{className:"text-xs font-semibold text-gray-900",children:"Doanh thu"}),t.jsx("span",{className:"text-xs font-semibold text-gray-900",children:p(v)})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex items-center justify-between border-b-2 border-gray-200 pb-2",children:[t.jsx("span",{className:"text-xs font-semibold text-gray-900",children:"Tổng chi phí"}),t.jsx("span",{className:"text-xs font-semibold text-gray-900",children:p(u)})]}),h.map((x,_)=>t.jsxs("div",{className:"flex items-start justify-between border-b border-gray-100 py-3 last:border-b-0",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("div",{className:"text-xs font-medium text-gray-900",children:x.label}),t.jsxs("div",{className:"mt-1 text-xs text-gray-500",children:[x.subtitle," ",t.jsx("span",{className:"font-bold text-red-600",children:x.highlightNumber})," ",x.subtitleEnd]})]}),t.jsxs("div",{className:"text-right",children:[t.jsx("div",{className:`text-xs ${x.amount===0?"text-gray-500":"font-medium"}`,children:p(x.amount)}),t.jsx("div",{className:"mt-1 text-xs font-bold text-red-600",children:x.percentage})]})]},_))]}),t.jsx("div",{className:"mt-4 border-t border-gray-200 pt-4",children:t.jsxs("div",{className:"flex items-start justify-between py-3",children:[t.jsx("div",{className:"flex-1",children:t.jsx("div",{className:"text-xs font-medium text-gray-900",children:"Lợi nhuận"})}),t.jsxs("div",{className:"text-right",children:[t.jsx("div",{className:"text-xs font-bold text-black",children:p(y)}),t.jsx("div",{className:"mt-1 text-xs font-bold text-red-600",children:e!=null&&e.percentage_profit?`${e.percentage_profit} %`:"0 %"})]})]})})]})]})}function Lr(){const{defaultDateRange:e,weekdaysData:n,isWeekdaysLoading:a,weekdaysError:r}=H(),s=g.useMemo(()=>{const i=l=>`${l.getDate().toString().padStart(2,"0")}/${(l.getMonth()+1).toString().padStart(2,"0")}/${l.getFullYear()}`,o=`${i(e.from)} 00:00`,c=`${i(e.to)} 23:59`;return`Báo cáo tính từ ${o} - ${c}`},[e]);return a?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Doanh thu 7 ngày gần nhất"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"})]}),t.jsx(P,{children:t.jsx("div",{className:"text-muted-foreground py-8 text-center",children:"Đang tải dữ liệu..."})})]}):r?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Doanh thu 7 ngày gần nhất"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"})]}),t.jsx(P,{children:t.jsxs("div",{className:"py-8 text-center text-red-500",children:["Lỗi: ",r]})})]}):!n||n.length===0?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Doanh thu 7 ngày gần nhất"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"}),t.jsx(W,{})]}),t.jsx(P,{children:t.jsx("div",{className:"flex h-[350px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})})})]}):t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Doanh thu 7 ngày gần nhất"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"}),t.jsx(W,{})]}),t.jsx(P,{className:"pl-2",children:t.jsx(Or,{weekdaysData:n})})]})}function Er(){const{sourcesData:e,isSourcesLoading:n,sourcesError:a,defaultDateRange:r}=H(),s=g.useMemo(()=>{const l=m=>`${m.getDate().toString().padStart(2,"0")}/${(m.getMonth()+1).toString().padStart(2,"0")}/${m.getFullYear()}`;return`${l(r.from)} - ${l(r.to)}`},[r]),i=g.useMemo(()=>!e||e.length===0?null:e.find(l=>l.source_name==="ShopeeFood"),[e]),o=g.useMemo(()=>i?[{label:"Tổng giá trị đơn hàng",amount:i.revenue_gross},{label:"Tổng hoa hồng chiết khấu",amount:i.commission_amount},{label:"Tổng khuyến mại nhà hàng",amount:i.discount_amount},{label:"Tổng giảm giá đối tác",amount:i.partner_marketing_amount}]:[],[i]),c=l=>new Intl.NumberFormat("vi-VN").format(l)+" ₫";return n?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Đơn từ kênh: Shopee"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s})]}),t.jsx(P,{children:t.jsx("div",{className:"text-muted-foreground py-8 text-center",children:"Đang tải dữ liệu..."})})]}):a?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Đơn từ kênh: Shopee"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s})]}),t.jsx(P,{children:t.jsxs("div",{className:"py-8 text-center text-red-500",children:["Lỗi: ",a]})})]}):i?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Đơn từ kênh: Shopee"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(W,{})]}),t.jsxs(P,{className:"space-y-4",children:[t.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-muted-foreground text-xs",children:"Tổng đơn"}),t.jsx("div",{className:"text-lg font-bold",children:i.total_bill.toLocaleString()})]}),t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-muted-foreground text-xs",children:"Tổng thực thu"}),t.jsx("div",{className:"text-lg font-bold",children:c(i.revenue_net)})]})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex items-center justify-between border-b-2 border-gray-200 pb-2",children:[t.jsx("span",{className:"text-xs font-semibold text-gray-900",children:"Danh mục"}),t.jsx("span",{className:"text-xs font-semibold text-gray-900",children:"Số tiền"})]}),o.map((l,m)=>t.jsxs("div",{className:"flex items-center justify-between border-b border-gray-100 py-2 last:border-b-0",children:[t.jsx("span",{className:"text-xs text-gray-700",children:l.label}),t.jsx("span",{className:"text-xs font-medium",children:c(l.amount)})]},m))]})]})]}):t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Đơn từ kênh: Shopee"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s})]}),t.jsx(P,{children:t.jsx("div",{className:"flex h-[200px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})})})]})}const pt=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8"],Rr=({cx:e,cy:n,midAngle:a,innerRadius:r,outerRadius:s,percent:i})=>{const o=Math.PI/180,c=r+(s-r)*.6,l=e+c*Math.cos(-a*o),m=n+c*Math.sin(-a*o);return t.jsx("text",{x:l,y:m,fill:"white",textAnchor:"middle",dominantBaseline:"central",fontSize:"12",fontWeight:"bold",children:`${(i*100).toFixed(1)}%`})},Ir=({active:e,payload:n})=>{if(e&&n&&n.length){const a=n[0];return t.jsxs("div",{className:"bg-background rounded-lg border p-2 shadow-md",children:[t.jsx("p",{className:"font-medium",children:a.name}),t.jsx("p",{className:"text-muted-foreground text-sm",children:me(a.value)})]})}return null};function Mr({data:e,isLoading:n,isError:a}){const r=g.useMemo(()=>n||a||!(e!=null&&e.length)?[]:e.sort((s,i)=>i.total_amount-s.total_amount).slice(0,5).map((s,i)=>({name:s.payment_method_name,value:s.total_amount,color:pt[i]||pt[0]})),[e,n,a]);return n?t.jsx("div",{className:"flex h-[400px] items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"border-primary mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2"}),t.jsx("p",{className:"text-muted-foreground text-sm",children:"Đang tải dữ liệu..."})]})}):a?t.jsx("div",{className:"flex h-[400px] items-center justify-center",children:t.jsx("div",{className:"text-center",children:t.jsx("p",{className:"text-sm text-red-500",children:"Lỗi tải dữ liệu phương thức thanh toán"})})}):r.length?t.jsxs("div",{className:"h-[250px] w-full",children:[t.jsx(we,{width:"100%",height:"100%",children:t.jsxs(kr,{children:[t.jsx(ee,{data:r,cx:"50%",cy:"50%",labelLine:!1,label:Rr,innerRadius:65,outerRadius:120,paddingAngle:2,dataKey:"value",children:r.map((s,i)=>t.jsx(kt,{fill:s.color},`cell-${i}`))}),t.jsx(Ae,{content:t.jsx(Ir,{})})]})}),t.jsx("div",{className:"mt-4 grid grid-cols-1 gap-2 text-sm",children:r.map((s,i)=>t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"h-3 w-3 rounded-full",style:{backgroundColor:s.color}}),t.jsx("span",{className:"text-muted-foreground",children:s.name})]}),t.jsx("span",{className:"font-bold",children:me(s.value)})]},i))})]}):t.jsx("div",{className:"flex h-[400px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})})}function Fr(){const{selectedStores:e,defaultDateRange:n}=H(),{company:a,brands:r}=Se(v=>v.auth),{currentBrandStores:s}=Ze(),i=r==null?void 0:r[0],o=g.useMemo(()=>{const v=n.from,y=n.to,u=w=>`${w.getDate().toString().padStart(2,"0")}/${(w.getMonth()+1).toString().padStart(2,"0")}/${w.getFullYear()}`,x=`${u(v)} 00:00`,_=`${u(y)} 23:59`;return`Báo cáo tính từ ${x} - ${_}`},[n]),c=g.useMemo(()=>{const v=new Date(n.from);return v.setHours(0,0,0,0),v.getTime()},[n.from]),l=g.useMemo(()=>{const v=new Date(n.to);return v.setHours(23,59,59,999),v.getTime()},[n.to]),m=g.useMemo(()=>{if(!s||s.length===0)return[];const v=e.filter(y=>y!=="all-stores");return v.length===0?s.map(y=>y.id):v},[e,s]),{data:d,isLoading:h,isError:p}=bn({startDate:c,endDate:l,selectedStoreIds:m,companyUid:(a==null?void 0:a.id)||"",brandUid:(i==null?void 0:i.id)||"",limit:5,enabled:!!(a!=null&&a.id&&(i!=null&&i.id)&&m.length>0)});return t.jsxs(A,{className:"col-span-1 h-[500px]",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Top 5 phương thức thanh toán"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:o}),t.jsx(W,{})]}),t.jsx(P,{children:t.jsx(Mr,{data:d,isLoading:h,isError:p})})]})}const gt=[{value:"revenue_net",label:"Doanh thu (net)"},{value:"quantity_sold",label:"Số lượng"},{value:"revenue_gross",label:"Doanh thu (gross)"}];function Kr({value:e,onChange:n}){const[a,r]=g.useState(!1),s=gt.find(i=>i.value===e);return t.jsxs(jt,{open:a,onOpenChange:r,children:[t.jsx(bt,{asChild:!0,children:t.jsxs(vt,{variant:"ghost",size:"sm",className:"text-muted-foreground hover:text-foreground h-auto p-0 text-xs",children:[s==null?void 0:s.label,t.jsx(Oe,{className:"ml-1 h-3 w-3"})]})}),t.jsx(_t,{align:"start",className:"w-40",children:gt.map(i=>t.jsxs(sn,{onClick:()=>{n(i.value),r(!1)},className:"text-xs",children:[i.label,e===i.value&&t.jsx("span",{className:"ml-auto text-blue-600",children:"✓"})]},i.value))})]})}function qr(){const{itemsSortBy:e,setItemsSortBy:n,defaultDateRange:a}=H(),r=g.useMemo(()=>{const s=c=>`${c.getDate().toString().padStart(2,"0")}/${(c.getMonth()+1).toString().padStart(2,"0")}/${c.getFullYear()}`,i=`${s(a.from)} 00:00`,o=`${s(a.to)} 23:59`;return`Báo cáo tính từ ${i} - ${o}`},[a]);return t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Top 5 mặt hàng bán chạy"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:r}),t.jsxs(b,{className:"text-muted-foreground text-xs",children:["Sắp xếp theo: ",t.jsx(Kr,{value:e,onChange:n})]}),t.jsx(W,{})]}),t.jsx(P,{children:t.jsx(Dr,{})})]})}const Br=({active:e,payload:n,label:a})=>{if(e&&n&&n.length){const r=n[0].payload,s=i=>new Intl.NumberFormat("vi-VN").format(i);return t.jsxs("div",{className:"min-w-48 rounded-lg border bg-white p-3 shadow-lg",children:[t.jsx("p",{className:"mb-2 font-semibold text-gray-900",children:a}),t.jsxs("div",{className:"space-y-1 text-xs text-gray-600",children:[t.jsxs("p",{children:["• Tổng doanh thu: ",s(r.revenue_net)," đ"]}),t.jsxs("p",{children:["• Tổng giảm giá: ",s(r.discount_amount)," đ"]}),t.jsxs("p",{children:["• Tổng hóa đơn: ",r.total_bill]})]})]})}return null};function Vr(){const{promotionsData:e,isPromotionsLoading:n,promotionsError:a}=H(),r=g.useMemo(()=>e.map(s=>({name:s.promotion_name,total:s.revenue_net,secondary:s.discount_amount,revenue_gross:s.revenue_gross,revenue_net:s.revenue_net,discount_amount:s.discount_amount,total_bill:s.total_bill,commission_amount:s.commission_amount,deduct_tax_amount:s.deduct_tax_amount})),[e]);return n?t.jsx("div",{className:"flex h-[350px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Đang tải dữ liệu..."})}):a?t.jsx("div",{className:"flex h-[350px] items-center justify-center",children:t.jsxs("div",{className:"text-sm text-red-500",children:["Lỗi: ",a]})}):r.length?t.jsx(we,{width:"100%",height:350,children:t.jsxs(Re,{data:r,children:[t.jsx(Le,{dataKey:"name",stroke:"#888888",fontSize:9,tickLine:!1,axisLine:!1,angle:-45,textAnchor:"end",height:80,interval:0,tick:{fontSize:9,textAnchor:"end"},tickFormatter:s=>s.length>15?s.substring(0,15)+"...":s}),t.jsx(Ee,{stroke:"#888888",fontSize:12,tickLine:!1,axisLine:!1,tickFormatter:s=>`${(s/1e6).toFixed(0)}M`}),t.jsx(Ae,{content:t.jsx(Br,{})}),t.jsx(Ne,{dataKey:"total",fill:"currentColor",radius:[4,4,0,0],className:"fill-primary",maxBarSize:40}),t.jsx(Ne,{dataKey:"secondary",fill:"#fb923c",radius:[4,4,0,0],maxBarSize:25})]})}):t.jsx("div",{className:"flex h-[350px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})})}function zr(){const e=g.useMemo(()=>{const n=new Date,a=new Date(n.getTime()-24*60*60*1e3),r=new Date(a.getTime()-6*24*60*60*1e3),s=c=>`${c.getDate().toString().padStart(2,"0")}/${(c.getMonth()+1).toString().padStart(2,"0")}/${c.getFullYear()}`,i=`${s(r)} 00:00`,o=`${s(a)} 23:59`;return`Báo cáo tính từ ${i} - ${o}`},[]);return t.jsxs(A,{className:"col-span-1 flex flex-col",children:[t.jsxs(k,{className:"flex-shrink-0",children:[t.jsx(T,{children:"Top 5 chương trình khuyến mại"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:e}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"}),t.jsx(W,{})]}),t.jsx(P,{className:"flex flex-1 flex-col pl-2",children:t.jsx(Vr,{})})]})}const Yr=e=>e.map(n=>({name:n.source_name,total:n.revenue_net})).sort((n,a)=>a.total-n.total).slice(0,5),Wr=({active:e,payload:n,label:a})=>{if(e&&n&&n.length){const r=n[0].value,s=i=>new Intl.NumberFormat("vi-VN").format(i);return t.jsxs("div",{className:"rounded-lg border bg-white p-3 shadow-lg",children:[t.jsx("p",{className:"font-semibold text-gray-900",children:a}),t.jsxs("p",{className:"text-xs text-gray-600",children:["Doanh thu từ nguồn: ",s(r)," đ"]})]})}return null};function Hr({sourcesData:e}){if(!e||e.length===0)return t.jsx("div",{className:"flex h-[350px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})});const n=Yr(e);return t.jsx(we,{width:"100%",height:350,children:t.jsxs(Re,{data:n,children:[t.jsx(Le,{dataKey:"name",stroke:"#888888",fontSize:12,tickLine:!1,axisLine:!1,tick:{fontSize:12,textAnchor:"middle"},interval:0}),t.jsx(Ee,{stroke:"#888888",fontSize:12,tickLine:!1,axisLine:!1,tickFormatter:a=>`${(a/1e6).toFixed(0)}M`}),t.jsx(Ae,{content:t.jsx(Wr,{})}),t.jsx(Ne,{dataKey:"total",fill:"currentColor",radius:[4,4,0,0],className:"fill-primary"})]})})}function Ur(){const{defaultDateRange:e,sourcesData:n,isSourcesLoading:a,sourcesError:r}=H(),s=g.useMemo(()=>{const i=l=>`${l.getDate().toString().padStart(2,"0")}/${(l.getMonth()+1).toString().padStart(2,"0")}/${l.getFullYear()}`,o=`${i(e.from)} 00:00`,c=`${i(e.to)} 23:59`;return`Báo cáo tính từ ${o} - ${c}`},[e]);return a?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Top 5 nguồn đơn"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"})]}),t.jsx(P,{children:t.jsx("div",{className:"text-muted-foreground py-8 text-center",children:"Đang tải dữ liệu..."})})]}):r?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Top 5 nguồn đơn"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"})]}),t.jsx(P,{children:t.jsxs("div",{className:"py-8 text-center text-red-500",children:["Lỗi: ",r]})})]}):!n||n.length===0?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Top 5 nguồn đơn"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"}),t.jsx(W,{})]}),t.jsx(P,{children:t.jsx("div",{className:"flex h-[350px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})})})]}):t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Top 5 nguồn đơn"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"}),t.jsx(W,{})]}),t.jsx(P,{className:"pl-2",children:t.jsx(Hr,{sourcesData:n})})]})}const Gr=e=>e.map(n=>({name:n.store_name,total:n.revenue_net,sales:n.total_sales})).sort((n,a)=>a.total-n.total).slice(0,5),Zr=({active:e,payload:n,label:a})=>{if(e&&n&&n.length){const r=n[0].value,s=i=>new Intl.NumberFormat("vi-VN").format(i);return t.jsxs("div",{className:"rounded-lg border bg-white p-3 shadow-lg",children:[t.jsx("p",{className:"font-semibold text-gray-900",children:a}),t.jsxs("p",{className:"text-xs text-gray-600",children:["Doanh thu ròng: ",s(r)," đ"]})]})}return null};function Qr({storesData:e}){if(!e||e.length===0)return t.jsx("div",{className:"flex h-[350px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})});const n=Gr(e);return t.jsx(we,{width:"100%",height:350,children:t.jsxs(Re,{data:n,children:[t.jsx(Le,{dataKey:"name",stroke:"#888888",fontSize:10,tickLine:!1,axisLine:!1,angle:0,textAnchor:"middle",height:60,interval:0,tick:{fontSize:10,textAnchor:"middle"},tickFormatter:a=>a.length>12?a.substring(0,12)+"...":a}),t.jsx(Ee,{stroke:"#888888",fontSize:12,tickLine:!1,axisLine:!1,tickFormatter:a=>`${(a/1e6).toFixed(0)}M`}),t.jsx(Ae,{content:t.jsx(Zr,{})}),t.jsx(Ne,{dataKey:"total",fill:"currentColor",radius:[4,4,0,0],className:"fill-primary",maxBarSize:30})]})})}function Xr(){const{defaultDateRange:e,storesData:n,isStoresLoading:a,storesError:r}=H(),s=g.useMemo(()=>{const i=l=>`${l.getDate().toString().padStart(2,"0")}/${(l.getMonth()+1).toString().padStart(2,"0")}/${l.getFullYear()}`,o=`${i(e.from)} 00:00`,c=`${i(e.to)} 23:59`;return`Báo cáo tính từ ${o} - ${c}`},[e]);return a?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Top 5 cửa hàng có doanh thu cao"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"})]}),t.jsx(P,{children:t.jsx("div",{className:"text-muted-foreground py-8 text-center",children:"Đang tải dữ liệu..."})})]}):r?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Top 5 cửa hàng có doanh thu cao"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"})]}),t.jsx(P,{children:t.jsxs("div",{className:"py-8 text-center text-red-500",children:["Lỗi: ",r]})})]}):!n||n.length===0?t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Top 5 cửa hàng có doanh thu cao"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"}),t.jsx(W,{})]}),t.jsx(P,{children:t.jsx("div",{className:"flex h-[350px] items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground text-sm",children:"Chưa có thông tin"})})})]}):t.jsxs(A,{className:"col-span-1",children:[t.jsxs(k,{children:[t.jsx(T,{children:"Top 5 cửa hàng có doanh thu cao"}),t.jsx(b,{className:"text-muted-foreground text-xs",children:s}),t.jsx(b,{className:"text-xs font-medium text-black",children:"Doanh thu (₫)"}),t.jsx(W,{})]}),t.jsx(P,{className:"pl-2",children:t.jsx(Qr,{storesData:n})})]})}function Jr(){return t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"mb-8 text-center",children:t.jsx("h2",{className:"text-xl font-bold",children:"Báo cáo thống kê 7 ngày trước"})}),t.jsxs("div",{className:"mb-8 flex flex-wrap items-stretch gap-4",children:[t.jsx("div",{className:"h-[500px] min-w-80 flex-1",children:t.jsx(Lr,{})}),t.jsx("div",{className:"h-[500px] min-w-80 flex-1",children:t.jsx(qr,{})}),t.jsx("div",{className:"h-[500px] min-w-80 flex-1",children:t.jsx(zr,{})})]}),t.jsxs("div",{className:"mb-8 flex flex-wrap items-stretch gap-4",children:[t.jsx("div",{className:"min-w-80 flex-1",children:t.jsx(Ur,{})}),t.jsx("div",{className:"min-w-80 flex-1",children:t.jsx(Fr,{})}),t.jsx("div",{className:"min-w-80 flex-1",children:t.jsx(Xr,{})})]}),t.jsxs("div",{className:"flex flex-wrap items-stretch gap-4",children:[t.jsx("div",{className:"min-w-80 flex-1",children:t.jsx(Er,{})}),t.jsx("div",{className:"min-w-80 flex-1",children:t.jsx(Cr,{})}),t.jsx("div",{className:"min-w-80 flex-1",children:t.jsx($r,{})})]})]})}function es(){var m;const{overviewData:e,isOverviewLoading:n,overviewError:a,dateRange:r}=H(),s=e!=null&&e.total_sales?e.revenue_net/e.total_sales:0,i=e!=null&&e.peo_count?e.revenue_net/e.peo_count:0,o=()=>{if(!(e!=null&&e.previous_period))return"";const d=new Date(r.from),h=new Date(r.to);if(d.toDateString()===h.toDateString()){const v=new Date(e.previous_period.start_date);return`so với Ngày ${v.getDate().toString().padStart(2,"0")}/${(v.getMonth()+1).toString().padStart(2,"0")}`}else{const v=new Date(e.previous_period.start_date),y=new Date(e.previous_period.end_date);return`so với Từ ngày ${v.getDate().toString().padStart(2,"0")}/${(v.getMonth()+1).toString().padStart(2,"0")} đến ngày ${y.getDate().toString().padStart(2,"0")}/${(y.getMonth()+1).toString().padStart(2,"0")}`}},c=d=>{const h=d>0,p=d<0;return{isPositive:h,isNegative:p,text:`${h?"Tăng":"Giảm"} ${Math.abs(d).toFixed(2)}%`,icon:h?"↑":"↓",variant:h?"secondary":"destructive"}},l=(m=e==null?void 0:e.previous_period)!=null&&m.percentage?c(e.previous_period.percentage):null;return n?t.jsxs("div",{className:"space-y-8",children:[t.jsx("div",{className:"animate-pulse",children:t.jsx("div",{className:"h-32 rounded-lg bg-gray-200"})}),t.jsx("div",{className:"grid grid-cols-1 gap-2 md:grid-cols-3",children:[1,2,3].map(d=>t.jsx("div",{className:"animate-pulse",children:t.jsx("div",{className:"h-24 rounded-lg bg-gray-200"})},d))})]}):a?t.jsx("div",{className:"py-8 text-center",children:t.jsxs("p",{className:"text-red-500",children:["Lỗi tải dữ liệu: ",a]})}):t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"mb-8",children:t.jsxs(A,{className:"gap-0",children:[t.jsx(k,{className:"pb-3",children:t.jsx(T,{className:"text-lg font-semibold",children:"Doanh thu (NET)"})}),t.jsx(P,{children:t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("div",{className:"text-3xl font-bold",children:me((e==null?void 0:e.revenue_net)||0)}),l&&t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs(Ye,{variant:l.variant,className:`flex items-center gap-1 ${l.isPositive?"border-green-200 bg-green-100 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400":""}`,children:[t.jsx("span",{children:l.icon}),t.jsx("span",{children:l.text})]}),t.jsx("span",{className:"text-muted-foreground text-sm",children:o()})]})]})})]})}),t.jsxs("div",{className:"mb-8 grid grid-cols-1 gap-2 md:grid-cols-3",children:[t.jsxs(A,{className:"gap-2",children:[t.jsxs(k,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(T,{className:"text-sm font-medium",children:"Giảm giá và chi phí"}),t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"text-muted-foreground h-4 w-4",children:t.jsx("path",{d:"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"})})]}),t.jsx(P,{children:t.jsx("div",{className:"text-2xl font-bold",children:me((e==null?void 0:e.discount_amount)||0)})})]}),t.jsxs(A,{className:"gap-2",children:[t.jsxs(k,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(T,{className:"text-sm font-medium",children:"Số hóa đơn"}),t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"text-muted-foreground h-4 w-4",children:[t.jsx("rect",{width:"20",height:"14",x:"2",y:"5",rx:"2"}),t.jsx("path",{d:"M2 10h20"})]})]}),t.jsxs(P,{children:[t.jsx("div",{className:"text-2xl font-bold",children:(e==null?void 0:e.total_sales)||0}),t.jsxs("p",{className:"text-muted-foreground text-xs",children:["Trung bình ",me(s)," / hoá đơn"]})]})]}),t.jsxs(A,{className:"gap-2",children:[t.jsxs(k,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(T,{className:"text-sm font-medium",children:"Số khách"}),t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"text-muted-foreground h-4 w-4",children:[t.jsx("path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}),t.jsx("circle",{cx:"9",cy:"7",r:"4"}),t.jsx("path",{d:"M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75"})]})]}),t.jsxs(P,{children:[t.jsx("div",{className:"text-2xl font-bold",children:(e==null?void 0:e.peo_count)||0}),t.jsxs("p",{className:"text-muted-foreground text-xs",children:["Trung bình ",me(i)," / khách"]})]})]})]})]})}function ts(){return t.jsxs(Rn,{children:[t.jsx(zt,{children:t.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[t.jsx(Ht,{}),t.jsx(Ut,{}),t.jsx(Wt,{})]})}),t.jsxs(Yt,{children:[t.jsx(Mn,{}),t.jsx(es,{}),t.jsx(Jr,{})]})]})}const Zs=ts;export{Zs as component};
