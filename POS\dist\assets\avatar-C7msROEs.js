import{r as u,j as d,C as w,P as v,_ as C,Y as f,c as g}from"./index-DZ2N7iEN.js";var m="Avatar",[R,z]=w(m),[j,S]=R(m),x=u.forwardRef((a,e)=>{const{__scopeAvatar:n,...r}=a,[s,t]=u.useState("idle");return d.jsx(j,{scope:n,imageLoadingStatus:s,onImageLoadingStatusChange:t,children:d.jsx(v.span,{...r,ref:e})})});x.displayName=m;var L="AvatarImage",E=u.forwardRef((a,e)=>{const{__scopeAvatar:n,src:r,onLoadingStatusChange:s=()=>{},...t}=a,l=S(L,n),o=_(r,t),i=C(c=>{s(c),l.onImageLoadingStatusChange(c)});return f(()=>{o!=="idle"&&i(o)},[o,i]),o==="loaded"?d.jsx(v.img,{...t,ref:e,src:r}):null});E.displayName=L;var I="AvatarFallback",b=u.forwardRef((a,e)=>{const{__scopeAvatar:n,delayMs:r,...s}=a,t=S(I,n),[l,o]=u.useState(r===void 0);return u.useEffect(()=>{if(r!==void 0){const i=window.setTimeout(()=>o(!0),r);return()=>window.clearTimeout(i)}},[r]),l&&t.imageLoadingStatus!=="loaded"?d.jsx(v.span,{...s,ref:e}):null});b.displayName=I;function p(a,e){return a?e?(a.src!==e&&(a.src=e),a.complete&&a.naturalWidth>0?"loaded":"loading"):"error":"idle"}function _(a,{referrerPolicy:e,crossOrigin:n}){const r=N(),s=u.useRef(null),t=r?(s.current||(s.current=new window.Image),s.current):null,[l,o]=u.useState(()=>p(t,a));return f(()=>{o(p(t,a))},[t,a]),f(()=>{const i=h=>()=>{o(h)};if(!t)return;const c=i("loaded"),A=i("error");return t.addEventListener("load",c),t.addEventListener("error",A),e&&(t.referrerPolicy=e),typeof n=="string"&&(t.crossOrigin=n),()=>{t.removeEventListener("load",c),t.removeEventListener("error",A)}},[t,n,e]),l}function y(){return()=>{}}function N(){return u.useSyncExternalStore(y,()=>!0,()=>!1)}var k=x,F=E,M=b;function T({className:a,...e}){return d.jsx(k,{"data-slot":"avatar",className:g("relative flex size-8 shrink-0 overflow-hidden rounded-full",a),...e})}function $({className:a,...e}){return d.jsx(F,{"data-slot":"avatar-image",className:g("aspect-square size-full",a),...e})}function H({className:a,...e}){return d.jsx(M,{"data-slot":"avatar-fallback",className:g("bg-muted flex size-full items-center justify-center rounded-full",a),...e})}export{T as A,$ as a,H as b};
