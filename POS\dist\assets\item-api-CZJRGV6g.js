import{b as v}from"./pos-api-PZMeNc3U.js";const s=new Map,n=new Map,m=10*60*1e3,E={getItems:async t=>{const r=`${t.company_uid}-${t.brand_uid}-${t.list_store_uid||"all"}-${t.city_uid||"all"}-${t.list_city_uid||"all"}-${t.skip_limit||!1}-${t.page||1}-${t.limit||50}-${t.search||""}-${t.active??1}-${t.list_item_id||"all"}-${t.is_all||!1}`,o=s.get(r);if(o&&Date.now()-o.timestamp<m)return o.data;const i=n.get(r);if(i)return i;const a=(async()=>{var c,d,u,_,y,w,g;try{const e=new URLSearchParams(Object.entries({company_uid:t.company_uid,brand_uid:t.brand_uid,...t.list_store_uid&&{list_store_uid:t.list_store_uid},...t.city_uid&&{city_uid:t.city_uid},...t.list_city_uid&&{list_city_uid:t.list_city_uid},...t.skip_limit!==void 0&&{skip_limit:t.skip_limit.toString()},...t.page!==void 0&&{page:t.page.toString()},...t.limit!==void 0&&{limit:t.limit.toString()},...t.search&&{search:t.search},...t.active!==void 0&&{active:t.active.toString()},...t.store_uid&&{store_uid:t.store_uid},...t.apply_with_store!==void 0&&{apply_with_store:t.apply_with_store.toString()},...t.list_item_id&&{list_item_id:t.list_item_id},...t.is_all!==void 0&&{is_all:t.is_all.toString()}}).filter(([,f])=>f!==void 0)),l=await v.get(`/mdata/v1/items?${e.toString()}`);if(!l.data||typeof l.data!="object")throw new Error("Invalid response format from items API");const h=l.data;return s.set(r,{data:h,timestamp:Date.now()}),h}catch(e){throw e.code==="ECONNABORTED"||(c=e.message)!=null&&c.includes("timeout")?new Error("Request timeout - server is taking too long to respond. Please try again."):((d=e.response)==null?void 0:d.status)===504?new Error("Gateway timeout (504) - server is overloaded. Please try again later."):((u=e.response)==null?void 0:u.status)===503?new Error("Service unavailable (503) - server is temporarily down. Please try again later."):((_=e.response)==null?void 0:_.status)>=500?new Error(`Server error (${e.response.status}) - please try again later.`):((y=e.response)==null?void 0:y.status)===429?new Error("Too many requests - please wait a moment before trying again."):((w=e.response)==null?void 0:w.status)===401?new Error("Unauthorized - please check your authentication."):((g=e.response)==null?void 0:g.status)===403?new Error("Forbidden - you do not have permission to access this resource."):e}finally{n.delete(r)}})();return n.set(r,a),a},formatCurrency:t=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND",minimumFractionDigits:0,maximumFractionDigits:0}).format(t),createItem:async t=>{var r,o;try{const i=await v.post("/mdata/v1/item",t);return s.clear(),i.data.data||i.data}catch(i){throw((r=i.response)==null?void 0:r.status)===400?new Error(((o=i.response.data)==null?void 0:o.message)||"Invalid data provided."):i}},clearCache:()=>{s.clear(),n.clear()}};export{E as i};
