import{j as s}from"./index-D0Grd55b.js";import{S as e}from"./skeleton-zUnEohn-.js";import"./date-range-picker-CruKYeHR.js";import"./form-Bk1C9kLO.js";import{b as l,e as a}from"./table-CLNYB6yq.js";function j(){return s.jsx(s.Fragment,{children:Array.from({length:5}).map((m,r)=>s.jsxs(l,{children:[s.jsx(a,{className:"text-center",children:s.jsx(e,{className:"mx-auto h-4 w-6"})}),s.jsx(a,{children:s.jsx(e,{className:"h-4 w-24"})}),s.jsx(a,{children:s.jsx(e,{className:"h-4 w-32"})}),s.jsx(a,{children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(e,{className:"h-4 w-20"}),s.jsx(e,{className:"h-4 w-4"})]})}),s.jsx(a,{children:s.jsx(e,{className:"h-8 w-8"})})]},r))})}export{j as T};
