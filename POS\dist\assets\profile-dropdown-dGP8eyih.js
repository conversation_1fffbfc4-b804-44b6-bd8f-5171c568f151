import{j as s,B as l,L as a}from"./index-D0Grd55b.js";import{A as o,a as t,b as i}from"./avatar-C7dnn6zI.js";import{D as d,a as c,b as x,d as h,e as r,f as j,c as e,g as n}from"./dropdown-menu-BvqrmFsX.js";function g(){return s.jsxs(d,{modal:!1,children:[s.jsx(c,{asChild:!0,children:s.jsx(l,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:s.jsxs(o,{className:"h-8 w-8",children:[s.jsx(t,{src:"/avatars/01.png",alt:"@shadcn"}),s.jsx(i,{children:"SN"})]})})}),s.jsxs(x,{className:"w-56",align:"end",forceMount:!0,children:[s.jsx(h,{className:"font-normal",children:s.jsxs("div",{className:"flex flex-col space-y-1",children:[s.jsx("p",{className:"text-sm leading-none font-medium",children:"satnaing"}),s.jsx("p",{className:"text-muted-foreground text-xs leading-none",children:"<EMAIL>"})]})}),s.jsx(r,{}),s.jsxs(j,{children:[s.jsx(e,{asChild:!0,children:s.jsxs(a,{to:"/settings",children:["Profile",s.jsx(n,{children:"⇧⌘P"})]})}),s.jsx(e,{asChild:!0,children:s.jsxs(a,{to:"/settings",children:["Billing",s.jsx(n,{children:"⌘B"})]})}),s.jsx(e,{asChild:!0,children:s.jsxs(a,{to:"/settings",children:["Settings",s.jsx(n,{children:"⌘S"})]})}),s.jsx(e,{children:"New Team"})]}),s.jsx(r,{}),s.jsxs(e,{children:["Log out",s.jsx(n,{children:"⇧⌘Q"})]})]})]})}export{g as P};
