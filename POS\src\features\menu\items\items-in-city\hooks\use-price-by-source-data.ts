import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { Source } from '@/types/sources'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { api } from '@/lib/api/pos/pos-api'
import { itemApi } from '@/lib/item-api'
import { itemTypesApi } from '@/lib/item-types-api'
import { sourcesApi } from '@/lib/sources-api'

import { QUERY_KEYS } from '@/constants/query-keys'

import type { ItemsInCity } from '../data'
import type { PriceBySourcePreviewItem } from '../list/components/modals/price-by-source-preview-dialog'

/**
 * Hook to fetch items for a specific city for price by source configuration
 */
export const useItemsForPriceBySource = (cityUid: string, enabled: boolean = true) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_LIST, 'price-by-source', cityUid],
    queryFn: async () => {
      const params = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        city_uid: cityUid,
        skip_limit: true,
        active: 1
      }

      const response = await itemApi.getItems(params)
      return response.data || []
    },
    enabled: enabled && !!(company?.id && selectedBrand?.id && cityUid),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  })
}

/**
 * Hook to fetch sources for a specific city for price by source configuration
 */
export const useSourcesForPriceBySource = (cityUid: string, enabled: boolean = true) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.SOURCES, 'price-by-source', cityUid],
    queryFn: async () => {
      const params = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        city_uid: cityUid,
        skip_limit: true
      }

      const response = await sourcesApi.getSources(params)
      return response || []
    },
    enabled: enabled && !!(company?.id && selectedBrand?.id && cityUid),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  })
}

/**
 * Hook to fetch item types for a specific city for price by source configuration
 */
export const useItemTypesForPriceBySource = (cityUid: string, enabled: boolean = true) => {
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  return useQuery({
    queryKey: [QUERY_KEYS.ITEM_TYPES, 'price-by-source', cityUid],
    queryFn: async () => {
      const params = {
        company_uid: company?.id || '',
        brand_uid: selectedBrand?.id || '',
        city_uid: cityUid,
        skip_limit: true,
        active: 1
      }

      const response = await itemTypesApi.getItemTypes(params)
      return response.data || []
    },
    enabled: enabled && !!(company?.id && selectedBrand?.id && cityUid),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  })
}

/**
 * Combined hook to fetch all data needed for price by source configuration
 */
export const usePriceBySourceData = (cityUid: string, enabled: boolean = true) => {
  const itemsQuery = useItemsForPriceBySource(cityUid, enabled)
  const sourcesQuery = useSourcesForPriceBySource(cityUid, enabled)
  const itemTypesQuery = useItemTypesForPriceBySource(cityUid, enabled)

  return {
    items: itemsQuery.data || [],
    sources: sourcesQuery.data || [],
    itemTypes: itemTypesQuery.data || [],
    isLoading: itemsQuery.isLoading || sourcesQuery.isLoading || itemTypesQuery.isLoading,
    isError: itemsQuery.isError || sourcesQuery.isError || itemTypesQuery.isError,
    error: itemsQuery.error || sourcesQuery.error || itemTypesQuery.error,
    refetch: () => {
      itemsQuery.refetch()
      sourcesQuery.refetch()
      itemTypesQuery.refetch()
    }
  }
}

/**
 * Transform PriceBySourcePreviewItem to API format for bulk update
 */
const transformPriceBySourceData = (
  previewItems: PriceBySourcePreviewItem[],
  originalItems: ItemsInCity[],
  sources: Source[]
): ItemsInCity[] => {
  return previewItems.map(previewItem => {
    const originalItem = originalItems.find(item => item.id === previewItem.item_uid)

    if (!originalItem) {
      throw new Error(`Original item not found for ${previewItem.item_uid}`)
    }

    const priceBySource: Array<{ source_id: string; price: number }> = []

    // Create dynamic source mapping from sources data
    const sourceMapping: Record<string, string> = {}
    sources.forEach(source => {
      // Create the display key format: "SOURCE_NAME [SOURCE_ID]"
      const displayKey = `${source.sourceName} [${source.sourceId}]`
      sourceMapping[displayKey] = source.sourceId
    })

    Object.entries(sourceMapping).forEach(([sourceKey, sourceId]) => {
      const price = previewItem[sourceKey]
      if (price !== undefined && price !== null && price !== '') {
        const numPrice = typeof price === 'string' ? parseFloat(price) : price
        if (!isNaN(numPrice) && numPrice > 0) {
          priceBySource.push({
            source_id: sourceId,
            price: numPrice
          })
        }
      }
    })

    return {
      ...originalItem,
      ots_price: previewItem.ots_price,
      ots_tax: previewItem.ots_tax / 100,
      ta_price: previewItem.ots_price,
      ta_tax: previewItem.ots_tax / 100,
      extra_data: {
        ...originalItem.extra_data,
        price_by_source: priceBySource
      }
    }
  })
}

/**
 * Hook to bulk update price by source configuration
 */
export const useBulkUpdatePriceBySource = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: { previewItems: PriceBySourcePreviewItem[]; originalItems: any[]; sources: any[] }) => {
      const transformedData = transformPriceBySourceData(data.previewItems, data.originalItems, data.sources)

      const response = await api.put('/mdata/v1/items', transformedData)

      return response.data.data || response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_LIST],
        refetchType: 'none'
      })

      setTimeout(() => {
        queryClient.refetchQueries({
          queryKey: [QUERY_KEYS.ITEMS_LIST]
        })
      }, 100)

      toast.success('Đã cập nhật cấu hình giá theo nguồn thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Có lỗi xảy ra khi lưu cấu hình: ${error.message}`)
    }
  })

  return { bulkUpdatePriceBySource: mutate, isUpdating: isPending }
}
