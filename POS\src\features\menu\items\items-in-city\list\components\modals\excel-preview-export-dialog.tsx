import { useEffect, useState } from 'react'

import { useCurrent<PERSON>rand } from '@/stores'
import { Trash2 } from 'lucide-react'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { useItemTypesData, useItemClassesData, useUnitsData, useCitiesData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import type { ItemsInCity } from '../../../data'
import { useBulkUpdateItemsInCity } from '../../../hooks'

export interface ExportItem {
  id: string
  item_id: string
  city_name: string
  item_name: string
  ots_price: number
  active: number
  item_id_barcode: string
  is_eat_with: number
  no_update_quantity_toping: number
  [key: string]: any
}

interface ExcelPreviewExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: ExportItem[]
}

export function ExcelPreviewExportDialog({ open, onOpenChange, data }: ExcelPreviewExportDialogProps) {
  const [exportData, setExportData] = useState<ExportItem[]>(data)
  const [isProcessing, setIsProcessing] = useState(false)

  const { user, company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  const { mutate: bulkUpdateItemsInCity, isPending: isBulkUpdating } = useBulkUpdateItemsInCity()

  const { data: itemTypes = [] } = useItemTypesData({
    skip_limit: true
  })
  const { data: itemClasses = [] } = useItemClassesData({
    skip_limit: true
  })
  const { data: units = [] } = useUnitsData()
  const { data: cities = [] } = useCitiesData()

  useEffect(() => {
    setExportData(data)
  }, [data])

  const handleRemoveItem = (index: number) => {
    setExportData(prev => prev.filter((_, i) => i !== index))
  }

  const handleConfirm = async () => {
    if (!company || !selectedBrand) {
      toast.error('Thiếu thông tin cần thiết để cập nhật')
      return
    }

    setIsProcessing(true)

    const transformedData: ItemsInCity[] = exportData.map(item => {
      const unit = units.find(u => u.unit_id === item.unit_id)
      const city = cities.find(c => c.city_name === item.city_name)

      const itemType = itemTypes.find(
        it => it.item_type_id === item.item_type_id || it.item_type_name === item.item_type_name
      )

      const itemClass = itemClasses.find(
        ic => ic.item_class_id === item.item_class_id || ic.item_class_name === item.item_class_name
      )

      const defaultUnit = units.find(u => u.unit_id === 'MON')
      const defaultItemType = itemTypes.find(it => it.item_type_name === 'LOẠI KHÁC')
      
      return {
        item_id: item.item_id,
        item_name: item.item_name,
        description: item.description || '',
        ots_price: item.ots_price || 0,
        ots_tax: (item.ots_tax || 0) / 100,
        ta_price: item.ots_price || 0,
        ta_tax: (item.ots_tax || 0) / 100,
        time_sale_hour_day: String(item.time_sale_hour_day ?? 0),
        time_sale_date_week: String(item.time_sale_date_week ?? 0),
        allow_take_away: 1,
        is_eat_with: item.is_eat_with || 0,
        image_path: item.image_path || '',
        image_path_thumb: item.image_path ? `${item.image_path}?width=185` : '',
        item_color: '',
        list_order: item.list_order || 0,
        is_service: item.is_item_service || 0,
        is_material: 0,
        active: item.active || 1,
        user_id: '',
        is_foreign: 0,
        quantity_default: 0,
        price_change: item.price_change || 0,
        currency_type_id: '',
        point: 0,
        is_gift: 0,
        is_fc: 0,
        show_on_web: 0,
        show_price_on_web: 0,
        cost_price: 0,
        is_print_label: item.is_print_label || 0,
        quantity_limit: 0,
        is_kit: 0,
        time_cooking: (item.time_cooking || 0) * 60000, // Convert minutes to milliseconds
        item_id_barcode: item.item_id_barcode || '',
        process_index: 0,
        is_allow_discount: item.is_allow_discount || 0,
        quantity_per_day: 0,
        item_id_eat_with: '',
        is_parent: 0,
        is_sub: 0,
        item_id_mapping: String(item.sku || ''),
        effective_date: 0,
        expire_date: 0,
        sort: item.list_order || 1,
        sort_online: 1000,
        extra_data: {
          cross_price: item.cross_price || [],
          formula_qrcode: item.inqr_formula || '',
          is_buffet_item: item.is_buffet_item || 0,
          up_size_buffet: [],
          is_item_service: item.is_item_service || 0,
          is_virtual_item: item.is_virtual_item || 0,
          price_by_source: item.price_by_source || [],
          enable_edit_price: item.price_change || 0,
          exclude_items_buffet: item.exclude_items_buffet || [],
          no_update_quantity_toping: item.no_update_quantity_toping || 0
        },
        revision: 0,
        unit_uid: unit?.id || defaultUnit?.id || '',
        unit_secondary_uid: null,
        item_type_uid: itemType?.id || defaultItemType?.id || '',
        item_class_uid: itemClass?.id || undefined,
        source_uid: null,
        brand_uid: selectedBrand.id,
        city_uid: city?.id || '',
        company_uid: company.id,
        customization_uid: item.customization_uid || '',
        is_fabi: 1,
        deleted: false,
        created_by: user?.email || '',
        updated_by: user?.email || '',
        deleted_by: null,
        created_at: item.created_at || Math.floor(Date.now() / 1000),
        updated_at: Math.floor(Date.now() / 1000),
        deleted_at: null,
        cities: city ? [{
          id: city.id,
          city_id: city.city_id || '',
          fb_city_id: city.fb_city_id || '',
          city_name: city.city_name,
          image_path: city.image_path,
          description: city.description || '',
          active: city.active || 1,
          extra_data: city.extra_data,
          revision: city.revision || 0,
          sort: city.sort || 0,
          created_by: city.created_by,
          updated_by: city.updated_by,
          deleted_by: city.deleted_by,
          created_at: city.created_at || 0,
          updated_at: city.updated_at || 0,
          deleted_at: city.deleted_at,
          items_cities: {
            item_uid: item.id,
            city_uid: city.id
          }
        }] : [],
        id: item.id
      } as unknown as ItemsInCity
    })

    bulkUpdateItemsInCity(transformedData, {
      onSuccess: () => {
        setIsProcessing(false)
        onOpenChange(false)
      },
      onError: error => {
        console.error('Error updating items:', error)
        toast.error(`Có lỗi xảy ra khi cập nhật món ăn: ${error}`)
        setIsProcessing(false)
      }
    })
  }

  const columns = [
    { key: 'item_id', label: 'Mã món', width: '120px' },
    { key: 'city_name', label: 'Thành phố', width: '120px' },
    { key: 'item_name', label: 'Tên', width: '200px' },
    { key: 'ots_price', label: 'Giá', width: '100px' },
    { key: 'active', label: 'Trạng thái', width: '100px' },
    { key: 'item_id_barcode', label: 'Mã barcode', width: '120px' },
    { key: 'is_eat_with', label: 'Món ăn kèm', width: '120px' },
    { key: 'no_update_quantity_toping', label: 'Không cập nhật số lượng', width: '180px' },
    { key: 'unit_name', label: 'Đơn vị', width: '100px' },
    { key: 'item_type_id', label: 'Nhóm', width: '120px' },
    { key: 'item_type_name', label: 'Tên nhóm', width: '150px' },
    { key: 'item_class_id', label: 'Loại món', width: '120px' },
    { key: 'item_class_name', label: 'Tên loại', width: '150px' },
    { key: 'description', label: 'Mô tả', width: '200px' },
    { key: 'sku', label: 'SKU', width: '100px' },
    { key: 'ots_tax', label: 'VAT (%)', width: '80px' },
    { key: 'time_cooking', label: 'Thời gian chế biến (phút)', width: '180px' },
    { key: 'price_change', label: 'Cho phép sửa giá khi bán', width: '180px' },
    { key: 'is_virtual_item', label: 'Cấu hình món ảo', width: '150px' },
    { key: 'is_item_service', label: 'Cấu hình món dịch vụ', width: '180px' },
    { key: 'is_buffet_item', label: 'Cấu hình món ăn là vé buffet', width: '200px' },
    { key: 'time_sale_hour_day', label: 'Giờ', width: '80px' },
    { key: 'time_sale_date_week', label: 'Ngày', width: '80px' },
    { key: 'list_order', label: 'Thứ tự', width: '80px' },
    { key: 'image_path', label: 'Hình ảnh', width: '120px' },
    { key: 'inqr_formula', label: 'Công thức inQR cho máy pha trà', width: '220px' }
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] max-w-7xl sm:max-w-4xl'>
        <DialogHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <DialogTitle className='text-xl font-semibold'>Xuất, sửa thực đơn</DialogTitle>
        </DialogHeader>

        <div className='space-y-4 overflow-hidden'>
          <ScrollArea className='h-[60vh] w-full rounded-md border'>
            <Table>
              <TableHeader className='sticky top-0 z-10 bg-white'>
                <TableRow>
                  <TableHead className='w-12'></TableHead>
                  {columns.map(column => (
                    <TableHead key={column.key} style={{ width: column.width }}>
                      {column.label}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {exportData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Button
                        variant='ghost'
                        size='icon'
                        onClick={() => handleRemoveItem(index)}
                        className='h-8 w-8 text-red-500 hover:text-red-700'
                      >
                        <Trash2 className='h-4 w-4' />
                      </Button>
                    </TableCell>
                    {columns.map(column => (
                      <TableCell key={column.key} style={{ width: column.width }}>
                        {(() => {
                          const value = item[column.key]

                          if (column.key === 'ots_price') {
                            return <span className='text-right'>{Number(value)?.toLocaleString('vi-VN') || 0} ₫</span>
                          }

                          if (column.key === 'active') {
                            return <span>{value}</span>
                          }

                          if (column.key === 'item_id' || column.key === 'item_id_barcode') {
                            return <span className='font-mono text-sm'>{value || ''}</span>
                          }

                          if (column.key === 'item_name') {
                            return <span className='font-medium'>{value || ''}</span>
                          }

                          if (['is_eat_with', 'no_update_quantity_toping', 'price_change',
                               'is_virtual_item', 'is_item_service', 'is_buffet_item'].includes(column.key)) {
                            return <span className='text-center'>{value}</span>
                          }

                          // Default case for all other columns
                          return <span>{value || ''}</span>
                        })()}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <ScrollBar orientation='horizontal' />
            <ScrollBar orientation='vertical' />
          </ScrollArea>

          <div className='flex items-center justify-between border-t pt-4'>
            <Button variant='outline' onClick={() => onOpenChange(false)}>
              Đóng
            </Button>
            <Button onClick={handleConfirm} disabled={isProcessing || isBulkUpdating}>
              {isProcessing || isBulkUpdating ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
