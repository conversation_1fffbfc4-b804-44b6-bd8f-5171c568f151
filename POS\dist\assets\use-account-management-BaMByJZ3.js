import{z as t,r as o}from"./index-DZ2N7iEN.js";const m=[{name:"Báo cáo",permissions:[{id:"all",name:"<PERSON><PERSON><PERSON> c<PERSON>",category:"Báo cáo"},{id:"revenue_member",name:"<PERSON><PERSON><PERSON> thu thành viên",category:"Báo cáo"},{id:"customer_report",name:"Báo cáo khách hàng",category:"Báo cáo"},{id:"customer_group_report",name:"S<PERSON> sánh nhóm khách",category:"Báo cáo"},{id:"customer_behavior_report",name:"Biến động khách hàng",category:"Báo cáo"},{id:"customer_feedback_report",name:"<PERSON><PERSON>n hồi khách hàng",category:"Báo cáo"},{id:"voucher_report",name:"Báo cáo voucher",category:"Báo cáo"},{id:"invoice_report",name:"<PERSON><PERSON><PERSON> kê hóa đơn",category:"Báo cáo"}]},{name:"<PERSON><PERSON> sách khách hàng",permissions:[{id:"customer_access",name:"Truy cập",category:"Danh sách khách hàng"},{id:"customer_filter",name:"Lọc khách hàng",category:"Danh sách khách hàng"},{id:"customer_archive",name:"Lưu bộ lọc",category:"Danh sách khách hàng"},{id:"customer_tag",name:"Thêm tag",category:"Danh sách khách hàng"},{id:"customer_export",name:"Xuất danh sách",category:"Danh sách khách hàng"},{id:"customer_import",name:"Nhập dữ liệu khách hàng",category:"Danh sách khách hàng"},{id:"customer_attach_filter",name:"Gắn tag theo bộ lọc",category:"Danh sách khách hàng"},{id:"customer_add_new",name:"Thêm khách hàng mới",category:"Danh sách khách hàng"}]},{name:"Chi tiết khách hàng",permissions:[{id:"customer_detail_access",name:"Truy cập",category:"Chi tiết khách hàng"},{id:"customer_info_edit",name:"Sửa thông tin khách hàng (Tên, ngày sinh,...)",category:"Chi tiết khách hàng"},{id:"customer_pos_sync",name:"Đồng bộ điểm từ POS",category:"Chi tiết khách hàng"},{id:"customer_edit",name:"Sửa hạng khách hàng",category:"Chi tiết khách hàng"}]},{name:"Lịch sử hạng thành viên",permissions:[{id:"member_history_access",name:"Truy cập",category:"Lịch sử hạng thành viên"}]},{name:"Công cụ phân tích",permissions:[{id:"analytics_access",name:"Truy cập",category:"Công cụ phân tích"},{id:"analytics_tag",name:"Thêm tag",category:"Công cụ phân tích"}]},{name:"Quản lý phản hồi khách hàng",permissions:[{id:"feedback_access",name:"Truy cập",category:"Quản lý phản hồi khách hàng"},{id:"feedback_process",name:"Xử lý",category:"Quản lý phản hồi khách hàng"},{id:"feedback_export",name:"Xuất danh sách",category:"Quản lý phản hồi khách hàng"}]},{name:"Campaign trên Momo",permissions:[{id:"momo_campaign_access",name:"Truy cập",category:"Campaign trên Momo"}]},{name:"Quản lý e-voucher",permissions:[{id:"evoucher_access",name:"Truy cập",category:"Quản lý e-voucher"},{id:"evoucher_search",name:"Tìm kiếm mã voucher",category:"Quản lý e-voucher"},{id:"evoucher_create",name:"Tạo chương trình",category:"Quản lý e-voucher"},{id:"evoucher_view",name:"Xem kết quả",category:"Quản lý e-voucher"},{id:"evoucher_list",name:"Xem danh sách mã voucher",category:"Quản lý e-voucher"},{id:"evoucher_edit",name:"Chỉnh sửa chương trình",category:"Quản lý e-voucher"},{id:"evoucher_copy",name:"Sao chép chương trình",category:"Quản lý e-voucher"},{id:"evoucher_deactivate",name:"Hủy, kích hoạt, gia hạn chương trình",category:"Quản lý e-voucher"},{id:"evoucher_export",name:"Xuất mã voucher",category:"Quản lý e-voucher"},{id:"evoucher_send",name:"Gửi mã voucher",category:"Quản lý e-voucher"},{id:"evoucher_priority",name:"Ưu đãi VinID",category:"Quản lý e-voucher"},{id:"evoucher_cancel",name:"Hủy voucher",category:"Quản lý e-voucher"}]},{name:"Quản lý broadcast",permissions:[{id:"broadcast_access",name:"Truy cập",category:"Quản lý broadcast"},{id:"broadcast_create",name:"Tạo broadcast",category:"Quản lý broadcast"}]},{name:"Trang đăng ký thành viên",permissions:[{id:"member_registration_access",name:"Trang đăng ký thành viên",category:"Trang đăng ký thành viên"}]},{name:"Loyalty",permissions:[{id:"loyalty_member",name:"Hạng thành viên",category:"Loyalty"}]}],b=t.object({username:t.string().min(1,"Tên người dùng là bắt buộc").min(3,"Tên người dùng phải có ít nhất 3 ký tự"),email:t.string().min(1,"Email là bắt buộc").email("Email không hợp lệ"),password:t.string().min(1,"Mật khẩu là bắt buộc").min(6,"Mật khẩu phải có ít nhất 6 ký tự"),permissions:t.array(t.string()).min(1,"Phải chọn ít nhất một quyền")});t.object({username:t.string().min(3,"Tên người dùng phải có ít nhất 3 ký tự").optional(),email:t.string().email("Email không hợp lệ").optional(),password:t.string().min(6,"Mật khẩu phải có ít nhất 6 ký tự").optional(),permissions:t.array(t.string()).min(1,"Phải chọn ít nhất một quyền").optional(),status:t.enum(["active","inactive"]).optional()});const v=[{id:"1",username:"tutimi",email:"<EMAIL>",status:"active",permissions:[{id:"all",name:"Tất cả",category:"Báo cáo"},{id:"customer_access",name:"Truy cập",category:"Danh sách khách hàng"}],createdAt:"2024-01-15T10:30:00Z",updatedAt:"2024-01-15T10:30:00Z"}];function f(){const[g,h]=o.useState(v),[u,i]=o.useState(!1),[d,r]=o.useState(null),l=async a=>{i(!0),r(null);try{await new Promise(c=>setTimeout(c,1e3));const e=m.flatMap(c=>c.permissions).filter(c=>a.permissions.includes(c.id)),n={id:Date.now().toString(),username:a.username,email:a.email,status:"active",permissions:e,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return h(c=>[...c,n]),n}catch(e){throw r("Có lỗi xảy ra khi tạo tài khoản"),e}finally{i(!1)}},y=async a=>{i(!0),r(null);try{await new Promise(e=>setTimeout(e,1e3)),h(e=>e.map(n=>{if(n.id===a.id){const c=a.permissions?m.flatMap(s=>s.permissions).filter(s=>a.permissions.includes(s.id)):n.permissions;return{...n,...a,permissions:c,updatedAt:new Date().toISOString()}}return n}))}catch(e){throw r("Có lỗi xảy ra khi cập nhật tài khoản"),e}finally{i(!1)}},p=async a=>{i(!0),r(null);try{await new Promise(e=>setTimeout(e,500)),h(e=>e.filter(n=>n.id!==a))}catch(e){throw r("Có lỗi xảy ra khi xóa tài khoản"),e}finally{i(!1)}},k=async a=>{i(!0),r(null);try{await new Promise(e=>setTimeout(e,500)),h(e=>e.map(n=>n.id===a?{...n,status:n.status==="active"?"inactive":"active",updatedAt:new Date().toISOString()}:n))}catch(e){throw r("Có lỗi xảy ra khi thay đổi trạng thái tài khoản"),e}finally{i(!1)}},_=o.useMemo(()=>m,[]);return{users:g,isLoading:u,error:d,createUser:l,updateUser:y,deleteUser:p,toggleUserStatus:k,permissionCategories:_}}export{b as c,f as u};
