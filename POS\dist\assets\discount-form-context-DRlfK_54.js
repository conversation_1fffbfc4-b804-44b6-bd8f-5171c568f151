import{u as f}from"./useQuery-Ck3BpOfq.js";import{ay as U,a3 as R,b as _,l as m,a4 as l,j as v,r as S}from"./index-D0Grd55b.js";import{u as D}from"./useMutation-ATsU-ht7.js";import{j as g}from"./date-utils-DBbLjCz0.js";import{a as c}from"./pos-api-B09qRspF.js";import{Q as d}from"./query-keys-3lmd-xp6.js";const P=async(e={})=>{var s;const t=new URLSearchParams;e.companyUid&&t.append("company_uid",e.companyUid),e.brandUid&&t.append("brand_uid",e.brandUid),e.page&&t.append("page",e.page.toString()),e.listStoreUid&&e.listStoreUid.length>0&&t.append("list_store_uid",e.listStoreUid.join(",")),e.status&&t.append("status",e.status),e.active!==void 0&&t.append("active",e.active.toString()),e.searchTerm&&t.append("search",e.searchTerm);const o=await c.get(`/mdata/v1/discounts?${t.toString()}`);if((s=o.data)!=null&&s.data){const{convertApiDiscountToDiscount:i}=await U(async()=>{const{convertApiDiscountToDiscount:n}=await import("./discounts-DCn5I4UW.js");return{convertApiDiscountToDiscount:n}},[]);return o.data.data.map(i)}return[]},T=async e=>{var s;const t=new URLSearchParams;t.append("company_uid",e.companyUid),t.append("brand_uid",e.brandUid),t.append("id",e.id);const o=await c.get(`/mdata/v1/discount?${t.toString()}`);if((s=o.data)!=null&&s.data)return o.data.data;throw new Error("Regular discount not found")},E=(e,t,o,s)=>{const i=g.convertDaysToBitFlags(e.selectedDays),n=g.convertHoursToBitFlags(e.selectedHours);console.log("Transform Regular - formData.filterState:",e.filterState);const{is_all:a,is_item:r,is_type:u,type_id:p,item_id:h,is_combo:b,combo_id:C}=e.filterState;return{store_uid:t,promotion_uid:e.promotionUid,discount_type:e.discountType,type_id:p,item_id:h,from_date:e.fromDate.getTime(),to_date:e.toDate.getTime(),time_sale_date_week:i,time_sale_hour_day:n,company_uid:o,brand_uid:s,ta_discount:e.discountValue,ots_discount:e.discountValue,is_all:a,is_type:u,is_item:r,extra_data:{is_combo:b,combo_id:C},is_update_same_discounts:!1}},x=async e=>{var t,o;try{console.log("🔥 CREATE Regular Discount API Call:"),console.log("🔥 URL: POST /mdata/v1/discount"),console.log("🔥 Payload:",JSON.stringify(e,null,2)),console.log("🔥 Expected cURL Payload:",JSON.stringify({store_uid:"e20d55dd-6dcc-4238-a32e-42f8ae6abaeb",promotion_uid:"82939bd6-**************-8ec3a0caaf7b",discount_type:"AMOUNT",type_id:"",item_id:"",from_date:17544996e5,to_date:1754585999e3,time_sale_date_week:32,time_sale_hour_day:28,company_uid:"595e8cb4-674c-49f7-adec-826b211a7ce3",brand_uid:"d43a01ec-2f38-4430-a7ca-9b3324f7d39e",ta_discount:100,ots_discount:100,is_all:1,is_type:0,is_item:0,extra_data:{is_combo:0,combo_id:""},is_update_same_discounts:!1},null,2));const i=await c.post("/mdata/v1/discount",e);return console.log("🔥 CREATE Regular Discount Response:",i.data),{success:!0,data:i.data}}catch(s){return console.error("🔥 CREATE Regular Discount Error:",s),{success:!1,message:((o=(t=s.response)==null?void 0:t.data)==null?void 0:o.message)||s.message||"Có lỗi xảy ra khi tạo regular discount"}}},F=(e,t)=>{const o=g.convertDaysToBitFlags(e.selectedDays),s=g.convertHoursToBitFlags(e.selectedHours),{is_all:i,is_item:n,is_type:a,type_id:r,item_id:u,is_combo:p,combo_id:h}=e.filterState;return{...t,ta_discount:e.discountValue,ots_discount:e.discountValue,is_all:i,is_type:a,is_item:n,type_id:r,item_id:u,discount_type:e.discountType,from_date:e.fromDate.getTime(),to_date:e.toDate.getTime(),time_sale_hour_day:s,time_sale_date_week:o,extra_data:{is_combo:p,combo_id:h},is_update_same_discounts:!1}},q=async e=>{var t,o;try{return{success:!0,data:(await c.put("/mdata/v1/discount",e)).data}}catch(s){return{success:!1,message:((o=(t=s.response)==null?void 0:t.data)==null?void 0:o.message)||s.message||"Có lỗi xảy ra khi cập nhật regular discount"}}},k=async e=>{var a;const o=`/mdata/v1/promotions?${new URLSearchParams({skip_limit:"true",company_uid:e.companyUid,brand_uid:e.brandUid,store_uid:e.storeUid,partner_auto_gen:"0",active:"1"}).toString()}`;console.log("🔥 Regular Discount Promotions API Call:",o),console.log("🔥 Params:",e);const s=await c.get(o);console.log("🔥 Regular Discount Promotions Response:",s.data);const n=(((a=s.data)==null?void 0:a.data)||[]).flatMap(r=>r.list_data.map(u=>({promotion_uid:u.id,promotion_name:u.promotion_name})));return console.log("🔥 Parsed Promotions:",n),n},O=async e=>{await c.put("/mdata/v1/discount",e)};function H(e={}){return f({queryKey:[d.DISCOUNTS,"regular",e],queryFn:()=>P(e),staleTime:2*60*1e3,gcTime:5*60*1e3})}function I(){const e=R();return D({mutationFn:O,onSuccess:()=>{e.invalidateQueries({queryKey:[d.DISCOUNTS]})}})}function M(e,t){const o=R(),{company:s}=_(),{selectedBrand:i}=m();return D({mutationFn:async n=>{if(!(s!=null&&s.id)||!(i!=null&&i.id))throw new Error("Thiếu thông tin company hoặc brand");const a=E(n,e,s.id,i.id),r=await x(a);if(!r.success)throw new Error(r.message||"Có lỗi xảy ra");return r.data},onSuccess:()=>{var n;l.success("Tạo regular discount thành công"),o.invalidateQueries({queryKey:[d.DISCOUNTS]}),(n=t==null?void 0:t.onSuccess)==null||n.call(t)},onError:n=>{const a=n.message||"Có lỗi xảy ra khi tạo regular discount";l.error(a)}})}function V(e,t){const o=R(),{company:s}=_(),{selectedBrand:i}=m();return D({mutationFn:async n=>{if(!(s!=null&&s.id)||!(i!=null&&i.id)||!e)throw new Error("Thiếu thông tin cần thiết để cập nhật");const a=await T({companyUid:s.id,brandUid:i.id,id:e}),r=F(n,a),u=await q(r);if(!u.success)throw new Error(u.message||"Có lỗi xảy ra");return u.data},onSuccess:()=>{var n;l.success("Cập nhật regular discount thành công"),o.invalidateQueries({queryKey:[d.DISCOUNTS]}),(n=t==null?void 0:t.onSuccess)==null||n.call(t)},onError:n=>{const a=n.message||"Có lỗi xảy ra khi cập nhật regular discount";l.error(a)}})}function $(e){const{company:t}=_(),{selectedBrand:o}=m();return f({queryKey:[d.DISCOUNTS,e],queryFn:()=>{if(!(t!=null&&t.id)||!(o!=null&&o.id)||!e)throw new Error("Thiếu thông tin cần thiết");return T({companyUid:t.id,brandUid:o.id,id:e})},enabled:!!e&&!!(t!=null&&t.id)&&!!(o!=null&&o.id),staleTime:2*60*1e3,gcTime:5*60*1e3})}function G(e,t={}){const{company:o}=_(),{selectedBrand:s}=m(),{enabled:i=!0}=t;return f({queryKey:[d.DISCOUNTS,"promotions",e,o==null?void 0:o.id,s==null?void 0:s.id],queryFn:()=>{if(!(o!=null&&o.id)||!(s!=null&&s.id)||!e)throw new Error("Thiếu thông tin cần thiết để lấy danh sách khuyến mãi");return k({companyUid:o.id,brandUid:s.id,storeUid:e})},enabled:i&&!!e&&!!(o!=null&&o.id)&&!!(s!=null&&s.id),staleTime:2*60*1e3,gcTime:5*60*1e3})}const w=S.createContext(void 0);function J({children:e,value:t}){return v.jsx(w.Provider,{value:t,children:e})}function y(){const e=S.useContext(w);if(e===void 0)throw new Error("useRegularDiscountFormContext must be used within a RegularDiscountFormProvider");return e}function W(){const{formData:e,updateFormData:t}=y();return{formData:e,updateFormData:t}}function Y(){const{handleBack:e,handleSave:t}=y();return{handleBack:e,handleSave:t}}function z(){const{isFormValid:e,isLoading:t,isEditMode:o}=y();return{isFormValid:e,isLoading:t,isEditMode:o}}function X(){const{promotions:e,isLoadingPromotions:t}=y();return{promotions:e,isLoadingPromotions:t}}export{J as R,H as a,Y as b,z as c,$ as d,W as e,X as f,P as g,G as h,M as i,V as j,I as u};
