import{e as a,r as p,j as t}from"./index-DZ2N7iEN.js";import{I as e}from"./item-detail-form-DHoJO6Zn.js";import"./form-CUvXDg29.js";import"./exceljs.min-CLCFqwcY.js";import"./pos-api-PZMeNc3U.js";import{l as c}from"./customization-dialog-Da7huSuz.js";import"./user-B3TbfMPQ.js";import"./vietqr-api-y35earyI.js";import"./crm-api-DyqsDFNF.js";import"./header-DUOMiURq.js";import"./main-DEy6aM-s.js";import"./search-context-bUKMT4ET.js";import"./date-range-picker-extpnOqj.js";import"./multi-select-B_hvbjyS.js";import"./zod-Dyorah_P.js";import"./use-upload-image-DiW50L6Q.js";import"./images-api-cr7Vek97.js";import"./use-item-types-BdROFFdK.js";import"./useQuery-CCx3k6lE.js";import"./utils-km2FGkQ4.js";import"./useMutation-zADCXE7W.js";import"./query-keys-3lmd-xp6.js";import"./use-item-classes-JUakwOO-.js";import"./use-units-CwHCNZjQ.js";import"./use-items-DL_H3tEk.js";import"./item-api-CZJRGV6g.js";import"./use-removed-items-DMGo-T5T.js";import"./use-customizations-BihGvaSQ.js";import"./use-customization-by-id-B2xOzkgp.js";import"./use-sources-BcmDTqiT.js";import"./sources-api-DJQwcNwK.js";import"./sources-CfiQ7039.js";import"./calendar-CoHe9sRq.js";import"./createLucideIcon-2r9cCEY3.js";import"./index-CC24pdSB.js";import"./isSameMonth-C8JQo-AN.js";import"./checkbox-Bqls14Dj.js";import"./index-DIwSqRjm.js";import"./check-CP51yA4o.js";import"./input-DpObCffE.js";import"./textarea-DOc8ucX0.js";import"./combobox-B_tJR0V_.js";import"./command-By9h7q-C.js";import"./dialog-DDb7K8l1.js";import"./search-Cr9zvKK2.js";import"./popover-BK8t3srL.js";import"./chevrons-up-down-Cl92_NdW.js";import"./upload-BpTKdC5P.js";import"./collapsible-Dk62AUDY.js";import"./confirm-dialog-DAqmAcjM.js";import"./alert-dialog-3tViT-1a.js";import"./date-picker-DAO9d4dI.js";import"./calendar-3mjBqfJn.js";import"./circle-help-CYiLfMW4.js";import"./select-B60YVMMU.js";import"./index-Csh0LmDQ.js";import"./chevron-right-D-SI6PAy.js";import"./use-dialog-state-CIsfF4Gm.js";import"./modal-C3wc7cQn.js";import"./separator-Bnr4MN7f.js";import"./createReactComponent-CQMxkbLi.js";import"./scroll-area-CoEnZUVR.js";import"./IconChevronRight-Cvwf7k8w.js";import"./react-icons.esm-Ck8h5xF8.js";import"./badge-5IdLxoVq.js";import"./circle-x-DCSjVXFs.js";const vt=function(){const i=a({from:"/_authenticated/menu/items/items-in-city/detail"}),[r,s]=p.useState(null),o=i==null?void 0:i.id,{data:m,isLoading:n}=c(o,!!o);return o?(p.useEffect(()=>{m&&s(m.data)},[m]),n||!r?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):t.jsx(e,{currentRow:{...r,item_id:"",item_id_barcode:""},isCopyMode:!!r})):t.jsx(e,{})};export{vt as component};
