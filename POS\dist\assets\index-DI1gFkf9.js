import{j as e,B as I,r as g,a as L,l as V,b as E,a4 as H,h as O}from"./index-DZ2N7iEN.js";import{u as q}from"./useQuery-CCx3k6lE.js";import{u as Q,a as Y,g as W}from"./discount-form-context-CC89Hfsy.js";import{Q as J}from"./query-keys-3lmd-xp6.js";import"./pos-api-PZMeNc3U.js";import"./vietqr-api-y35earyI.js";import{u as X}from"./use-combos-tJ3iCRsv.js";import"./user-B3TbfMPQ.js";import"./crm-api-DyqsDFNF.js";import{H as Z}from"./header-DUOMiURq.js";import{M as B}from"./main-DEy6aM-s.js";import{P as ee}from"./profile-dropdown-CYtncOP3.js";import{S as te,T as se}from"./search-BE7sNBUd.js";import"./date-range-picker-extpnOqj.js";import"./form-CUvXDg29.js";import{S as C,a as S,b as y,c as b,d as u,C as ae}from"./select-B60YVMMU.js";import{D as ie,a as ne,b as re,c as le}from"./dropdown-menu-Ci4yPenc.js";import{u as oe,a as de}from"./use-discount-programs-_fo9WZyg.js";import{P as ce}from"./modal-C3wc7cQn.js";import{C as M}from"./checkbox-Bqls14Dj.js";import{B as $}from"./badge-5IdLxoVq.js";import{a as me,C as he}from"./chevron-right-D-SI6PAy.js";import"./utils-km2FGkQ4.js";import"./useMutation-zADCXE7W.js";import"./date-utils-DBbLjCz0.js";import"./separator-Bnr4MN7f.js";import"./avatar-C7msROEs.js";import"./search-context-bUKMT4ET.js";import"./command-By9h7q-C.js";import"./calendar-CoHe9sRq.js";import"./createLucideIcon-2r9cCEY3.js";import"./index-CC24pdSB.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DDb7K8l1.js";import"./search-Cr9zvKK2.js";import"./createReactComponent-CQMxkbLi.js";import"./scroll-area-CoEnZUVR.js";import"./index-Csh0LmDQ.js";import"./IconChevronRight-Cvwf7k8w.js";import"./IconSearch-Do03nWQy.js";import"./react-icons.esm-Ck8h5xF8.js";import"./popover-BK8t3srL.js";import"./index-DIwSqRjm.js";import"./check-CP51yA4o.js";import"./index-DeeqTHK3.js";import"./error-utils-Cfeehata.js";function xe({selectedStoreId:l,onStoreChange:j,selectedPromotion:r,onPromotionChange:p,selectedStatus:o,onStatusChange:f,selectedExpiry:m,onExpiryChange:a,stores:v,promotions:h,onCopyModalOpen:c,onCreateDiscount:N}){return e.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold whitespace-nowrap",children:"Chương trình giảm giá"}),e.jsx("div",{className:"min-w-[160px]",children:e.jsxs(C,{value:l,onValueChange:j,children:[e.jsx(S,{children:e.jsx(y,{placeholder:"Tất cả các điểm"})}),e.jsxs(b,{children:[e.jsx(u,{value:"all",children:"Tất cả các điểm"}),v.map(x=>e.jsx(u,{value:x.id,children:x.store_name},x.id))]})]})}),e.jsx("div",{className:"min-w-[160px]",children:e.jsxs(C,{value:r,onValueChange:p,children:[e.jsx(S,{children:e.jsx(y,{placeholder:"Tất cả các CKTM"})}),e.jsxs(b,{children:[e.jsx(u,{value:"all",children:"Tất cả các CKTM"}),h.map(x=>e.jsx(u,{value:x.promotion_id,children:x.promotion_name},x.promotion_id))]})]})}),e.jsx("div",{className:"min-w-[130px]",children:e.jsxs(C,{value:o,onValueChange:f,children:[e.jsx(S,{children:e.jsx(y,{placeholder:"Tất cả trạng thái"})}),e.jsxs(b,{children:[e.jsx(u,{value:"all",children:"Tất cả trạng thái"}),e.jsx(u,{value:"1",children:"Active"}),e.jsx(u,{value:"0",children:"Deactive"})]})]})}),e.jsx("div",{className:"min-w-[150px]",children:e.jsxs(C,{value:m,onValueChange:a,children:[e.jsx(S,{children:e.jsx(y,{placeholder:"Tất cả ngày áp dụng"})}),e.jsxs(b,{children:[e.jsx(u,{value:"all",children:"Tất cả ngày áp dụng"}),e.jsx(u,{value:"expired",children:"Hết hạn"}),e.jsx(u,{value:"unexpired",children:"Chưa hết hạn"})]})]})}),e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsxs(ie,{children:[e.jsx(ne,{asChild:!0,children:e.jsxs(I,{variant:"outline",size:"sm",children:["Tiện ích ",e.jsx(ae,{className:"ml-2 h-4 w-4"})]})}),e.jsx(re,{children:e.jsx(le,{onClick:c,children:"Sao chép CTGG"})})]}),e.jsx(I,{size:"sm",onClick:N,children:"Tạo giảm giá"})]})]})}function pe({isOpen:l,onClose:j}){const[r,p]=g.useState(""),[o,f]=g.useState(new Set),[m,a]=g.useState(new Set),{currentBrandStores:v}=L(),{selectedBrand:h}=V(),{company:c}=E(),N=oe(),{data:x,isLoading:D}=de({companyUid:c==null?void 0:c.id,brandUid:h==null?void 0:h.id,storeUid:r,enabled:!!r,includePromotionPartnerAutoGen:!1}),d=(x==null?void 0:x.data)||[],n=v.filter(t=>t.id!==r),k=t=>{const i=new Set(m);i.has(t)?i.delete(t):i.add(t),a(i)},w=t=>{const i=new Set(o);i.has(t)?i.delete(t):i.add(t),f(i)},T=()=>{o.size===n.length?f(new Set):f(new Set(n.map(t=>t.id)))},P=async()=>{if(!(!(c!=null&&c.id)||!(h!=null&&h.id)||!r||o.size===0||m.size===0))try{const t=Array.from(m),i=Array.from(o);for(const U of i)await N.mutateAsync({companyUid:c.id,brandUid:h.id,listDiscountUid:t,sourceStore:r,targetStore:U});H.success("Sao chép thành công"),_()}catch(t){console.error("Error copying discount programs:",t)}},_=()=>{p(""),f(new Set),a(new Set),j()},A=d.filter(t=>m.has(t.id));return e.jsx(ce,{title:"Sao chép chương trình giảm giá",open:l,onOpenChange:_,onCancel:_,onConfirm:P,confirmText:"Sao chép",confirmDisabled:!r||o.size===0||m.size===0||N.isPending,maxWidth:"sm:max-w-6xl",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng nguồn"}),e.jsxs(C,{value:r,onValueChange:p,children:[e.jsx(S,{className:"mt-1",children:e.jsx(y,{placeholder:"Chọn cửa hàng nguồn"})}),e.jsx(b,{children:v.map(t=>e.jsx(u,{value:t.id,children:t.store_name},t.id))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chương trình giảm giá"}),e.jsx("div",{className:"max-h-64 overflow-y-auto rounded-md border p-3",children:D?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Đang tải..."}):d.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:r?"Không có chương trình giảm giá":"Chọn cửa hàng nguồn"}):e.jsx("div",{className:"space-y-3",children:d.map(t=>{var i;return e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(M,{id:t.id,checked:m.has(t.id),onCheckedChange:()=>k(t.id)}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-sm font-medium",children:((i=t.promotion)==null?void 0:i.promotion_name)||"N/A"}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Giảm"," ",t.discount_type==="AMOUNT"?`${(t.ta_discount||t.ots_discount||0).toLocaleString("vi-VN")} ₫`:`${((t.ta_discount||t.ots_discount||0)*100).toFixed(0)}%`]})]})]},t.id)})})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng đích"}),e.jsxs(C,{children:[e.jsx(S,{className:"mt-1",children:e.jsx(y,{placeholder:o.size===0?"Chọn cửa hàng đích":`Đã chọn ${o.size} cửa hàng`})}),e.jsx(b,{children:n.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Chọn cửa hàng nguồn trước"}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center space-x-3 border-b px-2 py-1.5",children:[e.jsx(M,{id:"select-all-targets",checked:o.size===n.length&&n.length>0,onCheckedChange:T,onClick:t=>t.stopPropagation()}),e.jsx("label",{htmlFor:"select-all-targets",className:"flex-1 cursor-pointer text-sm font-medium",onClick:t=>{t.preventDefault(),T()},children:"Chọn tất cả"})]}),n.map(t=>e.jsxs("div",{className:"flex items-center space-x-3 px-2 py-1.5",children:[e.jsx(M,{id:`target-${t.id}`,checked:o.has(t.id),onCheckedChange:()=>w(t.id),onClick:i=>i.stopPropagation()}),e.jsx("label",{htmlFor:`target-${t.id}`,className:"flex-1 cursor-pointer text-sm font-medium",onClick:i=>{i.preventDefault(),w(t.id)},children:t.store_name})]},t.id))]})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chương trình được chọn"}),e.jsx("div",{className:"max-h-64 overflow-y-auto rounded-md border p-3",children:A.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Chưa chọn chương trình nào"}):e.jsx("div",{className:"space-y-2",children:A.map(t=>{var i;return e.jsxs("div",{className:"bg-muted rounded-md p-2",children:[e.jsx("div",{className:"text-sm font-medium",children:((i=t.promotion)==null?void 0:i.promotion_name)||"N/A"}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Giảm"," ",t.discount_type==="AMOUNT"?`${(t.ta_discount||t.ots_discount||0).toLocaleString("vi-VN")} ₫`:`${((t.ta_discount||t.ots_discount||0)*100).toFixed(0)}%`]})]},t.id)})})})]})]})]})})}function ue({discounts:l,isLoading:j,onToggleActive:r,currentPage:p,pageSize:o=20}){const f=O(),m=a=>{f({to:"/sale/discount/regular/detail/$id",params:{id:a}})};return e.jsx("div",{className:"rounded-md border",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-muted/50 border-b",children:[e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"#"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"Tên CT"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"Cửa hàng"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"Tuỳ chỉnh"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"Phần trăm / Số tiền"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"Thao tác"})]})}),e.jsx("tbody",{children:j?e.jsx("tr",{children:e.jsx("td",{colSpan:6,className:"h-24 text-center",children:"Đang tải dữ liệu..."})}):l.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:6,className:"text-muted-foreground h-24 text-center",children:"Không có dữ liệu"})}):l.map((a,v)=>{const h=(p-1)*o+v+1;return e.jsxs("tr",{className:"hover:bg-muted/50 cursor-pointer border-b",onClick:()=>m(a.id),children:[e.jsx("td",{className:"px-4 py-3",children:h}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"font-medium",children:a.promotionName||"N/A"})}),e.jsx("td",{className:"px-4 py-3",children:a.storeName}),e.jsx("td",{className:"px-4 py-3",children:e.jsxs("div",{className:"space-y-1",children:[a.toDate<Date.now()?e.jsx("div",{className:"font-medium text-red-600",children:"Hết hạn"}):e.jsxs("div",{className:"text-sm",children:["Từ ",new Date(a.fromDate).toLocaleDateString("vi-VN")," đến"," ",new Date(a.toDate).toLocaleDateString("vi-VN")]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:[a.isAll===1&&"(Áp dụng cho tất cả)",a.isItem===1&&a.itemId&&`(Áp dụng cho ${a.itemId.split(",").length} món)`,a.isType===1&&a.typeId&&`(Áp dụng cho ${a.typeId.split(",").length} loại món)`,a.isCombo===1&&a.comboId&&`(Áp dụng cho ${a.comboId.split(",").length} combo)`]})]})}),e.jsx("td",{className:"px-4 py-3",children:a.discountType==="AMOUNT"?`${a.taDiscount.toLocaleString("vi-VN")}đ`:`${(a.taDiscount*100).toFixed(0)}%`}),e.jsx("td",{className:"px-4 py-3",children:a.active===1?e.jsx($,{variant:"default",className:"cursor-pointer bg-green-100 text-green-800 hover:bg-green-200",onClick:c=>{c.stopPropagation(),r(a)},children:"Active"}):e.jsx($,{variant:"destructive",className:"cursor-pointer bg-red-100 text-red-800 hover:bg-red-200",onClick:c=>{c.stopPropagation(),r(a)},children:"Deactive"})})]},a.id)})})]})})}function ge({currentPage:l,onPageChange:j,hasNextPage:r}){const p=()=>{l>1&&j(l-1)},o=()=>{r&&j(l+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(I,{variant:"outline",size:"sm",onClick:p,disabled:l===1,className:"flex items-center gap-2",children:[e.jsx(me,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:l}),e.jsxs(I,{variant:"outline",size:"sm",onClick:o,disabled:!r,className:"flex items-center gap-2",children:["Sau",e.jsx(he,{className:"h-4 w-4"})]})]})}function je(){const[l,j]=g.useState("all"),[r,p]=g.useState("all"),[o,f]=g.useState("all"),[m,a]=g.useState("all"),[v,h]=g.useState(!1),[c,N]=g.useState(1),x=O(),{currentBrandStores:D}=L(),{selectedBrand:d}=V(),{company:n}=E(),k=Q(),w=l==="all"?D.map(s=>s.id):[l],{data:T=[]}=X({storeUid:l==="all"?void 0:l}),P={companyUid:n==null?void 0:n.id,brandUid:d==null?void 0:d.id,page:c,listStoreUid:w,status:m==="all"?void 0:m,active:o==="all"?void 0:parseInt(o)},{data:_=[],isLoading:A}=Y(P),t={...P,page:c+1},{data:i=[]}=q({queryKey:[J.DISCOUNTS,"regular",t],queryFn:()=>W(t),enabled:!!(n!=null&&n.id)&&!!(d!=null&&d.id)&&_.length>0,staleTime:2*60*1e3,gcTime:5*60*1e3}),U=r==="all"?_:_.filter(s=>s.promotionId===r),z=A;g.useEffect(()=>{p("all")},[l]),g.useEffect(()=>{N(1)},[l,o,m,r]);const R=s=>{const K={id:s.id,created_at:s.createdAt,created_by:s.createdBy,updated_at:s.updatedAt,updated_by:s.updatedBy,deleted:s.deleted||!1,deleted_at:s.deletedAt||null,deleted_by:s.deletedBy||null,ta_discount:s.taDiscount,ots_discount:s.otsDiscount,is_all:s.isAll,is_type:s.isType,is_item:s.isItem,type_id:s.typeId,item_id:s.itemId,discount_type:s.discountType,from_date:s.fromDate,to_date:s.toDate,time_sale_hour_day:0,time_sale_date_week:0,description:null,extra_data:{combo_id:"",is_combo:0},active:s.active===1?0:1,revision:null,promotion_uid:s.promotionUid,brand_uid:(d==null?void 0:d.id)||"",company_uid:(n==null?void 0:n.id)||"",sort:1e3,store_uid:s.storeUid,discount_clone_id:null,source_uid:s.sourceUid,promotion:{id:s.promotionUid,sort:1e3,active:1,deleted:!1,is_fabi:1,revision:0,brand_uid:(d==null?void 0:d.id)||"",store_uid:s.storeUid,created_at:s.createdAt||Date.now(),created_by:"system",deleted_at:null,deleted_by:null,extra_data:{},source_uid:s.sourceUid,updated_at:s.updatedAt||Date.now(),updated_by:"system",company_uid:(n==null?void 0:n.id)||"",description:null,promotion_id:s.promotionId,promotion_name:s.promotionName,partner_auto_gen:s.partnerAutoGen},promotion_id:s.promotionId,partner_auto_gen:s.partnerAutoGen,store_name:s.storeName,source:{id:s.source.id,sort:1e3,is_fb:0,active:s.source.active,deleted:!1,is_fabi:1,revision:null,brand_uid:(d==null?void 0:d.id)||"",source_id:s.source.sourceId,store_uid:s.storeUid,created_at:s.createdAt||Date.now(),created_by:"system",deleted_at:null,deleted_by:null,extra_data:{},updated_at:s.updatedAt||Date.now(),updated_by:"system",company_uid:(n==null?void 0:n.id)||"",description:null,source_name:s.source.sourceName,source_type:s.source.sourceType,partner_config:1}};k.mutate(K)},F=()=>{x({to:"/sale/discount/regular/detail"})},G=s=>{N(s)};return e.jsxs(e.Fragment,{children:[e.jsx(Z,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(te,{}),e.jsx(se,{}),e.jsx(ee,{})]})}),e.jsx(B,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(xe,{selectedStoreId:l,onStoreChange:j,selectedPromotion:r,onPromotionChange:p,selectedStatus:o,onStatusChange:f,selectedExpiry:m,onExpiryChange:a,stores:D,promotions:T,onCopyModalOpen:()=>h(!0),onCreateDiscount:F}),e.jsx(ue,{discounts:U,isLoading:z,onToggleActive:R,currentPage:c,pageSize:20}),!z&&U.length>0&&e.jsx(ge,{currentPage:c,onPageChange:G,hasNextPage:i.length>0})]}),e.jsx(pe,{isOpen:v,onClose:()=>h(!1)})]})})]})}const ct=je;export{ct as component};
