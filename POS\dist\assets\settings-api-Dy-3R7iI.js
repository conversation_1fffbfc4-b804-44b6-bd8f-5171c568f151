import{c as a}from"./crm-api-Bz_HnEz3.js";const n={getCrmSettings:async e=>{try{const r=new URLSearchParams({pos_parent:e}),t=await a.get(`/settings/get-pos-parent?${r.toString()}`);if(!t.data||typeof t.data!="object")throw new Error("Invalid response format from CRM settings API");return t.data}catch(r){throw console.error("Error fetching CRM settings:",r),r}},updateBrandConfig:async(e,r)=>{try{const t=new URLSearchParams({pos_parent:e});return(await a.post(`/settings/update_pos_parent?${t.toString()}`,{json_update:JSON.stringify(r)})).data}catch(t){throw console.error("Error updating brand config:",t),t}},updateSettingConfig:async(e,r)=>{try{const t=new URLSearchParams({pos_parent:e});return(await a.post(`/settings/update-setting-config?${t.toString()}`,r)).data}catch(t){throw console.error("Error updating setting config:",t),t}},updateLoyaltySettings:async(e,r)=>{try{const t=new URLSearchParams({pos_parent:e});return(await a.post(`/settings/update-setting-loyalty?${t.toString()}`,r)).data}catch(t){throw console.error("Error updating loyalty settings:",t),t}},updateCheatConfig:async(e,r)=>{try{const t=new URLSearchParams({pos_parent:e});return(await a.post(`/settings/update-cheat-config?${t.toString()}`,r)).data}catch(t){throw console.error("Error updating cheat config:",t),t}}};export{n as c};
