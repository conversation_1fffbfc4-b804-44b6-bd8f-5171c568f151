import{r as v,u as P,j as e,B as V,a4 as D,h as U}from"./index-D0Grd55b.js";import{g as z}from"./error-utils-rXhAXMyo.js";import"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import{u as Q,a as X,b as q}from"./use-users-ByAke5a8.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import{H as B}from"./header-yApU5MZq.js";import{M as G}from"./main-Czv3HpP4.js";import{P as J}from"./profile-dropdown-dGP8eyih.js";import{S as W,T as Y}from"./search-BEMocVbv.js";import{u as Z}from"./use-roles-FABx1m88.js";import{F as w}from"./filter-dropdown-Dmabn8U0.js";import"./date-range-picker-CruKYeHR.js";import"./form-Bk1C9kLO.js";import{I as ee}from"./input-C-0UnKOB.js";import{I as te}from"./IconFilter-ClC5QT7u.js";import{c as le}from"./createReactComponent-eJgt86Cn.js";import{D as H}from"./data-table-column-header-CWPVxhJt.js";import{B as ae}from"./badge-DoEZpO1Y.js";import{u as se,d as re,b as ne,e as ie,f as K}from"./index-CwC79dMR.js";import{T as oe,a as ce,b as L,c as me,d as de,e as $}from"./table-CLNYB6yq.js";import{u as ue}from"./use-excel-export-DGEuZXJu.js";import"./useQuery-Ck3BpOfq.js";import"./utils-km2FGkQ4.js";import"./useMutation-ATsU-ht7.js";import"./query-keys-3lmd-xp6.js";import"./separator-CiUiq7rT.js";import"./avatar-C7dnn6zI.js";import"./dropdown-menu-BvqrmFsX.js";import"./index-CI2TkimM.js";import"./index-CW7Xpojs.js";import"./index-DxQNaO1C.js";import"./check-TFQPNqMS.js";import"./createLucideIcon-DNzDbUBG.js";import"./search-context-CjM0jrYw.js";import"./command-Bcq7GTcy.js";import"./calendar-5lpy20z0.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-C8IVKkOo.js";import"./search-C4dfJjaw.js";import"./scroll-area-r2ikcXUQ.js";import"./select-DBO-8fSu.js";import"./index-C-UyCxtf.js";import"./IconChevronRight-DnVUSvDn.js";import"./IconSearch-ClUZlEwa.js";import"./popover-CMTiAV3j.js";import"./chevrons-up-down-BCCoC0zx.js";import"./chevron-right-D2H_xC9V.js";import"./react-icons.esm-BOnXQgjl.js";import"./excel-export-Dm0P9LkO.js";import"./exceljs.min-BlLyHhym.js";import"./xlsx-DkH2s96g.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var he=le("outline","file-export","IconFileExport",[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M11.5 21h-4.5a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v5m-5 6h7m-3 -3l3 3l-3 3",key:"svg-1"}]]);function pe({searchTerm:i,onSearchChange:c,onSearchSubmit:l,selectedRole:h,onRoleChange:d,selectedStatus:m,onStatusChange:s,selectedBrand:t="all",onBrandChange:o,selectedCity:r="all",onCityChange:n,selectedStore:p="all",onStoreChange:u,onExportEmployees:S,onInviteEmployee:N,isExporting:y=!1}){const[T,b]=v.useState(!1),{brands:x,cities:j,stores:f}=P(a=>a.auth),{data:M=[]}=Z(),A=M.map(a=>({value:a.role_id,label:a.role_name})),C=[{value:"active",label:"Tài khoản hoạt động"},{value:"inactive",label:"Tài khoản ngừng hoạt động"}],I=(x==null?void 0:x.filter(a=>a.active===1).map(a=>({value:a.id,label:a.brand_name})))||[],R=()=>{if(t==="all")return[];const a=(f==null?void 0:f.filter(g=>g.active===1&&g.brand_uid===t))||[],F=[...new Set(a.map(g=>g.city_uid))];return(j==null?void 0:j.filter(g=>g.active===1&&F.includes(g.id)).map(g=>({value:g.id,label:g.city_name})))||[]},O=()=>r==="all"?[]:(f==null?void 0:f.filter(a=>a.active===1&&a.city_uid===r&&(t==="all"||a.brand_uid===t)).map(a=>({value:a.id,label:a.store_name})))||[],_=a=>{o==null||o(a),n==null||n("all"),u==null||u("all")},k=a=>{n==null||n(a),u==null||u("all")},E=a=>{u==null||u(a)};return e.jsxs("div",{className:"mb-6 space-y-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Danh sách nhân viên"}),e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(ee,{placeholder:"Tìm kiếm email, SĐT...",value:i,onChange:a=>c(a.target.value),onKeyDown:a=>{a.key==="Enter"&&(a.preventDefault(),l==null||l())},className:"max-w-sm"}),e.jsx(w,{value:h,onValueChange:d,options:A,placeholder:"Tất cả chức vụ",allOptionLabel:"Tất cả chức vụ",className:"w-[180px]"}),e.jsxs(V,{variant:"outline",size:"sm",onClick:()=>b(!T),className:"flex items-center gap-2",children:[e.jsx(te,{className:"h-4 w-4"}),"Nâng cao"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(V,{variant:"outline",size:"sm",onClick:S,disabled:y,children:[e.jsx(he,{className:"h-4 w-4"}),y?"Đang xuất...":"Xuất DS nhân viên"]}),e.jsx(V,{size:"sm",onClick:N,children:"Tạo nhân viên"})]})]}),T&&e.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:[e.jsx(w,{value:m,onValueChange:s,options:C,placeholder:"Tất cả tài khoản",allOptionLabel:"Tất cả tài khoản",className:"w-full"}),e.jsx(w,{value:t,onValueChange:_,options:I,placeholder:"Tất cả thương hiệu",allOptionLabel:"Tất cả thương hiệu",className:"w-full"}),e.jsx(w,{value:r,onValueChange:t==="all"?()=>{}:k,options:t==="all"?[]:R(),placeholder:t==="all"?"Chọn thương hiệu trước":"Chọn thành phố",allOptionLabel:"Tất cả thành phố",className:`w-full ${t==="all"?"cursor-not-allowed opacity-50":""}`}),e.jsx(w,{value:p,onValueChange:r==="all"?()=>{}:E,options:r==="all"?[]:O(),placeholder:r==="all"?"Chọn thành phố trước":"Chọn cửa hàng",allOptionLabel:"Tất cả cửa hàng",className:`w-full ${r==="all"?"cursor-not-allowed opacity-50":""}`})]})]})}const xe=(i,c)=>[{id:"index",header:"#",cell:({row:l})=>e.jsx("div",{className:"w-8",children:l.index+1}),enableSorting:!1,enableHiding:!1},{accessorKey:"full_name",header:"Tên",cell:({row:l})=>e.jsx("div",{className:"font-medium",children:l.getValue("full_name")})},{accessorKey:"role_name",header:"Chức vụ",cell:({row:l})=>e.jsx("div",{children:l.getValue("role_name")})},{accessorKey:"email",header:"Email",cell:({row:l})=>e.jsx("div",{className:"text-muted-foreground",children:l.getValue("email")})},{accessorKey:"phone",header:"Số điện thoại",cell:({row:l})=>e.jsx("div",{children:l.getValue("phone")})},{id:"stores",header:({column:l})=>e.jsx(H,{column:l,title:"Cửa hàng"}),cell:({row:l})=>{const d=l.original.stores||{},m=[];if(Object.values(d).forEach(t=>{typeof t=="object"&&t!==null&&Object.values(t).forEach(o=>{Array.isArray(o)&&m.push(...o)})}),m.length===0)return e.jsx("div",{className:"text-muted-foreground"});const s=m.map(t=>{const o=c==null?void 0:c.find(r=>r.id===t);return(o==null?void 0:o.store_name)||`Store ${t.slice(0,8)}...`}).filter(Boolean);return s.length===0?e.jsx("div",{className:"text-muted-foreground",children:"-"}):s.length===1?e.jsx("div",{className:"text-sm",children:s[0]}):e.jsxs("div",{className:"text-sm",children:[s[0],e.jsxs("span",{className:"text-muted-foreground ml-1 text-xs",children:["(+",s.length-1,")"]})]})},enableSorting:!1},{accessorKey:"active",header:({column:l})=>e.jsx(H,{column:l,title:"Thao tác"}),cell:({row:l})=>{const h=l.original,d=l.getValue("active")===1;return e.jsx("div",{onClick:m=>{m.stopPropagation(),i==null||i(h)},className:"cursor-pointer",children:e.jsx(ae,{variant:d?"default":"destructive",className:d?"bg-green-500 hover:bg-green-600":"bg-red-500 hover:bg-red-600",children:d?"Active":"Deactive"})})},enableSorting:!1}];function ve({columns:i,data:c,onRowClick:l}){var o;const[h,d]=v.useState([]),[m,s]=v.useState([]),t=se({data:c,columns:i,getCoreRowModel:ie(),getSortedRowModel:ne(),getFilteredRowModel:re(),onSortingChange:d,onColumnFiltersChange:s,state:{sorting:h,columnFilters:m}});return e.jsx("div",{className:"rounded-md border",children:e.jsxs(oe,{children:[e.jsx(ce,{children:t.getHeaderGroups().map(r=>e.jsx(L,{children:r.headers.map(n=>e.jsx(me,{children:n.isPlaceholder?null:K(n.column.columnDef.header,n.getContext())},n.id))},r.id))}),e.jsx(de,{children:(o=t.getRowModel().rows)!=null&&o.length?t.getRowModel().rows.map(r=>e.jsx(L,{"data-state":r.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:()=>l==null?void 0:l(r.original.id),children:r.getVisibleCells().map(n=>e.jsx($,{children:K(n.column.columnDef.cell,n.getContext())},n.id))},r.id)):e.jsx(L,{children:e.jsx($,{colSpan:i.length,className:"h-24 text-center",children:"Không có dữ liệu nhân viên."})})})]})})}function fe({employees:i}={}){const[c,l]=v.useState(""),[h,d]=v.useState(""),[m,s]=v.useState("all"),[t,o]=v.useState("all"),[r,n]=v.useState("all"),[p,u]=v.useState("all"),[S,N]=v.useState("all"),y=v.useMemo(()=>{if(!i)return[];let x=i;if(m!=="all"&&(x=x.filter(j=>j.role_id===m)),t!=="all"){const j=t==="active";x=x.filter(f=>f.active===(j?1:0))}return x},[i,m,t]);return{searchTerm:c,searchQuery:h,handleSearchChange:x=>{d(x)},handleSearchSubmit:()=>{l(h)},selectedRole:m,setSelectedRole:s,selectedStatus:t,setSelectedStatus:o,selectedBrand:r,setSelectedBrand:n,selectedCity:p,setSelectedCity:u,selectedStore:S,setSelectedStore:N,filteredEmployees:y}}function ge({employees:i,stores:c}){const l=v.useMemo(()=>i.map(s=>{const t=s.stores||{},o=[];Object.values(t).forEach(n=>{typeof n=="object"&&n!==null&&Object.values(n).forEach(p=>{Array.isArray(p)&&o.push(...p)})});const r=o.map(n=>{const p=c==null?void 0:c.find(u=>u.id===n);return(p==null?void 0:p.store_name)||`Store ${n.slice(0,8)}...`}).filter(Boolean);return{id:s.id,full_name:s.full_name,role_name:s.role_name,email:s.email,phone:s.phone||"",status:s.active===1?"Active":"Inactive",stores:r.length>0?r.join(", "):""}}),[i,c]),{exportData:h,isExporting:d}=ue({data:l,filename:`danh-sach-nhan-vien-${new Date().toISOString().split("T")[0]}.xlsx`,sheetName:"Danh sách nhân viên",columnMapping:{id:"ID",full_name:"Tên",role_name:"Chức vụ",email:"Email",phone:"Số điện thoại",status:"Trạng thái",stores:"Cửa hàng"},onExportStart:()=>{D.info("Đang xuất danh sách nhân viên...")},onExportComplete:()=>{D.success("Xuất danh sách nhân viên thành công!")},onExportError:s=>{console.error("Export error:",s),D.error("Có lỗi xảy ra khi xuất danh sách nhân viên")}});return{handleExportEmployees:()=>{if(i.length===0){D.warning("Không có dữ liệu nhân viên để xuất");return}h()},isExporting:d}}function je(){const i=U(),c=Q(),l=X();return{handleInviteEmployee:()=>{i({to:"/employee/detail"})},handleEmployeeClick:s=>{i({to:"/employee/detail/$userId",params:{userId:s}})},handleToggleEmployeeStatus:async s=>{try{s.active===1?await l.mutateAsync(s.id):await c.mutateAsync(s.id)}catch(t){console.error("Toggle user status error:",t)}}}}function Ee(){const{company:i,stores:c}=P(E=>E.auth),l=fe(),{searchTerm:h,searchQuery:d,handleSearchChange:m,handleSearchSubmit:s,selectedRole:t,setSelectedRole:o,selectedStatus:r,setSelectedStatus:n,selectedBrand:p,setSelectedBrand:u,selectedCity:S,setSelectedCity:N,selectedStore:y,setSelectedStore:T}=l,{data:b,isLoading:x,error:j}=q({company_uid:i==null?void 0:i.id,search:h||void 0,brand_uid:p!=="all"?p:void 0,city_uid:S!=="all"?S:void 0,store_uid:y!=="all"?y:void 0}),f=v.useMemo(()=>{if(!b)return[];let E=b;if(t!=="all"&&(E=E.filter(a=>a.role_id===t)),r!=="all"){const a=r==="active";E=E.filter(F=>F.active===(a?1:0))}return E},[b,t,r]),{handleExportEmployees:M,isExporting:A}=ge({employees:f,stores:c}),{handleInviteEmployee:C,handleEmployeeClick:I,handleToggleEmployeeStatus:R}=je(),O=x,_=j,k=xe(R,c);return e.jsxs(e.Fragment,{children:[e.jsx(B,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(W,{}),e.jsx(Y,{}),e.jsx(J,{})]})}),e.jsx(G,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(pe,{searchTerm:d,onSearchChange:m,onSearchSubmit:s,selectedRole:t,onRoleChange:o,selectedStatus:r,onStatusChange:n,selectedBrand:p,onBrandChange:u,selectedCity:S,onCityChange:N,selectedStore:y,onStoreChange:T,onExportEmployees:M,onInviteEmployee:C,isExporting:A}),_?e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:z(_)})}):O?e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:"Đang tải dữ liệu nhân viên..."})}):e.jsx(ve,{columns:k,data:f,onRowClick:I})]})})]})}const Et=Ee;export{Et as component};
