import { useState, useEffect } from 'react'

import { Trash2 } from 'lucide-react'
import { toast } from 'sonner'

import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { useBulkUpdatePriceBySource } from '../../../hooks'

export interface PriceBySourcePreviewItem {
  item_uid: string
  item_id: string
  item_name: string
  item_type_name: string // Nhóm món
  ots_price: number // Giá gốc
  ots_tax: number // Vat (%)
  // Source prices
  'ZALO [10000045]'?: number | string
  'FACEBOOK [10000049]'?: number | string
  'SO [10000134]'?: number | string
  'CRM [10000162]'?: number | string
  'VNPAY [10000165]'?: number | string
  'GOJEK (GOVIET) [10000168]'?: number | string
  'ShopeeFood [10000169]'?: number | string
  'MANG VỀ [10000171]'?: number | string
  'TẠI CHỖ [10000172]'?: number | string
  'CALL CENTER [10000176]'?: number | string
  'O2O [10000216]'?: number | string
  'BEFOOD [10000253]'?: number | string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

interface PriceBySourcePreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: PriceBySourcePreviewItem[]
  originalItems: any[]
  sources: any[]
  storeName?: string
}

export function PriceBySourcePreviewDialog({
  open,
  onOpenChange,
  data,
  originalItems,
  sources
}: PriceBySourcePreviewDialogProps) {
  const [previewData, setPreviewData] = useState<PriceBySourcePreviewItem[]>(data)
  const { bulkUpdatePriceBySource, isUpdating } = useBulkUpdatePriceBySource()

  useEffect(() => {
    setPreviewData(data)
  }, [data])

  const handleRemoveItem = (index: number) => {
    setPreviewData(prev => prev.filter((_, i) => i !== index))
  }

  const handleConfirm = async () => {
    if (previewData.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return
    }

    try {
      await bulkUpdatePriceBySource({
        previewItems: previewData,
        originalItems: originalItems,
        sources: sources
      }, {
        onSuccess: () => {
          onOpenChange(false)
        },
        onError: error => {
          console.error('Error saving price by source configuration:', error)
        }
      })
    } catch (error) {
      console.error('Error saving price by source configuration:', error)
      // Error handling is already done in the hook
    }
  }

  const columns = [
    { key: 'item_uid', label: 'item_uid', width: '280px' },
    { key: 'item_id', label: 'item_id', width: '120px' },
    { key: 'item_name', label: 'item_name', width: '200px' },
    { key: 'ots_price', label: 'Giá gốc', width: '100px' },
    { key: 'ots_tax', label: 'Vat', width: '80px' },
    { key: 'ZALO [10000045]', label: 'ZALO [10000045]', width: '120px' },
    { key: 'FACEBOOK [10000049]', label: 'FACEBOOK [10000049]', width: '140px' },
    { key: 'SO [10000134]', label: 'SO [10000134]', width: '120px' },
    { key: 'CRM [10000162]', label: 'CRM [10000162]', width: '120px' },
    { key: 'VNPAY [10000165]', label: 'VNPAY [10000165]', width: '120px' },
    { key: 'GOJEK (GOVIET) [10000168]', label: 'GOJEK (GOVIET) [10000168]', width: '180px' },
    { key: 'ShopeeFood [10000169]', label: 'ShopeeFood [10000169]', width: '160px' },
    { key: 'MANG VỀ [10000171]', label: 'MANG VỀ [10000171]', width: '140px' },
    { key: 'TẠI CHỖ [10000172]', label: 'TẠI CHỖ [10000172]', width: '140px' },
    { key: 'CALL CENTER [10000176]', label: 'CALL CENTER [10000176]', width: '160px' },
    { key: 'O2O [10000216]', label: 'O2O [10000216]', width: '120px' },
    { key: 'BEFOOD [10000253]', label: 'BEFOOD [10000253]', width: '140px' }
  ]

  const formatPrice = (value: number | string | undefined): string => {
    if (value === undefined || value === null || value === '') return ''
    const numValue = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(numValue)) return ''
    return numValue.toLocaleString('vi-VN')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] max-w-7xl sm:max-w-4xl'>
        <DialogHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <DialogTitle className='text-xl font-semibold'>Thêm cấu hình giá món theo nguồn đơn</DialogTitle>
        </DialogHeader>

        <div className='flex h-full flex-col space-y-4 overflow-hidden'>
          <div className='flex-shrink-0 text-sm text-gray-600'>
            Tổng số món: <span className='font-medium'>{previewData.length}</span>
          </div>

          <div className='min-h-0 flex-1'>
            <ScrollArea className='h-[60vh] w-full rounded-md border'>
              <Table>
                <TableHeader className='sticky top-0 z-10 bg-white'>
                  <TableRow>
                    <TableHead className='w-12'></TableHead>
                    {columns.map(column => (
                      <TableHead key={column.key} style={{ width: column.width }}>
                        {column.label}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {previewData.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Button
                          variant='ghost'
                          size='icon'
                          onClick={() => handleRemoveItem(index)}
                          className='h-8 w-8 text-red-500 hover:text-red-700'
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </TableCell>
                      {columns.map(column => (
                        <TableCell key={column.key} style={{ width: column.width }}>
                          {column.key === 'item_uid' && (
                            <span className='font-mono text-xs text-gray-600'>{item[column.key]}</span>
                          )}
                          {column.key === 'item_id' && <span className='font-mono text-sm'>{item[column.key]}</span>}
                          {column.key === 'item_name' && <span className='font-medium'>{item[column.key]}</span>}
                          {column.key === 'item_type_name' && <span className='text-sm'>{item[column.key]}</span>}
                          {column.key === 'ots_price' && (
                            <span className='text-right font-medium'>{formatPrice(item[column.key])}</span>
                          )}
                          {column.key === 'ots_tax' && <span className='text-center'>{item[column.key]}</span>}
                          {column.key.includes('[') && (
                            // Source price columns
                            <span className='text-right font-medium text-blue-600'>
                              {item[column.key] ? `${formatPrice(item[column.key])}` : ''}
                            </span>
                          )}
                          {!column.key.includes('[') &&
                            column.key !== 'item_uid' &&
                            column.key !== 'item_id' &&
                            column.key !== 'item_name' &&
                            column.key !== 'item_type_name' &&
                            column.key !== 'ots_price' &&
                            column.key !== 'ots_tax' && <span>{item[column.key] || ''}</span>}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <ScrollBar orientation='horizontal' />
              <ScrollBar orientation='vertical' />
            </ScrollArea>
          </div>

          <div className='flex flex-shrink-0 items-center justify-between border-t pt-4'>
            <Button variant='outline' onClick={() => onOpenChange(false)}>
              Đóng
            </Button>
            <Button onClick={handleConfirm} disabled={isUpdating || previewData.length === 0}>
              {isUpdating ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
