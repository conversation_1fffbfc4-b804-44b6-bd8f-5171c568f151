import{j as e,B as w,r as l,z as a,h as G,a4 as T}from"./index-DZ2N7iEN.js";import{g as J}from"./error-utils-Cfeehata.js";import"./pos-api-PZMeNc3U.js";import{a as W,b as X}from"./use-devices-CM1TM1BB.js";import{u as Y}from"./use-stores-DvOEKdtr.js";import"./vietqr-api-y35earyI.js";import"./user-B3TbfMPQ.js";import"./crm-api-DyqsDFNF.js";import{H as Z}from"./header-DUOMiURq.js";import{M as ee}from"./main-DEy6aM-s.js";import{P as te}from"./profile-dropdown-CYtncOP3.js";import{S as se,T as ae}from"./search-BE7sNBUd.js";import{v as ie}from"./date-range-picker-extpnOqj.js";import{L as re}from"./form-CUvXDg29.js";import{I as A}from"./input-DpObCffE.js";import{S as M,a as O,b as P,c as L,d as h}from"./select-B60YVMMU.js";import{S as oe}from"./status-badge-BgxZUcyg.js";import{I as ne}from"./IconCopy-C-PT5cPn.js";import{f as le}from"./isSameMonth-C8JQo-AN.js";import{P as ce}from"./modal-C3wc7cQn.js";import{u as de,g as me,a as pe,b as he,c as ue,d as ge,e as xe,f as K}from"./index-AHlYCYkx.js";import{S as ve,a as Se}from"./scroll-area-CoEnZUVR.js";import{T as je,a as ye,b as R,c as fe,d as be,e as E}from"./table-DQgmazAN.js";import{D as De}from"./data-table-pagination-DmfeHsfg.js";import"./useQuery-CCx3k6lE.js";import"./utils-km2FGkQ4.js";import"./useMutation-zADCXE7W.js";import"./query-keys-3lmd-xp6.js";import"./stores-api-EIS-KrB6.js";import"./separator-Bnr4MN7f.js";import"./avatar-C7msROEs.js";import"./dropdown-menu-Ci4yPenc.js";import"./index-Csh0LmDQ.js";import"./index-CC24pdSB.js";import"./index-DeeqTHK3.js";import"./check-CP51yA4o.js";import"./createLucideIcon-2r9cCEY3.js";import"./search-context-bUKMT4ET.js";import"./command-By9h7q-C.js";import"./calendar-CoHe9sRq.js";import"./dialog-DDb7K8l1.js";import"./search-Cr9zvKK2.js";import"./createReactComponent-CQMxkbLi.js";import"./IconChevronRight-Cvwf7k8w.js";import"./IconSearch-Do03nWQy.js";import"./chevron-right-D-SI6PAy.js";import"./react-icons.esm-Ck8h5xF8.js";import"./popover-BK8t3srL.js";import"./index-DIwSqRjm.js";import"./badge-5IdLxoVq.js";const Ne=t=>({POS:"POS",POS_MINI:"POS_MINI",PDA:"PDA",KDS:"KDS",KDS_ORDER_CONTROL:"KDS ORDER CONTROL",KDS_MAKER:"KDS MAKER",SELF_ORDER:"SELF ORDER"})[t]||t,Re=t=>({2:"Máy trạm",1:"Máy chủ",0:"None"})[t]||"None",Ce=[{accessorKey:"id",header:"#",cell:({row:t})=>{const s=t.index+1;return e.jsx("div",{className:"w-[50px] font-medium",children:s})},enableSorting:!1},{accessorKey:"name",header:"Tên thiết bị",cell:({row:t})=>{const s=t.original;return e.jsx("span",{className:"font-medium",children:s.name})}},{accessorKey:"type",header:"Loại thiết bị",cell:({row:t})=>{const s=t.getValue("type"),c=t.original;return e.jsxs("span",{className:"font-medium",children:[Ne(s)," -"," ",e.jsx("span",{className:"text-gray-500",children:Re(c.device_type_local||0)})]})}},{accessorKey:"version",header:"Phiên bản",cell:({row:t})=>e.jsx("span",{className:"text-sm font-medium",children:t.getValue("version")})},{accessorKey:"storeName",header:"Cửa hàng",cell:({row:t})=>{const s=t.original;return e.jsx("span",{className:"font-medium",children:s.storeName})}},{accessorKey:"lastUpdate",header:"Thời gian cập nhật",cell:({row:t})=>{const s=t.getValue("lastUpdate");return e.jsx("span",{className:"text-sm font-medium",children:le(s,"dd/MM/yyyy HH:mm",{locale:ie})})}},{id:"copy",header:"Sao chép thiết bị",cell:({row:t,table:s})=>{const c=t.original,o=s.options.meta;return e.jsxs(w,{variant:"ghost",size:"sm",onClick:d=>{var n;d.stopPropagation(),(n=o==null?void 0:o.onCopyDevice)==null||n.call(o,c)},className:"h-8 w-8 p-0",children:[e.jsx(ne,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép thiết bị ",c.name]})]})}},{accessorKey:"isActive",header:"",cell:({row:t})=>{const s=t.getValue("isActive");return e.jsx(oe,{isActive:s,activeText:"Active",inactiveText:"Deactive"})}}],Te=t=>({POS:"POS",POS_MINI:"POS MINI",PDA:"PDA"})[t]||t,Me=({device:t,open:s,onOpenChange:c,onCopy:o})=>{const[d,n]=l.useState(""),[S,g]=l.useState(!1);l.useEffect(()=>{t&&s&&n("")},[t,s]);const j=async()=>{if(d.trim()){g(!0);try{await o(d.trim()),c(!1),n("")}finally{g(!1)}}},y=()=>{c(!1),n("")};if(!t)return null;const v=`Sao chép thiết bị ${Te(t.type)} ${t.name}`;return e.jsx(ce,{title:v,open:s,onOpenChange:c,onCancel:y,onConfirm:j,isLoading:S,confirmDisabled:!d.trim(),children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(re,{htmlFor:"device-name",className:"min-w-[100px] text-sm font-medium",children:"Tên thiết bị"}),e.jsx(A,{id:"device-name",value:d,onChange:m=>n(m.target.value),className:"flex-1",autoFocus:!0,required:!0,placeholder:"Nhập tên thiết bị"})]})})};function Oe({columns:t,data:s,onCopyDevice:c,onRowClick:o}){var b;const[d,n]=l.useState({}),[S,g]=l.useState([]),[j,y]=l.useState([]),v=(p,r)=>{const x=r.target;x.closest('input[type="checkbox"]')||x.closest("button")||x.closest('[role="button"]')||x.closest(".badge")||x.tagName==="BUTTON"||o==null||o(p)},m=de({data:s,columns:t,state:{sorting:j,columnVisibility:d,columnFilters:S},onSortingChange:y,onColumnFiltersChange:g,onColumnVisibilityChange:n,getCoreRowModel:xe(),getFilteredRowModel:ge(),getPaginationRowModel:ue(),getSortedRowModel:he(),getFacetedRowModel:pe(),getFacetedUniqueValues:me(),meta:{onCopyDevice:c}});return e.jsxs("div",{className:"space-y-4",children:[e.jsxs(ve,{className:"rounded-md border",children:[e.jsxs(je,{className:"relative",children:[e.jsx(ye,{children:m.getHeaderGroups().map(p=>e.jsx(R,{children:p.headers.map(r=>e.jsx(fe,{colSpan:r.colSpan,children:r.isPlaceholder?null:K(r.column.columnDef.header,r.getContext())},r.id))},p.id))}),e.jsx(be,{children:(b=m.getRowModel().rows)!=null&&b.length?m.getRowModel().rows.map(p=>e.jsx(R,{className:"hover:bg-muted/50 cursor-pointer",onClick:r=>v(p.original,r),children:p.getVisibleCells().map(r=>e.jsx(E,{children:K(r.column.columnDef.cell,r.getContext())},r.id))},p.id)):e.jsx(R,{children:e.jsx(E,{colSpan:t.length,className:"h-24 text-center",children:"Không có thiết bị nào."})})})]}),e.jsx(Se,{orientation:"horizontal"})]}),e.jsx(De,{table:m})]})}const Pe=a.union([a.literal("active"),a.literal("inactive"),a.literal("maintenance"),a.literal("error")]),Le=a.union([a.literal("POS"),a.literal("POS_MINI"),a.literal("PDA"),a.literal("KDS"),a.literal("KDS_ORDER_CONTROL"),a.literal("KDS_MAKER"),a.literal("SELF_ORDER")]),Ke=a.object({id:a.string(),name:a.string(),type:Le,version:a.string(),storeId:a.string(),storeName:a.string(),status:Pe,lastUpdate:a.coerce.date(),serialNumber:a.string(),manufacturer:a.string(),model:a.string(),ipAddress:a.string().optional(),macAddress:a.string().optional(),createdAt:a.coerce.date(),updatedAt:a.coerce.date(),isActive:a.boolean(),device_code:a.string().optional(),device_type_local:a.number().optional()});a.array(Ke);function Ee(){var C;const t=G(),[s,c]=l.useState(""),[o,d]=l.useState(""),[n,S]=l.useState("all"),[g,j]=l.useState("all"),[y,v]=l.useState(!1),[m,b]=l.useState(null),[p,r]=l.useState(1),x=W(),{data:u,isLoading:I,error:_}=Y(),F=(u==null?void 0:u.filter(i=>i.isActive).map(i=>i.id))||[],V=!s||s.length>=3,{data:H,isLoading:U,error:k}=X({searchTerm:V&&s||void 0,listStoreUid:n!=="all"?n:F.join(","),deviceType:g!=="all"?g:void 0,page:p,storesData:u==null?void 0:u.map(i=>({id:i.id,store_name:i.name}))}),D=I||U,f=_||k,B=i=>{b(i),v(!0)},$=async i=>{if(m)try{await x.mutateAsync({deviceId:m.id,newDeviceName:i}),T.success(`Thiết bị "${i}" đã được sao chép thành công!`)}catch(N){const Q=N instanceof Error?N.message:"Đã xảy ra lỗi khi sao chép thiết bị";throw T.error(Q),N}},z=()=>{t({to:"/devices/new"})},q=i=>{t({to:"/devices/detail/$id",params:{id:i.id},search:{store_uid:i.storeId}})};return e.jsxs(e.Fragment,{children:[e.jsx(Z,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(se,{}),e.jsx(ae,{}),e.jsx(te,{})]})}),e.jsx(ee,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Danh sách thiết bị"}),e.jsx(A,{placeholder:"Tìm kiếm thiết bị...",className:"w-64",value:o,onChange:i=>d(i.target.value),onKeyDown:i=>{i.key==="Enter"&&(i.preventDefault(),c(o),r(1))}}),e.jsxs(M,{value:n,onValueChange:i=>{S(i),r(1)},children:[e.jsx(O,{className:"w-48",children:e.jsx(P,{placeholder:"Tất cả các điểm"})}),e.jsxs(L,{children:[e.jsx(h,{value:"all",children:"Tất cả các điểm"}),(C=u==null?void 0:u.filter(i=>i.isActive))==null?void 0:C.map(i=>e.jsx(h,{value:i.id,children:i.name},i.id))]})]}),e.jsxs(M,{value:g,onValueChange:i=>{j(i),r(1)},children:[e.jsx(O,{className:"w-52",children:e.jsx(P,{placeholder:"Tất cả loại thiết bị"})}),e.jsxs(L,{children:[e.jsx(h,{value:"all",children:"Tất cả loại thiết bị"}),e.jsx(h,{value:"POS",children:"POS"}),e.jsx(h,{value:"POS_MINI",children:"POS MINI"}),e.jsx(h,{value:"PDA",children:"PDA"}),e.jsx(h,{value:"KDS",children:"KDS"}),e.jsx(h,{value:"KDS_ORDER_CONTROL",children:"KDS ORDER CONTROL"}),e.jsx(h,{value:"KDS_MAKER",children:"KDS MAKER"}),e.jsx(h,{value:"SELF_ORDER",children:"SELF ORDER"})]})]})]}),e.jsx(w,{size:"sm",onClick:z,children:"Tạo thiết bị"})]}),f&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:J(f)})}),!f&&D&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:"Đang tải dữ liệu thiết bị..."})}),!f&&!D&&s&&s.length>0&&s.length<3&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-amber-600",children:'Truyền thiếu dữ liệu : "search" length must be at least 3 characters long'})}),!f&&!D&&(!s||s.length===0||s.length>=3)&&e.jsx(Oe,{columns:Ce,data:H||[],onCopyDevice:B,onRowClick:q}),e.jsx(Me,{device:m,open:y,onOpenChange:v,onCopy:$})]})})]})}const Pt=Ee;export{Pt as component};
