import{r as j,j as d,R as Z,u as G,a3 as q,a4 as C,B as U,l as rt,z as V}from"./index-D0Grd55b.js";import{u as ct}from"./use-dialog-state-Bi2BGkq6.js";import{E as lt}from"./exceljs.min-BlLyHhym.js";import{u as Q}from"./useQuery-Ck3BpOfq.js";import{Q as S}from"./query-keys-3lmd-xp6.js";import{b as k}from"./pos-api-B09qRspF.js";import"./vietqr-api-C42F6d9k.js";import{u as dt}from"./use-item-types-CuyGAtwR.js";import{u as ut}from"./use-item-classes-s7fFgXey.js";import{u as mt}from"./use-units-ByB1meVo.js";import"./user-CN3Oxfhq.js";import"./crm-api-Bz_HnEz3.js";import{C as W}from"./checkbox-C_eqgJmW.js";import{I as ht}from"./input-C-0UnKOB.js";import{P as gt}from"./modal-hDollbt0.js";import"./date-range-picker-CruKYeHR.js";import{u as _t,F as yt,a as pt,b as ft,c as bt,d as Ft,e as Ct}from"./form-Bk1C9kLO.js";import{C as J}from"./chevron-right-D2H_xC9V.js";import{C as X}from"./select-DBO-8fSu.js";import{s as It}from"./zod-G2vIgQkk.js";import{D as wt,a as xt,b as Tt,c as vt,e as Mt,f as kt}from"./dialog-C8IVKkOo.js";import{C as St}from"./combobox-NkstXop_.js";import{u as z}from"./useMutation-ATsU-ht7.js";const tt=Z.createContext(null);function ye({children:t}){const[e,n]=ct(null),[i,a]=j.useState(null);return d.jsx(tt,{value:{open:e,setOpen:n,currentRow:i,setCurrentRow:a},children:t})}const pe=()=>{const t=Z.useContext(tt);if(!t)throw new Error("useItemsInCity has to be used within <ItemsInCityContext>");return t},Y=()=>{const t=new lt.Workbook;return t.creator="POS System",t.lastModifiedBy="POS System",t.created=new Date,t.modified=new Date,t},Nt=()=>["ID","Mã món","Thành phố","Tên","Giá","Trạng thái","Mã barcode","Món ăn kèm","Không cập nhật số lượng món ăn kèm","Đơn vị","Nhóm","Tên nhóm","Loại món","Tên loại","Mô tả","SKU","VAT (%)","Thời gian chế biến (phút)","Cho phép sửa giá khi bán","Cấu hình món ảo","Cấu hình món dịch vụ","Cấu hình món ăn là vé buffet","Giờ","Ngày","Thứ tự","Hình ảnh","Công thức inQR cho máy pha trà"],et=t=>{const e=Nt(),n=t.addWorksheet("Menu");return n.addRow(e).eachCell(s=>{s.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},s.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},s.alignment={horizontal:"center",vertical:"middle"},s.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),[36,15,12,25,12,12,15,15,35,12,15,12,15,12,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((s,p)=>{n.getColumn(p+1).width=s}),n},Rt=()=>["ID","Mã món","Thành phố","Tên","Giá (Mặc định 0)","Trạng thái (Mặc định 1)","Mã barcode (Tối đa 13)","Món ăn kèm (Mặc định 0)","Không cập nhật số lượng món ăn kèm (Mặc định 1)","Đơn vị (Mặc định MON)","Nhóm (Mặc định LOẠI KHÁC)","Tên nhóm","Loại món (Mặc định rỗng)","Tên loại","Mô tả","SKU (Tối đa 50)","VAT (%) (Mặc định 0)","Thời gian chế biến (phút) (Mặc định 0)","Cho phép sửa giá khi bán (Mặc định 0)","Cấu hình món ảo (Mặc định 0)","Cấu hình món dịch vụ (Mặc định 0)","Cấu hình món ăn là vé buffet (Mặc định 0)","Giờ (Mặc định 0)","Ngày (Mặc định 0)","Thứ tự (Mặc định 0)","Hình ảnh","Công thức inQR cho máy pha trà"],Et=()=>["Thành phố","Tên","Giá","Mã món","Mã barcode","Món ăn kèm","Không cập nhật số lượng món ăn kèm","Nhóm","Loại món","Mô tả","SKU","Đơn vị","VAT (%)","Thời gian chế biến (phút)","Cho phép sửa giá khi bán","Cấu hình món ảo","Cấu hình món dịch vụ","Cấu hình món ăn là vé buffet","Ngày","Giờ","Hình ảnh"],At=()=>["Thành phố","Tên","Giá (Mặc định 0)","Mã món","Mã barcode (Tối đa 13)","Món ăn kèm (Mặc định 0)","Không cập nhật số lượng món ăn kèm (Mặc định 0)","Nhóm (Mặc định LOẠI KHÁC)","Loại món (Mặc định rỗng)","Mô tả","SKU (tối đa 50)","Đơn vị (Mặc định MON)","VAT (%) (Mặc định 0)","Thời gian chế biến (phút) (Mặc định 0)","Cho phép sửa giá khi bán (Mặc định 0)","Cấu hình món ảo (Mặc định 0)","Cấu hình món dịch vụ (Mặc định 0)","Cấu hình món ăn là vé buffet (Mặc định 0)","Ngày (Mặc định 0)","Giờ (Mặc định 0)","Hình ảnh"],jt=()=>[["d9692391-3f3f-4754-9416-878d2d8b52ce","ITEM-3279","Hồ Chí Minh","Cà Phê Sữa (L)",0,1,"",0,1,"AM","ITEM_TYPE_OTHER","Uncategory","DA","Đồ ăn","không","",0,10,14,0,0,1,2064384,224,0,"https://image.foodbook.vn/images/20250829/1756431019051-anh-test-may-in-mau-chat-luong-hinh-2.jpg",""],["8119567b-f80e-43ac-8c85-012dd8f56b18","KHOAI","Hồ Chí Minh","pomato",0,1,"",1,1,"TRAI","MAK","MÓN ĂN KÈM","MA","manh","","",0,5,14,1,0,1,6355002,254,0,"https://image.foodbook.vn/images/20250829/1756450169874-vo_tri_2.jpg",""]],Dt=()=>[["Hà Nội","Món 1",1e5,"TRA_SUA_SO_1_YQ4P","",0,0,"MONN_KEMVK84","ITEM_CLASS-9ZKE","Món đắt","item-121","COC",8,12,0,0,0,0,124,12,"https://img.foodbook.vn/images/20191202/1575271193305-image.jpg"],["Đà Nẵng","Món 2",99999,"ITEM_KFHE","1281-II",1,1,"ITEMTYPE_BC56","ITEM_CLASS-12OP","Món ăn ngon","ITEM111","LANG",12,20,1,1,1,1,0,0,"https://img.foodbook.vn/images/20191107/1573097661136-image.jpg"]],nt=()=>[["Chủ nhật","2"],["Thứ 2","4"],["Thứ 3","8"],["Thứ 4","16"],["Thứ 5","32"],["Thứ 6","64"],["Thứ 7","128"],["Ví dụ: CN, T2, T5 = 2 + 4 + 32","38"]],it=()=>[["0h","1"],["1h","2"],["2h","4"],["3h","8"],["4h","16"],["5h","32"],["6h","64"],["7h","128"],["8h","256"],["9h","512"],["10h","1024"],["11h","2048"],["12h","4096"],["13h","8192"],["14h","16384"],["15h","32768"],["16h","65536"],["17h","131072"],["18h","262144"],["19h","524288"],["20h","1048576"],["21h","2097152"],["22h","4194304"],["23h","8388608"],["Ví dụ: 0h, 1h, 3h = 1 + 2 + 8","11"]],ot=t=>{const e=t.filter(n=>n.active===1).map(n=>[n.item_type_id,n.item_type_name]);return e.length>0?e:[["Không có dữ liệu",""]]},st=t=>{const e=t.filter(n=>n.active===1).map(n=>[n.item_class_id,n.item_class_name]);return e.length>0?e:[["Không có dữ liệu",""]]},at=t=>{if(!t||t.length===0)return[["Không có dữ liệu",""]];const e=t.map(n=>[n.unit_id,n.unit_name]);return e.length>0?e:[["Không có dữ liệu",""]]},Pt=t=>{if(!t||t.length===0)return[["Không có dữ liệu",""]];const e=t.map(n=>[n.city_id,n.city_name]);return e.length>0?e:[["Không có dữ liệu",""]]},Ht=(t,e)=>{var I,T,D,P,N,R,E,v,L,$;const n=Rt(),i=t.addWorksheet("Template"),a=i.addRow(['Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet "Menu" để nhập dữ liệu.']);i.mergeCells(`A${a.number}:AA${a.number}`),a.getCell(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},a.getCell(1).font={color:{argb:"FFFFFFFF"},bold:!0,size:11},a.getCell(1).alignment={horizontal:"left",vertical:"middle"},a.getCell(1).border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}},i.addRow(n).eachCell(h=>{h.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},h.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},h.alignment={horizontal:"center",vertical:"middle"},h.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),jt().forEach(h=>{i.addRow(h).eachCell(r=>{r.font={size:10},r.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),i.addRow([]);const _=i.addRow(["BẢNG THAM CHIẾU NGÀY","","","BẢNG THAM CHIẾU GIỜ","","","BẢNG THAM CHIẾU NHÓM MÓN","","","BẢNG THAM CHIẾU LOẠI MÓN","","","BẢNG THAM CHIẾU ĐƠN VỊ",""]),o=_.number;i.mergeCells(`A${o}:B${o}`),i.mergeCells(`D${o}:E${o}`),i.mergeCells(`G${o}:H${o}`),i.mergeCells(`J${o}:K${o}`),i.mergeCells(`M${o}:N${o}`),_.eachCell(h=>{h.value&&h.value.toString().trim()!==""&&(h.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},h.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},h.alignment={horizontal:"center",vertical:"middle"},h.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})}),i.addRow(["Thời gian","Giá trị","","Thời gian","Giá trị","","Mã nhóm","Tên nhóm","","Mã loại món","Tên loại món","","Mã đơn vị","Tên đơn vị"]).eachCell(h=>{h.value&&h.value.toString().trim()!==""&&(h.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79ABE3"}},h.font={bold:!0,size:10},h.alignment={horizontal:"center",vertical:"middle"},h.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})});const u=nt(),m=it(),f=e!=null&&e.itemTypes?ot(e.itemTypes):[["Không có dữ liệu",""]],l=e!=null&&e.itemClasses?st(e.itemClasses):[["Không có dữ liệu",""]],y=e!=null&&e.units?at(e.units):[["Không có dữ liệu",""]],F=Math.max(u.length,m.length,f.length,l.length,y.length),b=[];for(let h=0;h<F;h++){const g=[((I=u[h])==null?void 0:I[0])||"",((T=u[h])==null?void 0:T[1])||"","",((D=m[h])==null?void 0:D[0])||"",((P=m[h])==null?void 0:P[1])||"","",((N=f[h])==null?void 0:N[0])||"",((R=f[h])==null?void 0:R[1])||"","",((E=l[h])==null?void 0:E[0])||"",((v=l[h])==null?void 0:v[1])||"","",((L=y[h])==null?void 0:L[0])||"",(($=y[h])==null?void 0:$[1])||""];b.push(g)}return b.forEach(h=>{i.addRow(h).eachCell(r=>{r.value&&r.value.toString().trim()!==""&&(r.font={size:10},r.alignment={horizontal:"left",vertical:"middle"},r.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}})})}),[36,15,12,25,12,12,15,15,35,12,15,12,15,12,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((h,g)=>{i.getColumn(g+1).width=h}),i},zt=t=>{const e=Et(),n=t.addWorksheet("Menu");return n.addRow(e).eachCell(s=>{s.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},s.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},s.alignment={horizontal:"center",vertical:"middle"},s.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),[12,25,12,15,15,15,35,15,15,25,15,12,12,25,25,20,25,35,12,12,25].forEach((s,p)=>{n.getColumn(p+1).width=s}),n},Lt=(t,e)=>{var T,D,P,N,R,E,v,L,$,h,g;const n=At(),i=t.addWorksheet("Template"),a=i.addRow(["Đây là sheet mẫu để tham khảo, vui lòng quay lại sheet 1 để nhập thông tin"]);i.mergeCells(`A${a.number}:AA${a.number}`),a.getCell(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},a.getCell(1).font={color:{argb:"FFFFFFFF"},bold:!0,size:11},a.getCell(1).alignment={horizontal:"left",vertical:"middle"},a.getCell(1).border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}},i.addRow(n).eachCell(r=>{r.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},r.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},r.alignment={horizontal:"center",vertical:"middle"},r.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),Dt().forEach(r=>{i.addRow(r).eachCell(A=>{A.font={size:10},A.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),i.addRow([]);const _=i.addRow(["BẢNG THAM CHIẾU NGÀY","","","BẢNG THAM CHIẾU GIỜ","","","BẢNG THAM CHIẾU NHÓM MÓN","","","BẢNG THAM CHIẾU LOẠI MÓN","","","BẢNG THAM CHIẾU ĐƠN VỊ","","","Danh sách"]),o=_.number;i.mergeCells(`A${o}:B${o}`),i.mergeCells(`D${o}:E${o}`),i.mergeCells(`G${o}:H${o}`),i.mergeCells(`J${o}:K${o}`),i.mergeCells(`M${o}:N${o}`),_.eachCell(r=>{r.value&&r.value.toString().trim()!==""&&(r.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},r.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},r.alignment={horizontal:"center",vertical:"middle"},r.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})}),i.addRow(["Thời gian","Giá trị","","Thời gian","Giá trị","","Mã nhóm","Tên nhóm","","Mã loại món","Tên loại món","","Mã đơn vị","Tên đơn vị","","Thành phố"]).eachCell(r=>{r.value&&r.value.toString().trim()!==""&&(r.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79ABE3"}},r.font={bold:!0,size:10},r.alignment={horizontal:"center",vertical:"middle"},r.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})});const u=nt(),m=it(),f=e!=null&&e.itemTypes?ot(e.itemTypes):[["Không có dữ liệu",""]],l=e!=null&&e.itemClasses?st(e.itemClasses):[["Không có dữ liệu",""]],y=e!=null&&e.units?at(e.units):[["Không có dữ liệu",""]],F=e!=null&&e.cities?Pt(e.cities):[["Không có dữ liệu",""]],b=Math.max(u.length,m.length,f.length,l.length,y.length,F.length),x=[];for(let r=0;r<b;r++){const M=[((T=u[r])==null?void 0:T[0])||"",((D=u[r])==null?void 0:D[1])||"","",((P=m[r])==null?void 0:P[0])||"",((N=m[r])==null?void 0:N[1])||"","",((R=f[r])==null?void 0:R[0])||"",((E=f[r])==null?void 0:E[1])||"","",((v=l[r])==null?void 0:v[0])||"",((L=l[r])==null?void 0:L[1])||"","",(($=y[r])==null?void 0:$[0])||"",((h=y[r])==null?void 0:h[1])||"","",((g=F[r])==null?void 0:g[1])||""];x.push(M)}return x.forEach(r=>{i.addRow(r).eachCell(A=>{A.value&&A.value.toString().trim()!==""&&(A.font={size:10},A.alignment={horizontal:"left",vertical:"middle"},A.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}})})}),[36,15,20,25,25,25,30,30,35,12,15,12,20,20,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((r,M)=>{i.getColumn(M+1).width=r}),i},fe=async(t,e)=>{try{const n=Y(),i=et(n);if(e&&e.length>0){const u=l=>{var F;const y=(F=t==null?void 0:t.itemTypes)==null?void 0:F.find(b=>b.id===l);return{name:(y==null?void 0:y.item_type_name)||l,id:(y==null?void 0:y.item_type_id)||l}},m=l=>{var F;const y=(F=t==null?void 0:t.itemClasses)==null?void 0:F.find(b=>b.id===l);return{name:(y==null?void 0:y.item_class_name)||l,id:(y==null?void 0:y.item_class_id)||l}},f=l=>{var F;const y=(F=t==null?void 0:t.units)==null?void 0:F.find(b=>b.id===l);return{name:(y==null?void 0:y.unit_name)||l,id:(y==null?void 0:y.unit_id)||l}};e.forEach(l=>{var I;const F=((I=(l.cities||[])[0])==null?void 0:I.city_name)||"",b=l.extra_data||{},x=[l.id||"",l.item_id||"",F,l.item_name||"",l.ta_price||0,l.active||1,l.item_id_barcode||"",l.is_eat_with||0,b.no_update_quantity_toping||1,f(l.unit_uid).id,u(l.item_type_uid).id,u(l.item_type_uid).name,m(l.item_class_uid).id,m(l.item_class_uid).name,l.description||"",l.item_id_mapping||"",l.ta_tax*100||0,Math.round(l.time_cooking/6e4)||0,b.enable_edit_price||0,b.is_virtual_item||0,b.is_item_service||0,b.is_buffet_item||0,l.time_sale_hour_day||0,l.time_sale_date_week||0,l.sort,l.image_path||"",b.formula_qrcode||""];i.addRow(x)})}Ht(n,t);const a=await n.xlsx.writeBuffer(),s=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),_=`items_export_template_${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.xlsx`,o=window.URL.createObjectURL(s),c=document.createElement("a");return c.href=o,c.download=_,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(o),Promise.resolve()}catch(n){return console.error("Error creating Excel file:",n),Promise.reject(n)}},$t=async t=>{try{const e=Y();zt(e),Lt(e,t);const n=await e.xlsx.writeBuffer(),i=new Blob([n],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),s=`items_import_template_${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.xlsx`,p=window.URL.createObjectURL(i),_=document.createElement("a");return _.href=p,_.download=s,document.body.appendChild(_),_.click(),document.body.removeChild(_),window.URL.revokeObjectURL(p),Promise.resolve()}catch(e){return console.error("Error creating import Excel file:",e),Promise.reject(e)}},Kt=async(t,e)=>{try{const n=Y(),i=et(n),a=o=>{var u;const c=(u=e==null?void 0:e.itemTypes)==null?void 0:u.find(m=>m.id===o);return{name:(c==null?void 0:c.item_type_name)||o,id:(c==null?void 0:c.item_type_id)||o}},s=o=>{var u;const c=(u=e==null?void 0:e.itemClasses)==null?void 0:u.find(m=>m.id===o);return{name:(c==null?void 0:c.item_class_name)||o,id:(c==null?void 0:c.item_class_id)||o}},p=o=>{var u;const c=(u=e==null?void 0:e.units)==null?void 0:u.find(m=>m.id===o);return{name:(c==null?void 0:c.unit_name)||o,id:(c==null?void 0:c.unit_id)||o}};t.forEach(o=>{var l;const u=((l=(o.cities||[])[0])==null?void 0:l.city_name)||"",m=o.extra_data||{},f=[o.id||"",o.item_id||"",u,o.item_name||"",o.ots_price||0,o.active||1,o.item_id_barcode||"",o.is_eat_with||0,m.no_update_quantity_toping||1,p(o.unit_uid).id,a(o.item_type_uid).id,a(o.item_type_uid).name,s(o.item_class_uid).id,s(o.item_class_uid).name,o.description||"",o.item_id_mapping||"",o.ots_tax*100||0,Math.round(o.time_cooking/6e4)||0,m.enable_edit_price||0,m.is_virtual_item||0,m.is_item_service||0,m.is_buffet_item||0,o.time_sale_hour_day||0,o.time_sale_date_week||0,o.sort,o.image_path||"",m.formula_qrcode||""];i.addRow(f)});const _=await n.xlsx.writeBuffer();return new Blob([_],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})}catch(n){throw console.error("Error creating Excel blob:",n),n}},be=()=>{const t="0123456789";return`ITEM-${Array.from({length:4},()=>t[Math.floor(Math.random()*t.length)]).join("")}`},Fe=(t,e)=>{var n;return(n=e==null?void 0:e.find(i=>i.sourceId===t||i.id===t))==null?void 0:n.sourceName},Ce=t=>{const e={1:"Thứ 2",2:"Thứ 3",4:"Thứ 4",8:"Thứ 5",16:"Thứ 6",32:"Thứ 7",64:"Chủ nhật"};return t.map(n=>e[n]).join(", ")},Ie=t=>t.map(e=>`${e}h`).join(", "),we=t=>{const e=[];return t&1&&e.push(1),t&2&&e.push(2),t&4&&e.push(4),t&8&&e.push(8),t&16&&e.push(16),t&32&&e.push(32),t&64&&e.push(64),e},xe=t=>{const e=[];for(let n=0;n<24;n++)t&1<<n&&e.push(n);return e},Te=t=>t.toLocaleDateString("vi-VN"),ve=t=>t.map(e=>{const n=e.selectedDays.reduce((a,s)=>a|s,0),i=e.selectedHours.reduce((a,s)=>a|1<<s,0);return{price:e.price,from_date:e.startDate.getTime(),to_date:new Date(e.endDate.getTime()+24*60*60*1e3-1).getTime(),time_sale_date_week:n,time_sale_hour_day:i}}),qt=()=>{try{const t=localStorage.getItem("pos_cities_data");if(t)return JSON.parse(t).map(n=>n.id)}catch{}return[]},O=t=>{if(!t)return null;if(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(t))return t;try{const n=localStorage.getItem("pos_cities_data");if(n){const a=JSON.parse(n).find(s=>s.city_name===t);return(a==null?void 0:a.id)||null}}catch{return null}return null},H=new Map,B=new Map,Ut=10*60*1e3;function K(t){return typeof t=="object"&&t!==null&&"response"in t}const w={getItemsInCity:async t=>{const e=t.city_uid&&t.city_uid!=="all"?O(t.city_uid):t.city_uid,n=t.active!==void 0?t.active.toString():"undefined",i=t.skip_limit?"true":"false",a=`${t.company_uid}-${t.brand_uid}-${t.page||1}-${e||"all"}-${t.list_city_uid||"all"}-${t.item_type_uid||"all"}-${t.time_sale_date_week||""}-${n}-${t.reverse||0}-${t.search||""}-${t.limit||50}-${i}`,s=H.get(a);if(s&&Date.now()-s.timestamp<Ut)return s.data;const p=B.get(a);if(p)return p;const _=(async()=>{try{const o=new URLSearchParams;if(o.append("company_uid",t.company_uid),o.append("brand_uid",t.brand_uid),t.page&&o.append("page",t.page.toString()),t.item_type_uid&&o.append("item_type_uid",t.item_type_uid),t.list_city_uid)o.append("list_city_uid",t.list_city_uid);else if(t.city_uid&&t.city_uid!=="all"){const m=O(t.city_uid);m&&o.append("city_uid",m)}else{const m=qt();m.length>0&&o.append("list_city_uid",m.join(","))}t.time_sale_date_week&&o.append("time_sale_date_week",t.time_sale_date_week),t.reverse!==void 0&&o.append("reverse",t.reverse.toString()),t.search&&o.append("search",t.search),t.active!==void 0&&o.append("active",t.active.toString()),t.limit&&o.append("limit",t.limit.toString()),t.skip_limit&&o.append("skip_limit","true");const c=await k.get(`/mdata/v1/items?${o.toString()}`);if(!c.data||typeof c.data!="object")throw new Error("Invalid response format from items in city API");const u=c.data;return H.set(a,{data:u,timestamp:Date.now()}),u}finally{B.delete(a)}})();return B.set(a,_),_},deleteItemInCity:async t=>{var e;try{const n=new URLSearchParams;n.append("company_uid",t.company_uid),n.append("brand_uid",t.brand_uid),n.append("id",t.id),await k.delete(`/mdata/v1/item?${n.toString()}`),H.clear()}catch(n){throw K(n)&&((e=n.response)==null?void 0:e.status)===404?new Error("Item not found."):n}},deleteMultipleItemsInCity:async t=>{var e;try{const n=new URLSearchParams;n.append("company_uid",t.company_uid),n.append("brand_uid",t.brand_uid),n.append("list_item_uid",t.list_item_uid.join(",")),await k.delete(`/mdata/v1/items?${n.toString()}`),H.clear()}catch(n){throw K(n)&&((e=n.response)==null?void 0:e.status)===404?new Error("Items not found."):n}},downloadTemplate:async t=>{var s;const e=t.city_uid&&t.city_uid!=="all"?O(t.city_uid):null,n=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,...e&&{city_uid:e},...t.item_type_uid&&t.item_type_uid!=="all"&&{item_type_uid:t.item_type_uid},...t.active&&t.active!=="all"&&{active:t.active}}),i=await k.get(`/mdata/v1/items?${n}`),a=Array.isArray((s=i.data)==null?void 0:s.data)?i.data.data:[];return await Kt(a,t.referenceData)},fetchItemsData:async t=>{var a;const e=t.city_uid&&t.city_uid!=="all"?O(t.city_uid):null,n=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,...e&&{city_uid:e},...t.item_type_uid&&t.item_type_uid!=="all"&&{item_type_uid:t.item_type_uid},...t.active&&t.active!=="all"&&{active:t.active}}),i=await k.get(`/mdata/v1/items?${n}`);return Array.isArray((a=i.data)==null?void 0:a.data)?i.data.data:[]},createItemInCity:async t=>{var e,n;try{const i=await k.post("/mdata/v1/item",t);return H.clear(),i.data.data||i.data}catch(i){throw K(i)&&((e=i.response)==null?void 0:e.status)===400?new Error(((n=i.response.data)==null?void 0:n.message)||"Invalid data provided."):i}},updateItemInCity:async t=>{var e,n;try{const i=await k.put("/mdata/v1/item",t,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return H.clear(),i.data.data||i.data}catch(i){throw K(i)&&((e=i.response)==null?void 0:e.status)===400?new Error(((n=i.response.data)==null?void 0:n.message)||"Invalid data provided."):i}},getItemByListId:async t=>{var e,n;try{const i=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,is_all:"true",list_item_id:t.list_item_id}),a=await k.get(`/mdata/v1/items?${i}`,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4}),s=Array.isArray((e=a.data)==null?void 0:e.data)?a.data.data:[];if(!s.length)throw new Error("Item not found");return{data:s[0]}}catch(i){throw K(i)&&((n=i.response)==null?void 0:n.status)===404?new Error("Item not found."):i}},getItemById:async t=>{var e;try{const n=new URLSearchParams;n.append("id",t.id),t.company_uid&&n.append("company_uid",t.company_uid),t.brand_uid&&n.append("brand_uid",t.brand_uid);const i=await k.get(`/mdata/v1/item?${n.toString()}`);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from item detail API");return i.data}catch(n){throw K(n)&&((e=n.response)==null?void 0:e.status)===404?new Error("Item not found."):n}},importItems:async t=>(await k.post("/mdata/v1/items/import",{company_uid:t.company_uid,brand_uid:t.brand_uid,items:t.items})).data,updateItemStatus:async t=>{var e,n;try{const a={...(await w.getItemById({id:t.id})).data,active:t.active,company_uid:t.company_uid,brand_uid:t.brand_uid},s=await k.put("/mdata/v1/item",a,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return H.clear(),s.data.data||s.data}catch(i){throw K(i)&&((e=i.response)==null?void 0:e.status)===400?new Error(((n=i.response.data)==null?void 0:n.message)||"Invalid data provided."):i}},downloadImportTemplate:async t=>{try{await $t(t)}catch(e){throw console.error("Error creating import template:",e),e}},bulkUpdateItemsInCity:async t=>{const e=await k.put("/mdata/v1/items",t);return e.data.data||e.data},bulkCreateItemsInCity:async t=>{const e=await k.post("/mdata/v1/items",t);return e.data.data||e.data},clearCache:()=>{H.clear(),B.clear()},getCacheStats:()=>({cacheSize:H.size,pendingRequests:B.size})},Gt=(t={})=>{const{params:e={},enabled:n=!0}=t,{company:i,brands:a}=G(l=>l.auth),s=a==null?void 0:a[0],p={company_uid:(i==null?void 0:i.id)||"",brand_uid:(s==null?void 0:s.id)||"",page:1,reverse:1,limit:50,...e},_=!!(i!=null&&i.id&&(s!=null&&s.id)),o=Q({queryKey:[S.ITEMS_IN_CITY_LIST,JSON.stringify(p)],queryFn:async()=>(await w.getItemsInCity(p)).data||[],enabled:n&&_,staleTime:5*60*1e3,refetchInterval:10*60*1e3}),c={...p,page:(p.page||1)+1},u=Q({queryKey:[S.ITEMS_IN_CITY_LIST,"next",JSON.stringify(c)],queryFn:async()=>(await w.getItemsInCity(c)).data||[],enabled:n&&_&&(o.data?o.data.length>0:!1),staleTime:2*60*1e3,gcTime:5*60*1e3}),m=p.limit||50,f=(u.data?u.data.length>0:!1)||(o.data?o.data.length===m:!1);return{data:o.data,isLoading:o.isLoading,error:o.error,refetch:o.refetch,isFetching:o.isFetching,nextPageData:u.data||[],hasNextPage:f}},Me=(t,e=!0)=>Q({queryKey:[S.ITEMS_IN_CITY_DETAIL,t],queryFn:()=>w.getItemById({id:t}),enabled:e&&!!t,staleTime:5*60*1e3}),ke=(t={})=>{var o,c,u;const e=Gt(t),n=(o=t.params)==null?void 0:o.city_uid,i=(c=t.params)==null?void 0:c.list_city_uid,{data:a=[]}=dt({skip_limit:!0,...n&&n!=="all"?{city_uid:n}:{},...i?{list_city_uid:i}:{}}),{data:s=[]}=ut({skip_limit:!0}),{data:p=[]}=mt();return{data:((u=e.data)==null?void 0:u.map(m=>{var R,E;const f=m,y=((R=(f.cities||[])[0])==null?void 0:R.city_name)||"",F=f.item_type_uid?a.find(v=>v.id===f.item_type_uid):null,b=(F==null?void 0:F.item_type_name)||"",x=f.item_class_uid?s.find(v=>v.id===f.item_class_uid):null,I=(x==null?void 0:x.item_class_name)||"",T=f.unit_uid?p.find(v=>v.id===f.unit_uid):null,D=(T==null?void 0:T.unit_name)||"",N=f.is_eat_with===1||f.item_id_eat_with&&f.item_id_eat_with!==""?f.item_id_eat_with||"Món ăn kèm":"";return{...m,code:f.item_id||"",name:m.item_name,price:m.ots_price,vatPercent:m.ots_tax,cookingTime:m.time_cooking,categoryGroup:b,itemType:b,itemClass:I,unit:D,sideItems:N||void 0,city:y,buffetConfig:((E=f.extra_data)==null?void 0:E.is_buffet_item)===1?"Đã cấu hình":"Chưa cấu hình",customization:f.customization_uid||void 0,isActive:!!m.active,createdAt:typeof m.created_at=="number"?new Date(m.created_at*1e3):new Date(new Date(m.created_at).getTime())}}))||[],isLoading:e.isLoading,error:e.error,refetch:e.refetch,isFetching:e.isFetching,nextPageData:e.nextPageData,hasNextPage:e.hasNextPage}},Se=()=>{const t=q(),e=z({mutationFn:n=>w.createItemInCity(n),onSuccess:()=>{w.clearCache(),t.invalidateQueries({queryKey:[S.ITEMS_IN_CITY_LIST]}),C.success("Tạo món thành công!")},onError:n=>{C.error(n.message||"Có lỗi xảy ra khi tạo món")}});return{createItemAsync:e.mutateAsync,isPending:e.isPending}},Bt=()=>{const t=q(),e=z({mutationFn:n=>w.updateItemInCity(n),onSuccess:()=>{w.clearCache(),t.invalidateQueries({queryKey:[S.ITEMS_IN_CITY_LIST]}),t.invalidateQueries({queryKey:[S.ITEMS_IN_CITY_DETAIL]}),C.success("Cập nhật món thành công!")},onError:n=>{C.error(n.message||"Có lỗi xảy ra khi cập nhật món")}});return{updateItemAsync:e.mutateAsync,isPending:e.isPending}},Ne=()=>{const t=q(),{company:e,brands:n}=G(s=>s.auth),i=n==null?void 0:n[0],a=z({mutationFn:s=>{const p={company_uid:(e==null?void 0:e.id)||"",brand_uid:(i==null?void 0:i.id)||"",id:s};return w.deleteItemInCity(p)},onSuccess:()=>{t.invalidateQueries({queryKey:[S.ITEMS_IN_CITY_LIST]}),C.success("Xóa món thành công!")},onError:s=>{C.error(s.message||"Có lỗi xảy ra khi xóa món")}});return{deleteItemAsync:a.mutateAsync,isPending:a.isPending}},Re=()=>{const t=q(),{company:e,brands:n}=G(s=>s.auth),i=n==null?void 0:n[0],a=z({mutationFn:s=>{const p={company_uid:(e==null?void 0:e.id)||"",brand_uid:(i==null?void 0:i.id)||"",list_item_uid:s};return w.deleteMultipleItemsInCity(p)},onSuccess:()=>{t.invalidateQueries({queryKey:[S.ITEMS_IN_CITY_LIST]}),C.success("Xóa món ăn thành công")},onError:s=>{C.error((s==null?void 0:s.message)||"Có lỗi xảy ra khi xóa món ăn")}});return{deleteMultipleItemsAsync:a.mutateAsync,isPending:a.isPending}},Ee=()=>{const t=q(),{company:e,brands:n}=G(s=>s.auth),i=n==null?void 0:n[0],a=z({mutationFn:s=>{const p={id:s.id,active:s.active,company_uid:(e==null?void 0:e.id)||"",brand_uid:(i==null?void 0:i.id)||""};return w.updateItemStatus(p)},onSuccess:()=>{w.clearCache(),t.invalidateQueries({queryKey:[S.ITEMS_IN_CITY_LIST]}),t.invalidateQueries({queryKey:[S.ITEMS_IN_CITY_DETAIL]}),C.success("Cập nhật trạng thái thành công!")},onError:s=>{C.error(s.message||"Có lỗi xảy ra khi cập nhật trạng thái")}});return{updateStatusAsync:a.mutateAsync,isPending:a.isPending}},Ae=()=>{const t=z({mutationFn:e=>w.downloadImportTemplate(e),onSuccess:()=>{C.success("Tải template thành công!")},onError:e=>{C.error(e.message||"Có lỗi xảy ra khi tải template")}});return{downloadImportTemplateAsync:t.mutateAsync,isPending:t.isPending}},je=()=>{const{company:t,brands:e}=G(a=>a.auth),n=e==null?void 0:e[0],i=z({mutationFn:a=>{const s={company_uid:(t==null?void 0:t.id)||"",brand_uid:(n==null?void 0:n.id)||"",...a};return w.fetchItemsData(s)},onError:a=>{C.error(a.message||"Có lỗi xảy ra khi tải dữ liệu")}});return{fetchItemsDataAsync:i.mutateAsync,isPending:i.isPending}},De=()=>{const t=q();return z({mutationFn:e=>w.bulkUpdateItemsInCity(e),onSuccess:()=>{t.invalidateQueries({queryKey:[S.ITEMS_IN_CITY_LIST]}),C.success("Cập nhật món ăn thành công")},onError:e=>{C.error((e==null?void 0:e.message)||"Có lỗi xảy ra khi cập nhật món ăn")}})},Pe=()=>{const t=q(),{mutate:e,isPending:n}=z({mutationFn:async i=>w.bulkCreateItemsInCity(i),onSuccess:()=>{t.invalidateQueries({queryKey:[S.ITEMS_IN_CITY_LIST],refetchType:"none"}),setTimeout(()=>{t.refetchQueries({queryKey:[S.ITEMS_IN_CITY_LIST]})},100),C.success("Tạo món ăn thành công!")},onError:i=>{C.error((i==null?void 0:i.message)||"Có lỗi xảy ra khi tạo món ăn")}});return{bulkCreateItemsInCity:e,isBulkCreating:n}};function He({itemsBuffet:t,open:e,onOpenChange:n,onItemsChange:i,items:a,hide:s=!0,enable:p=!0,onEnableChange:_}){const[o,c]=j.useState(""),[u,m]=j.useState([]),[f,l]=j.useState(!1),[y,F]=j.useState(!1),[b,x]=j.useState(!1);j.useEffect(()=>{e&&(m(Array.isArray(t)?t:[]),x(p))},[t,e]);const I=j.useMemo(()=>o?a.filter(g=>{var r;return(r=g.item_name)==null?void 0:r.toLowerCase().includes(o.toLowerCase())}):a,[a,o]),T=j.useMemo(()=>I.length?I.filter(g=>u.includes(g.item_id||"")):[],[I,u]),D=j.useMemo(()=>I.length?I.filter(g=>!u.includes(g.item_id||"")):[],[I,u]),P=g=>{m(r=>r.includes(g)?r.filter(M=>M!==g):[...r,g])},N=T.length,R=I.length,E=R>0&&N===R,v=N>0&&N<R,L=()=>{if(E){const g=I.map(r=>r.item_id);m(r=>r.filter(M=>!g.includes(M)))}else{const g=I.map(r=>r.item_id);m(r=>{const M=[...r];return g.forEach(A=>{M.includes(A||"")||M.push(A||"")}),M})}},$=()=>{i(u),n(!1)},h=()=>{m([]),n(!1)};return d.jsx(gt,{title:"Chọn danh sách món không đi kèm vé buffet",centerTitle:!0,open:e,onOpenChange:n,onCancel:h,onConfirm:$,confirmText:"Lưu",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:d.jsxs("div",{className:"space-y-4",children:[!s&&d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(W,{id:"enable-buffet",checked:b,onCheckedChange:g=>{const r=!!g;x(r),_==null||_(r)}}),d.jsx("label",{htmlFor:"enable-buffet",className:"cursor-pointer text-blue-600",onClick:()=>x(g=>!g),children:"Cấu hình món ăn là vé buffet"})]}),(s||b)&&d.jsxs(d.Fragment,{children:[d.jsx("div",{className:"flex items-center gap-2",children:d.jsx(ht,{placeholder:"Tìm kiếm",value:o,onChange:g=>c(g.target.value),className:"w-full"})}),!s&&d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx(U,{type:"button",variant:"outline",className:"flex-1 justify-start",children:"Danh sách món không đi kèm vé buffet"}),d.jsx(U,{type:"button",variant:"link",className:"flex-1 justify-start text-blue-600",onClick:()=>{},children:"Danh sách vé buffet được upsize"})]}),d.jsxs("div",{className:"rounded-lg bg-green-50 p-3",children:[d.jsxs("div",{className:"flex items-center space-x-3",children:[d.jsx(W,{id:"select-all",checked:E,...v&&{"data-indeterminate":"true"},onCheckedChange:L,className:"data-[state=checked]:border-green-600 data-[state=checked]:bg-green-600"}),d.jsxs("label",{htmlFor:"select-all",className:"cursor-pointer text-sm font-medium text-green-700",children:["Đã chọn ",N]}),d.jsx(U,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>l(!f),children:f?d.jsx(J,{className:"h-3 w-3"}):d.jsx(X,{className:"h-3 w-3"})})]}),!f&&T.length>0&&d.jsx("div",{className:"mt-3 space-y-2",children:T.map(g=>d.jsxs("div",{className:"flex items-center space-x-3",children:[d.jsx(W,{id:`selected-${g.item_id}`,checked:u.includes(g.item_id),onCheckedChange:()=>P(g.item_id)}),d.jsx("label",{htmlFor:`selected-${g.item_id}`,className:"flex-1 cursor-pointer text-sm",children:g.item_name})]},g.item_id))})]}),d.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[d.jsxs("div",{className:"flex items-center space-x-3",children:[d.jsxs("div",{className:"text-sm font-medium text-gray-700",children:["Còn lại ",D.length]}),d.jsx(U,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>F(!y),children:y?d.jsx(J,{className:"h-3 w-3"}):d.jsx(X,{className:"h-3 w-3"})})]}),!y&&d.jsx("div",{className:"mt-3 max-h-60 space-y-2 overflow-y-auto",children:D.map(g=>d.jsxs("div",{className:"flex items-center space-x-3",children:[d.jsx(W,{id:g.item_id,checked:u.includes(g.item_id),onCheckedChange:()=>P(g.item_id)}),d.jsx("label",{htmlFor:g.item_id,className:"flex-1 cursor-pointer text-sm",children:g.item_name})]},g.item_id))})]})]})]})})}const Wt=V.object({customization_uid:V.string().nullable()});function ze({item:t,customizations:e,open:n,onOpenChange:i}){const{updateItemAsync:a}=Bt(),{company:s}=G(c=>c.auth),{selectedBrand:p}=rt(),_=_t({resolver:It(Wt),defaultValues:{customization_uid:"none"}});j.useEffect(()=>{if(n)try{_.reset({customization_uid:(t==null?void 0:t.customization_uid)??null})}catch{C.error("Lỗi khi load customization data")}},[n,_,t]);const o=async c=>{try{if(!(t!=null&&t.id)||!(s!=null&&s.id)||!(p!=null&&p.id))throw new Error("Required data is missing");const u=c.customization_uid==="none"?null:c.customization_uid;await a({...t,customization_uid:u}),i(!1)}catch{C.error("Lỗi khi cập nhật customization")}};return t?d.jsx(wt,{open:n,onOpenChange:c=>{i(c),_.reset()},children:d.jsxs(xt,{className:"top-[20%] w-full max-w-4xl translate-y-[-50%]",children:[d.jsx(Tt,{children:d.jsx(vt,{className:"text-center",children:"Cấu hình customization"})}),d.jsx(yt,{..._,children:d.jsxs("form",{onSubmit:_.handleSubmit(o),className:"space-y-4",children:[d.jsx(pt,{control:_.control,name:"customization_uid",render:({field:c})=>d.jsxs(ft,{children:[d.jsx(bt,{children:"Customization áp dụng cho món"}),d.jsx(Ft,{children:d.jsx(St,{value:c.value??"",onValueChange:u=>c.onChange(u===""?null:u),options:e.map(u=>({value:u.id,label:u.name})),placeholder:"Chọn customization...",searchPlaceholder:"Tìm kiếm customization...",emptyText:"Không có dữ liệu",className:"w-full"})}),d.jsx(Ct,{})]})}),d.jsxs(Mt,{children:[d.jsx(kt,{asChild:!0,children:d.jsx(U,{variant:"outline",type:"button",children:"Hủy"})}),d.jsx(U,{type:"submit",disabled:_.formState.isSubmitting,children:_.formState.isSubmitting?"Đang lưu...":"Lưu"})]})]})})]})}):null}export{He as B,ze as C,ye as I,Re as a,Pe as b,Ae as c,De as d,je as e,fe as f,be as g,Gt as h,Ne as i,Ee as j,Bt as k,ke as l,Me as m,xe as n,we as o,Te as p,Ie as q,Ce as r,ve as s,Fe as t,pe as u,Se as v};
