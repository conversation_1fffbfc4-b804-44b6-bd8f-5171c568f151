import{j as e,B as x,r,R as W,a4 as O,h as ce,aS as oe,aT as de}from"./index-DZ2N7iEN.js";import{g as me}from"./error-utils-Cfeehata.js";import"./pos-api-PZMeNc3U.js";import{u as he}from"./use-stores-DvOEKdtr.js";import{b as xe,d as pe}from"./use-item-types-BdROFFdK.js";import"./vietqr-api-y35earyI.js";import{u as ue}from"./use-items-DL_H3tEk.js";import{u as ge}from"./use-removed-items-DMGo-T5T.js";import{a as fe,b as je}from"./use-item-categories-BGX_A9n_.js";import{u as ye}from"./use-printer-positions-data-NbPO7Frn.js";import"./user-B3TbfMPQ.js";import"./crm-api-DyqsDFNF.js";import{P as Ce}from"./modal-C3wc7cQn.js";import"./date-range-picker-extpnOqj.js";import{L as M}from"./form-CUvXDg29.js";import{I as A}from"./input-DpObCffE.js";import{C as B,S as ve,a as Ne,b as Se,c as _e,d as J}from"./select-B60YVMMU.js";import{C as R}from"./checkbox-Bqls14Dj.js";import{C as Q,a as V,b as $}from"./collapsible-Dk62AUDY.js";import{D as Y,a as Z,e as ee}from"./dialog-DDb7K8l1.js";import{C as H}from"./chevron-right-D-SI6PAy.js";import{u as we}from"./use-printer-positions-BrRvl-FT.js";import{X as Pe}from"./calendar-CoHe9sRq.js";function Ie({selectedItems:m,onItemSelection:o,getSelectedItemsDisplay:a,isFieldsDisabledAfterCreation:c}){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Thêm các món vào nhóm"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Các món đang thuộc nhóm khác sẽ được gán lại vào nhóm này."})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(M,{className:"min-w-[200px] text-sm font-medium",children:"Áp dụng cho món"}),e.jsx("div",{className:"flex-1",children:e.jsx(x,{type:"button",variant:"outline",onClick:o,className:"w-full justify-start text-left",disabled:c,children:a()})})]})]})}function be({open:m,onOpenChange:o,items:a,selectedItems:c,onItemsSelected:P}){const[w,k]=r.useState(""),[v,T]=r.useState(!1),[i,U]=r.useState(!1),[p,f]=r.useState(c);console.log("ItemSelectionModal - selectedItems:",c),console.log("ItemSelectionModal - open:",m);const h=r.useMemo(()=>{if(!w.trim())return a;const l=w.toLowerCase();return a.filter(u=>u.item_name.toLowerCase().includes(l)||u.item_id.toLowerCase().includes(l))},[a,w]),N=h.filter(l=>p.includes(l.item_id)),j=h.filter(l=>!p.includes(l.item_id)),y=l=>{const u=p.includes(l)?p.filter(D=>D!==l):[...p,l];f(u)},t=()=>{P(p),o(!1)},d=()=>{f(c),o(!1)};return W.useEffect(()=>{m&&(f(c),k(""))},[m,c]),e.jsx(Y,{open:m,onOpenChange:o,children:e.jsxs(Z,{className:"max-w-md",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(A,{placeholder:"Tìm kiếm món",value:w,onChange:l=>k(l.target.value),className:"w-full"})}),e.jsxs(Q,{open:!v,onOpenChange:T,children:[e.jsx(V,{asChild:!0,children:e.jsxs(x,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",N.length,")"]}),v?e.jsx(H,{className:"h-4 w-4"}):e.jsx(B,{className:"h-4 w-4"})]})}),e.jsxs($,{className:"space-y-2",children:[N.map(l=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(R,{checked:!0,onCheckedChange:()=>y(l.item_id)}),e.jsx("span",{className:"text-sm",children:l.item_name})]},l.id)),N.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn món nào"})]})]}),e.jsxs(Q,{open:!i,onOpenChange:U,children:[e.jsx(V,{asChild:!0,children:e.jsxs(x,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",j.length,")"]}),i?e.jsx(H,{className:"h-4 w-4"}):e.jsx(B,{className:"h-4 w-4"})]})}),e.jsxs($,{className:"space-y-2",children:[j.map(l=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(R,{checked:!1,onCheckedChange:()=>y(l.item_id)}),e.jsx("span",{className:"text-sm",children:l.item_name})]},l.id)),j.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có món nào"})]})]})]}),e.jsxs(ee,{children:[e.jsx(x,{variant:"outline",onClick:d,children:"Hủy"}),e.jsx(x,{onClick:t,children:"Lưu"})]})]})})}function ke({showPrinterSection:m,selectedPrinters:o,onPrinterSelection:a,getSelectedPrintersDisplay:c,newlyCreatedCategory:P}){return m?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Vị trí máy in áp dụng"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Chọn các vị trí máy in sẽ áp dụng cho nhóm món này."})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(M,{className:"min-w-[200px] text-sm font-medium",children:"Chọn vị trí máy in"}),e.jsx("div",{className:"flex-1",children:e.jsx(x,{type:"button",variant:"outline",onClick:a,className:"w-full justify-start text-left",children:c()})})]})]}):null}function Te({open:m,onOpenChange:o,printerPositions:a,selectedPrinters:c,onPrintersSelected:P,newlyCreatedCategory:w,currentCategory:k}){const[v,T]=r.useState(""),[i,U]=r.useState(!1),[p,f]=r.useState(!1),[h,N]=r.useState(c),[j,y]=r.useState(!1),t=we(),d=r.useMemo(()=>{if(!v.trim())return a;const n=v.toLowerCase();return a.filter(C=>C.printer_position_name.toLowerCase().includes(n)||C.printer_position_id.toLowerCase().includes(n))},[a,v]),l=d.filter(n=>h.includes(n.printer_position_id)),u=d.filter(n=>!h.includes(n.printer_position_id)),D=n=>{const C=h.includes(n)?h.filter(L=>L!==n):[...h,n];N(C)},E=async()=>{const n=w||k;if(!n){P(h),o(!1);return}y(!0);try{const C=new Set(c),L=new Set(h),S=h.filter(_=>!C.has(_)),z=c.filter(_=>!L.has(_)),g=[];S.forEach(_=>{const I=a.find(F=>F.printer_position_id===_);I&&g.push(t.mutateAsync({printerPosition:I,categoryId:n.item_type_id,action:"add"}))}),z.forEach(_=>{const I=a.find(F=>F.printer_position_id===_);I&&g.push(t.mutateAsync({printerPosition:I,categoryId:n.item_type_id,action:"remove"}))}),g.length>0&&(await Promise.all(g),O.success(`Đã cập nhật ${g.length} vị trí máy in cho nhóm món "${n.item_type_name}"`)),P(h),o(!1)}catch(C){console.error("Error updating printer positions:",C),O.error("Có lỗi xảy ra khi cập nhật vị trí máy in")}finally{y(!1)}},K=()=>{N(c),o(!1)};return W.useEffect(()=>{m&&(N(c),T(""))},[m,c]),e.jsx(Y,{open:m,onOpenChange:o,children:e.jsxs(Z,{className:"max-w-md",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(A,{placeholder:"Tìm kiếm vị trí máy in",value:v,onChange:n=>T(n.target.value),className:"w-full"})}),e.jsxs(Q,{open:!i,onOpenChange:U,children:[e.jsx(V,{asChild:!0,children:e.jsxs(x,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",l.length,")"]}),i?e.jsx(H,{className:"h-4 w-4"}):e.jsx(B,{className:"h-4 w-4"})]})}),e.jsxs($,{className:"space-y-2",children:[l.map(n=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(R,{checked:!0,onCheckedChange:()=>D(n.printer_position_id)}),e.jsx("span",{className:"text-sm",children:n.printer_position_name})]},n.printer_position_id)),l.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn vị trí máy in nào"})]})]}),e.jsxs(Q,{open:!p,onOpenChange:f,children:[e.jsx(V,{asChild:!0,children:e.jsxs(x,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",u.length,")"]}),p?e.jsx(H,{className:"h-4 w-4"}):e.jsx(B,{className:"h-4 w-4"})]})}),e.jsxs($,{className:"space-y-2",children:[u.map(n=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(R,{checked:!1,onCheckedChange:()=>D(n.printer_position_id)}),e.jsx("span",{className:"text-sm",children:n.printer_position_name})]},n.printer_position_id)),u.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có vị trí máy in nào"})]})]})]}),e.jsxs(ee,{children:[e.jsx(x,{variant:"outline",onClick:K,disabled:j,children:"Hủy"}),e.jsx(x,{onClick:E,disabled:j,children:j?"Đang cập nhật...":"Lưu"})]})]})})}function et({id:m}){const o=ce(),a=!!m,{mutate:c,isPending:P}=fe(),{mutate:w,isPending:k}=xe(),{mutate:v,isPending:T}=pe(),{data:i,isLoading:U}=je(m||"",a),{data:p=[]}=ge(),{data:f=[],isLoading:h}=he(),{data:N=[]}=ue({params:{skip_limit:!0,list_city_uid:p.map(s=>s.id).join(",")},enabled:p.length>0}),{data:j}=ye({enabled:!0}),y=Array.isArray(j)?j:[],[t,d]=r.useState({name:"",code:"",followCategoryCreation:!1,displayOrder:void 0,selectedItems:[],selectedPrinters:[],selectedStoreUid:""}),[l,u]=r.useState(!1),[D,E]=r.useState(!1),[K,n]=r.useState(!1),[C,L]=r.useState(!1),[S,z]=r.useState(null);r.useEffect(()=>{if(a&&i&&y.length>0){const s=y.filter(b=>b.list_item_type_id.split(",").map(re=>re.trim()).filter(Boolean).includes(i.item_type_id)).map(b=>b.printer_position_id);d({name:i.item_type_name,code:i.item_type_id,followCategoryCreation:!1,displayOrder:i.sort||void 0,selectedItems:i.list_item||[],selectedPrinters:s,selectedStoreUid:i.store_uid||""}),L(!0)}},[a,i,y]),r.useEffect(()=>{!a&&f.length>0&&t.selectedStoreUid===""&&d(s=>({...s,selectedStoreUid:f[0].id}))},[a,f,t.selectedStoreUid]),r.useEffect(()=>{S&&!a&&d(s=>({...s,code:S.item_type_id,followCategoryCreation:!0}))},[S,a]);const g=!a&&!!S,_=()=>{o({to:"/menu/categories/categories-in-store"})},I=async()=>{if(q){if(S&&!a){o({to:"/menu/categories/categories-in-store"});return}if(a&&i){const s={...i,item_type_name:t.name,sort:t.displayOrder||i.sort,extra_data:i.extra_data||{},list_item:t.selectedItems||[],store_uid:t.selectedStoreUid||null};v(s,{onSuccess:()=>{t.selectedPrinters.length===0?n(!0):(O.success("Đã cập nhật nhóm món tại cửa hàng thành công"),o({to:"/menu/categories/categories-in-store"}))}})}else c({name:t.name,code:t.followCategoryCreation&&t.code.trim()?t.code:void 0,sort:t.displayOrder,selectedItems:t.selectedItems,store_uid:t.selectedStoreUid},{onSuccess:s=>{z(s),n(!0)}})}},F=async()=>{if(!a||!i)return;const s={...i,active:i.active===1?0:1,extra_data:i.extra_data||{}};w(s,{onSuccess:()=>{const b=s.active===1?"kích hoạt":"vô hiệu hóa";O.success(`Đã ${b} nhóm món tại cửa hàng "${i.item_type_name}"`)},onError:b=>{const G=me(b);O.error(G)}})},q=t.name.trim()!==""&&t.selectedStoreUid!=="",X=P||U||k||T,te=()=>{u(!0)},se=s=>{d({...t,selectedItems:s}),u(!1)},ae=()=>{E(!0)},ne=s=>{d({...t,selectedPrinters:s}),E(!1)},ie=()=>{n(!1),o({to:"/menu/categories/categories-in-store"})},le=()=>{n(!1),L(!0)};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(x,{variant:"ghost",size:"sm",onClick:_,className:"flex items-center",children:e.jsx(Pe,{className:"h-4 w-4"})}),e.jsxs("div",{className:"flex items-center gap-2",children:[a&&i&&e.jsx(x,{type:"button",variant:i.active===1?"destructive":"default",disabled:X,className:"min-w-[100px]",onClick:F,children:i.active===1?"Deactivate":"Activate"}),(!a||(i==null?void 0:i.active)===1)&&e.jsx(x,{type:"button",disabled:X||!q,className:"min-w-[100px]",onClick:I,children:X?a?"Đang cập nhật...":"Đang tạo...":"Lưu"})]})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:a?"Chỉnh sửa nhóm món":"Tạo nhóm món"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(M,{htmlFor:"category-name",className:"min-w-[200px] text-sm font-medium",children:["Tên nhóm món ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(A,{id:"category-name",value:t.name,onChange:s=>d({...t,name:s.target.value}),placeholder:"Nhập tên nhóm món",className:"flex-1",disabled:g})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(M,{htmlFor:"store-select",className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng áp dụng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(ve,{value:t.selectedStoreUid,onValueChange:s=>d({...t,selectedStoreUid:s}),disabled:a||g,children:[e.jsx(Ne,{id:"store-select",className:"flex-1",children:e.jsx(Se,{placeholder:"Chọn cửa hàng"})}),e.jsx(_e,{children:h?e.jsx(J,{disabled:!0,value:"loading",children:"Đang tải..."}):f.map(s=>e.jsx(J,{value:s.id,children:s.name},s.id))})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(M,{htmlFor:"category-code",className:"min-w-[200px] text-sm font-medium",children:"Mã nhóm món"}),e.jsx(A,{id:"category-code",value:t.code,onChange:s=>d({...t,code:s.target.value}),placeholder:"Nếu để trống, hệ thống sẽ tự động tạo một mã nhóm món",disabled:!t.followCategoryCreation||a||g,className:"flex-1"}),!a&&!g&&e.jsx(R,{id:"follow-category-creation",checked:t.followCategoryCreation,onCheckedChange:s=>d({...t,followCategoryCreation:s,code:s?t.code:""})})]}),e.jsx(Ie,{selectedItems:t.selectedItems,onItemSelection:te,getSelectedItemsDisplay:()=>oe(t.selectedItems),isFieldsDisabledAfterCreation:g}),e.jsx(ke,{showPrinterSection:C,selectedPrinters:t.selectedPrinters,onPrinterSelection:ae,getSelectedPrintersDisplay:()=>de(t.selectedPrinters),newlyCreatedCategory:S}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(M,{htmlFor:"display-order",className:"min-w-[200px] text-sm font-medium",children:"Thứ tự hiển thị"}),e.jsx(A,{id:"display-order",type:"number",value:t.displayOrder||"",onChange:s=>d({...t,displayOrder:s.target.value?Number(s.target.value):void 0}),placeholder:"Nhập số thứ tự hiển thị",className:"flex-1"})]})]})})}),e.jsx(be,{open:l,onOpenChange:u,items:N,selectedItems:t.selectedItems,onItemsSelected:se}),e.jsx(Te,{open:D,onOpenChange:E,printerPositions:y,selectedPrinters:t.selectedPrinters,onPrintersSelected:ne,newlyCreatedCategory:S,currentCategory:a?i:null}),e.jsx(Ce,{open:K,onOpenChange:n,title:"Chưa có vị trí máy in nào áp dụng nhóm món, bạn có muốn chọn vị trí máy in?",cancelText:"Rời khỏi",confirmText:"Chọn thêm",onCancel:ie,onConfirm:le,children:e.jsx("div",{className:"py-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Bạn có thể chọn các vị trí máy in để áp dụng cho nhóm món này."})})})]})}export{et as C};
