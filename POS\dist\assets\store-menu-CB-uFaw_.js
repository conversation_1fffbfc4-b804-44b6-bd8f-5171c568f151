import{r as u,j as e,B as V,h as A}from"./index-DZ2N7iEN.js";import{s as E,t as K,w as k,x as G,y as O}from"./date-range-picker-extpnOqj.js";import{u as $,F as q,a as j,b as f,c as N,d as v,e as y}from"./form-CUvXDg29.js";import{I as C}from"./input-DpObCffE.js";import{u as Q,g as _,a as J,b as W,c as X,d as Y,e as Z,f as H}from"./index-AHlYCYkx.js";import{T as ee,a as se,b as z,c as ne,d as ae,e as F}from"./table-DQgmazAN.js";import{D as te}from"./data-table-pagination-DmfeHsfg.js";import{D as w}from"./data-table-column-header-4ZB1Mdgz.js";import{D as re,a as le,b as ie,c as D}from"./dropdown-menu-Ci4yPenc.js";import{S as oe}from"./settings-BWjFFru9.js";import{S as R}from"./store-D__ic3ZS.js";import{c as ce}from"./createLucideIcon-2r9cCEY3.js";import{C as de}from"./clock-H9kN5N-j.js";import{D as he}from"./dollar-sign-Dr3iyLvY.js";import{s as me}from"./zod-Dyorah_P.js";import{C as P}from"./checkbox-Bqls14Dj.js";import{S as xe}from"./scroll-area-CoEnZUVR.js";import{s as ue,A as pe}from"./address-map-display-BzeDdogm.js";import{X as B}from"./calendar-CoHe9sRq.js";import{U as ge}from"./upload-BpTKdC5P.js";import"./chevron-right-D-SI6PAy.js";import"./react-icons.esm-Ck8h5xF8.js";import"./popover-BK8t3srL.js";import"./index-CC24pdSB.js";import"./select-B60YVMMU.js";import"./index-Csh0LmDQ.js";import"./index-DIwSqRjm.js";import"./check-CP51yA4o.js";import"./isSameMonth-C8JQo-AN.js";import"./index-DeeqTHK3.js";import"./TileLayer-Bwutih7O.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]],fe=ce("menu",je),Ne=[{id:"121956",name:"Tutimi-Bình Lợi",phone:"**********",address:"146 Bình Lợi, Phường Bình Lợi Trung, TP Hồ Chí Minh",lastSalesDataUpdate:"2024-01-15 10:30:00",partnerDriverPhone:"**********",active:!0,onlineSales:!0,deliverySales:!1,onlineReservation:!0,emailList:["<EMAIL>","<EMAIL>"],banner:"https://via.placeholder.com/800x200/0066cc/ffffff?text=Tutimi+Banner"},{id:"122423",name:"Triều sữa 2",phone:"333",address:"WinMart, Đường Hoàng Quốc Việt, Phường Nghĩa Đô, Cau Giay District, Hà Nội, 11150, Vietnam",lastSalesDataUpdate:"2024-01-14 15:45:00",partnerDriverPhone:"**********",active:!1,onlineSales:!1,deliverySales:!0,onlineReservation:!1,emailList:["<EMAIL>"],banner:""},{id:"123672",name:"SEKAI-Đông Bắc 2",phone:"0961826917",address:"Co.opmart Bình Tân 2, 819 Hương Lộ 2, Phường Bình Trị Đông A, Quận Bình Tân, Hồ Chí Minh, Việt Nam",lastSalesDataUpdate:"2024-01-13 09:20:00"},{id:"123674",name:"dsdasd",phone:"0392321312",address:"Phan Đăng Lưu, Yên Viên, Gia Lâm, Hà Nội, Việt Nam",lastSalesDataUpdate:"2024-01-12 14:15:00"},{id:"123676",name:"2323",phone:"09323123",address:"Bình Lợi, Bình Chánh, Hồ Chí Minh, Việt Nam",lastSalesDataUpdate:"2024-01-11 11:30:00"},{id:"123678",name:"czxczxc",phone:"083232324",address:"Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam",lastSalesDataUpdate:"2024-01-10 16:45:00"},{id:"123678",name:"czxczxc",phone:"083232324",address:"Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam",lastSalesDataUpdate:"2024-01-10 16:45:00"},{id:"123678",name:"czxczxc",phone:"083232324",address:"Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam",lastSalesDataUpdate:"2024-01-10 16:45:00"},{id:"123678",name:"czxczxc",phone:"083232324",address:"Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam",lastSalesDataUpdate:"2024-01-10 16:45:00"},{id:"123678",name:"czxczxc",phone:"083232324",address:"Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam",lastSalesDataUpdate:"2024-01-10 16:45:00"},{id:"123678",name:"czxczxc",phone:"083232324",address:"Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam",lastSalesDataUpdate:"2024-01-10 16:45:00"},{id:"123678",name:"czxczxc",phone:"083232324",address:"Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam",lastSalesDataUpdate:"2024-01-10 16:45:00"},{id:"123678",name:"czxczxc",phone:"083232324",address:"Phan Văn Trị, Phường An Nhơn, Thành phố Hồ Chí Minh, 71409, Việt Nam",lastSalesDataUpdate:"2024-01-10 16:45:00"}],ve=()=>{const[t,n]=u.useState({search:""});return{stores:u.useMemo(()=>Ne.filter(r=>{const c=t.search.toLowerCase();return r.id.toLowerCase().includes(c)||r.name.toLowerCase().includes(c)||r.phone.includes(c)||r.address.toLowerCase().includes(c)}),[t.search]),filters:t,updateFilters:r=>{n(c=>({...c,...r}))},isLoading:!1,error:null}},Se=({value:t,onChange:n,onFilter:s,placeholder:o="Tên / ID nhà hàng"})=>{const r=d=>{n(d.target.value)},c=d=>{d.key==="Enter"&&(s==null||s())};return e.jsxs("div",{className:"flex items-center gap-2 mb-6",children:[e.jsx("div",{className:"flex-1 max-w-md",children:e.jsx(C,{type:"text",placeholder:o,value:t,onChange:r,onKeyPress:c,className:"w-full"})}),e.jsx(V,{onClick:s,variant:"default",className:"bg-orange-500 hover:bg-orange-600 text-white px-6",children:"Lọc"})]})};function be({columns:t,data:n,onRowClick:s}){var g;const[o,r]=u.useState({}),[c,d]=u.useState([]),[m,x]=u.useState([]),l=Q({data:n,columns:t,state:{sorting:m,columnVisibility:o,columnFilters:c},onSortingChange:x,onColumnFiltersChange:d,onColumnVisibilityChange:r,getCoreRowModel:Z(),getFilteredRowModel:Y(),getPaginationRowModel:X(),getSortedRowModel:W(),getFacetedRowModel:J(),getFacetedUniqueValues:_()});return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"rounded-md border",children:e.jsxs(ee,{children:[e.jsx(se,{children:l.getHeaderGroups().map(p=>e.jsx(z,{children:p.headers.map(a=>e.jsx(ne,{colSpan:a.colSpan,children:a.isPlaceholder?null:H(a.column.columnDef.header,a.getContext())},a.id))},p.id))}),e.jsx(ae,{children:(g=l.getRowModel().rows)!=null&&g.length?l.getRowModel().rows.map(p=>e.jsx(z,{className:s?"group/row cursor-pointer hover:bg-gray-50":"group/row",onClick:()=>s==null?void 0:s(p.original),children:p.getVisibleCells().map(a=>e.jsx(F,{children:H(a.column.columnDef.cell,a.getContext())},a.id))},p.id)):e.jsx(z,{children:e.jsx(F,{colSpan:t.length,className:"h-24 text-center",children:"Không tìm thấy nhà hàng nào."})})})]})}),e.jsx(te,{table:l})]})}function Ce({store:t,onStoreInfoClick:n,onMenuClick:s,onDeliveryTimeClick:o,onShippingFeeClick:r}){const c=()=>{n==null||n(t)},d=()=>{s==null||s(t)},m=()=>{o==null||o(t)},x=()=>{r==null||r(t)};return e.jsxs(re,{children:[e.jsx(le,{asChild:!0,children:e.jsxs(V,{variant:"ghost",size:"sm",className:"h-8 px-3 text-xs",children:[e.jsx(oe,{className:"h-3 w-3 mr-1"}),"Cài đặt"]})}),e.jsxs(ie,{align:"end",className:"w-56",children:[e.jsxs(D,{onClick:c,children:[e.jsx(R,{className:"mr-2 h-4 w-4"}),"Thông tin cửa hàng"]}),e.jsxs(D,{onClick:d,children:[e.jsx(fe,{className:"mr-2 h-4 w-4"}),"Thực đơn"]}),e.jsxs(D,{onClick:m,children:[e.jsx(de,{className:"mr-2 h-4 w-4"}),"Thời gian bán giao hàng"]}),e.jsxs(D,{onClick:x,children:[e.jsx(he,{className:"mr-2 h-4 w-4"}),"Phí vận chuyển"]})]})]})}const ye=t=>[{id:"index",header:()=>e.jsx("div",{className:"text-left",children:"#"}),cell:({row:n,table:s})=>{const o=s.getState().pagination.pageIndex,r=s.getState().pagination.pageSize;return e.jsx("div",{className:"w-[50px] text-left font-medium",children:o*r+n.index+1})},enableSorting:!1,enableHiding:!1,size:60},{accessorKey:"id",header:({column:n})=>e.jsx("div",{className:"text-center",children:e.jsx(w,{column:n,title:"ID"})}),cell:({row:n})=>e.jsx("div",{className:"text-left",children:e.jsx("span",{className:"font-medium",children:n.getValue("id")})}),enableSorting:!0,size:100},{accessorKey:"name",header:({column:n})=>e.jsx("div",{className:"text-center",children:e.jsx(w,{column:n,title:"Nhà hàng"})}),cell:({row:n})=>{const s=n.original;return e.jsxs("div",{className:"text-left space-y-2",children:[e.jsx("div",{children:e.jsx("span",{className:"font-medium",children:n.getValue("name")})}),e.jsx("div",{children:e.jsx(Ce,{store:s,onStoreInfoClick:t==null?void 0:t.onStoreInfoClick,onMenuClick:t==null?void 0:t.onMenuClick,onDeliveryTimeClick:(t==null?void 0:t.onDeliveryTimeClick)||(o=>{console.log("Thời gian bán giao hàng:",o)}),onShippingFeeClick:(t==null?void 0:t.onShippingFeeClick)||(o=>{console.log("Phí vận chuyển:",o)})})})]})},enableSorting:!0,size:200},{id:"image",header:()=>e.jsx("div",{className:"text-center",children:"Ảnh"}),cell:({row:n})=>{const s=n.original;return e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-md",children:s.image?e.jsx("img",{src:s.image,alt:s.name,className:"w-12 h-12 object-cover rounded-md"}):e.jsx(R,{className:"w-6 h-6 text-gray-400"})})})},enableSorting:!1,size:80},{accessorKey:"phone",header:({column:n})=>e.jsx("div",{className:"text-center",children:e.jsx(w,{column:n,title:"Số điện thoại"})}),cell:({row:n})=>e.jsx("div",{className:"text-left",children:e.jsx("span",{className:"text-sm",children:n.getValue("phone")})}),enableSorting:!0,size:120},{accessorKey:"address",header:({column:n})=>e.jsx("div",{className:"text-center",children:e.jsx(w,{column:n,title:"Địa chỉ"})}),cell:({row:n})=>e.jsx("div",{className:"text-left",children:e.jsx("span",{className:"text-sm max-w-[300px] truncate block",title:n.getValue("address"),children:n.getValue("address")})}),enableSorting:!0,size:300},{accessorKey:"lastSalesDataUpdate",header:({column:n})=>e.jsx("div",{className:"text-center",children:e.jsx(w,{column:n,title:"Cập nhật dữ liệu bán hàng lần cuối"})}),cell:({row:n})=>e.jsx("div",{className:"text-left",children:e.jsx("span",{className:"text-sm text-muted-foreground",children:n.getValue("lastSalesDataUpdate")||"-"})}),enableSorting:!0,size:200}];function we({value:t=[],onChange:n,placeholder:s="Nhập email và nhấn enter",disabled:o=!1}){const[r,c]=u.useState(""),[d,m]=u.useState(""),x=i=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(i),l=i=>{i.key==="Enter"&&(i.preventDefault(),g())},g=()=>{const i=r.trim();if(i){if(!x(i)){m("Email không hợp lệ");return}if(t.includes(i)){m("Email đã tồn tại");return}n([...t,i]),c(""),m("")}},p=i=>{n(t.filter(S=>S!==i))},a=i=>{c(i),d&&m("")};return e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"min-h-[60px] border border-input rounded-md p-3 bg-background",children:[e.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:t.map((i,S)=>e.jsxs("div",{className:"inline-flex items-center gap-1 bg-blue-600 text-white px-3 py-1 rounded text-sm",children:[e.jsx("span",{children:i}),e.jsx("button",{type:"button",onClick:()=>p(i),disabled:o,className:"ml-1 hover:bg-blue-700 rounded-full p-0.5 transition-colors",children:e.jsx(B,{className:"h-3 w-3"})})]},S))}),e.jsx(C,{value:r,onChange:i=>a(i.target.value),onKeyDown:l,placeholder:s,disabled:o,className:`border-none shadow-none p-0 h-auto focus-visible:ring-0 focus-visible:ring-offset-0 ${d?"text-destructive":""}`})]}),d&&e.jsx("p",{className:"text-sm text-destructive",children:d})]})}function Te({value:t,onChange:n,disabled:s=!1,accept:o="image/*",maxSize:r=10,placeholder:c="TẢI ẢNH LÊN",className:d=""}){const[m,x]=u.useState(""),[l,g]=u.useState(""),p=()=>m||(typeof t=="string"?t:""),a=M=>{var h;const b=(h=M.target.files)==null?void 0:h[0];if(!b)return;if(b.size>r*1024*1024){alert(`File size must be less than ${r}MB`);return}const T=new FileReader;T.onload=L=>{var U;const I=(U=L.target)==null?void 0:U.result;x(I),g(b.name),n(b,I)},T.readAsDataURL(b)},i=()=>{x(""),g(""),n(null,"")},S=p();return e.jsx("div",{className:`space-y-2 ${d}`,children:S?e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"relative inline-block",children:[e.jsx("img",{src:S,alt:"Preview",className:"h-32 w-full max-w-md rounded-md border object-cover"}),e.jsx(V,{type:"button",variant:"destructive",size:"sm",className:"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0",onClick:i,disabled:s,children:e.jsx(B,{className:"h-3 w-3"})})]}),l&&e.jsx("p",{className:"text-xs text-muted-foreground",children:l})]}):e.jsx("div",{className:"rounded-md border-2 border-dashed border-gray-300 p-8 text-center transition-colors hover:border-gray-400",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"mx-auto h-12 w-12 text-gray-400",children:e.jsx(ge,{className:"h-full w-full"})}),e.jsx("div",{children:e.jsxs("label",{htmlFor:"image-upload",className:"cursor-pointer",children:[e.jsx("span",{className:"text-sm font-medium text-blue-600 hover:text-blue-500",children:c}),e.jsx(C,{id:"image-upload",type:"file",accept:o,onChange:a,disabled:s,className:"hidden"})]})}),e.jsxs("p",{className:"text-xs text-gray-500",children:["PNG, JPG, GIF up to ",r,"MB"]})]})})})}function De({open:t,onOpenChange:n,store:s,onSave:o,isLoading:r=!1}){const[c,d]=u.useState(null),[m,x]=u.useState(""),l=$({resolver:me(ue),defaultValues:{name:"",address:"",phone:"",partnerDriverPhone:"",active:!1,onlineSales:!1,deliverySales:!1,onlineReservation:!1,emailList:[],banner:""}});u.useEffect(()=>{s&&(l.reset({name:s.name||"",address:s.address||"",phone:s.phone||"",partnerDriverPhone:s.partnerDriverPhone||"",active:s.active||!1,onlineSales:s.onlineSales||!1,deliverySales:s.deliverySales||!1,onlineReservation:s.onlineReservation||!1,emailList:s.emailList||[],banner:s.banner||""}),x(s.banner||""),d(null))},[s,l]);const g=(a,i)=>{d(a),x(i),l.setValue("banner",i)},p=a=>{const i={...a,bannerFile:c};o(i)};return e.jsx(E,{open:t,onOpenChange:n,children:e.jsxs(K,{side:"right",className:"w-[400px] sm:w-[500px]",children:[e.jsxs(k,{className:"px-6 bg-blue-600 text-white",children:[e.jsx(G,{className:"text-white",children:"Thông tin nhà hàng"}),e.jsx(O,{className:"text-blue-100",children:"Cập nhật thông tin chi tiết của nhà hàng"})]}),e.jsx(xe,{className:"h-[calc(100vh-80px)]",children:e.jsx(q,{...l,children:e.jsxs("form",{onSubmit:l.handleSubmit(p),className:"space-y-6 px-6 py-4",children:[e.jsx(j,{control:l.control,name:"name",render:({field:a})=>e.jsxs(f,{children:[e.jsx(N,{children:"Tên nhà hàng"}),e.jsx(v,{children:e.jsx(C,{...a,disabled:r})}),e.jsx(y,{})]})}),e.jsx(j,{control:l.control,name:"address",render:({field:a})=>e.jsxs(f,{children:[e.jsx(N,{children:"Địa chỉ nhà hàng"}),e.jsx(v,{children:e.jsx(C,{...a,disabled:r})}),e.jsx(y,{})]})}),e.jsx("div",{className:"space-y-2",children:e.jsx(pe,{address:l.watch("address"),height:"200px",className:"w-full"})}),e.jsx(j,{control:l.control,name:"phone",render:({field:a})=>e.jsxs(f,{children:[e.jsx(N,{children:"SĐT cửa hàng"}),e.jsx(v,{children:e.jsx(C,{...a,disabled:r})}),e.jsx(y,{})]})}),e.jsx(j,{control:l.control,name:"partnerDriverPhone",render:({field:a})=>e.jsxs(f,{children:[e.jsx(N,{children:"SĐT để tài xế đối tác liên hệ với cửa hàng"}),e.jsx(v,{children:e.jsx(C,{...a,disabled:r})}),e.jsx(y,{})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(j,{control:l.control,name:"active",render:({field:a})=>e.jsxs(f,{className:"flex flex-row items-start space-x-3 space-y-0",children:[e.jsx(v,{children:e.jsx(P,{checked:a.value,onCheckedChange:a.onChange,disabled:r})}),e.jsx("div",{className:"space-y-1 leading-none",children:e.jsx(N,{children:"ACTIVE"})})]})}),e.jsx(j,{control:l.control,name:"onlineSales",render:({field:a})=>e.jsxs(f,{className:"flex flex-row items-start space-x-3 space-y-0",children:[e.jsx(v,{children:e.jsx(P,{checked:a.value,onCheckedChange:a.onChange,disabled:r})}),e.jsx("div",{className:"space-y-1 leading-none",children:e.jsx(N,{children:"BÁN HÀNG ONLINE"})})]})}),e.jsx(j,{control:l.control,name:"deliverySales",render:({field:a})=>e.jsxs(f,{className:"flex flex-row items-start space-x-3 space-y-0",children:[e.jsx(v,{children:e.jsx(P,{checked:a.value,onCheckedChange:a.onChange,disabled:r})}),e.jsx("div",{className:"space-y-1 leading-none",children:e.jsx(N,{children:"BÁN HÀNG GIAO SAU"})})]})}),e.jsx(j,{control:l.control,name:"onlineReservation",render:({field:a})=>e.jsxs(f,{className:"flex flex-row items-start space-x-3 space-y-0",children:[e.jsx(v,{children:e.jsx(P,{checked:a.value,onCheckedChange:a.onChange,disabled:r})}),e.jsx("div",{className:"space-y-1 leading-none",children:e.jsx(N,{children:"ĐẶT BÀN ONLINE"})})]})})]}),e.jsx(j,{control:l.control,name:"emailList",render:({field:a})=>e.jsxs(f,{children:[e.jsx(N,{children:"Danh sách email"}),e.jsx(v,{children:e.jsx(we,{value:a.value,onChange:a.onChange,disabled:r})}),e.jsx(y,{})]})}),e.jsx(j,{control:l.control,name:"banner",render:({field:a})=>e.jsxs(f,{children:[e.jsx(N,{children:"Banner cửa hàng"}),e.jsx(v,{children:e.jsx(Te,{value:m||a.value,onChange:g,disabled:r,placeholder:"TẢI ẢNH LÊN",maxSize:10})}),e.jsx(y,{})]})}),e.jsx("div",{className:"pt-4",children:e.jsx(V,{type:"submit",className:"w-full",disabled:r,children:r?"Đang lưu...":"Cập nhật"})})]})})})]})})}const Pe=({className:t})=>{const n=A(),{stores:s,filters:o,updateFilters:r}=ve(),[c,d]=u.useState(!1),[m,x]=u.useState(null),[l,g]=u.useState(!1),p=h=>{r({search:h})},a=h=>{console.log("Store clicked:",h)},i=h=>{x(h),d(!0)},S=h=>{n({to:"/crm/general-setups/items",search:{pos:String(h.id)}})},M=h=>{d(h),h||x(null)},b=async()=>{if(m){g(!0);try{await new Promise(h=>setTimeout(h,1e3)),d(!1),x(null)}catch(h){console.error("Error saving store info:",h)}finally{g(!1)}}},T=ye({onStoreInfoClick:i,onMenuClick:S});return e.jsxs("div",{className:`container mx-auto p-6 ${t||""}`,children:[e.jsx("div",{className:"mb-6",children:e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Nhà hàng và thực đơn"})}),e.jsx("div",{className:"mb-4",children:e.jsx(Se,{value:o.search,onChange:p,placeholder:"Tên / ID nhà hàng"})}),e.jsx(be,{columns:T,data:s,onRowClick:a}),s.length>0&&e.jsxs("div",{className:"mt-4 text-sm text-muted-foreground text-center",children:["Tổng số ",s.length," nhà hàng"]}),e.jsx(De,{open:c,onOpenChange:M,store:m,onSave:b,isLoading:l})]})},ls=Pe;export{ls as component};
